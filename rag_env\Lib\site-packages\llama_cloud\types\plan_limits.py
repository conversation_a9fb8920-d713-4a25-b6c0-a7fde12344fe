# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PlanLimits(pydantic.BaseModel):
    allow_pay_as_you_go: bool = pydantic.Field(description="Whether usage is allowed after credit grants are exhausted")
    subscription_cost_usd: int
    max_monthly_invoice_total_usd: typing.Optional[int]
    spending_soft_alerts_usd_cents: typing.Optional[typing.List[int]]
    max_concurrent_parse_jobs_premium: typing.Optional[int]
    max_concurrent_parse_jobs_other: typing.Optional[int]
    max_extraction_agents: typing.Optional[int]
    max_extraction_runs: typing.Optional[int]
    max_extraction_jobs: typing.Optional[int]
    max_pages_per_index: typing.Optional[int]
    max_files_per_index: typing.Optional[int]
    max_indexes: typing.Optional[int]
    max_concurrent_index_jobs: typing.Optional[int]
    max_data_sources: typing.Optional[int]
    max_embedding_models: typing.Optional[int]
    max_data_sinks: typing.Optional[int]
    max_published_agents: typing.Optional[int]
    max_report_agent_sessions: typing.Optional[int]
    max_users: typing.Optional[int]
    max_organizations: typing.Optional[int]
    max_projects: typing.Optional[int]
    mfa_enabled: bool
    sso_enabled: bool

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
