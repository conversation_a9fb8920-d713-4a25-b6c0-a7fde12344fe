# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .element_segmentation_config import ElementSegmentationConfig
from .none_segmentation_config import NoneSegmentationConfig
from .page_segmentation_config import PageSegmentationConfig


class AdvancedModeTransformConfigSegmentationConfig_None(NoneSegmentationConfig):
    mode: typing_extensions.Literal["none"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigSegmentationConfig_Page(PageSegmentationConfig):
    mode: typing_extensions.Literal["page"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigSegmentationConfig_Element(ElementSegmentationConfig):
    mode: typing_extensions.Literal["element"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


AdvancedModeTransformConfigSegmentationConfig = typing.Union[
    AdvancedModeTransformConfigSegmentationConfig_None,
    AdvancedModeTransformConfigSegmentationConfig_Page,
    AdvancedModeTransformConfigSegmentationConfig_Element,
]
