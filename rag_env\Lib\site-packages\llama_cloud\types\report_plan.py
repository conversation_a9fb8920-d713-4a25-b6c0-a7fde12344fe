# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .report_plan_block import ReportPlanBlock

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ReportPlan(pydantic.BaseModel):
    id: typing.Optional[str] = pydantic.Field(description="The id of the report plan")
    blocks: typing.Optional[typing.List[ReportPlanBlock]] = pydantic.Field(description="The blocks of the report")
    generated_at: typing.Optional[dt.datetime] = pydantic.Field(
        description="The timestamp of when the plan was generated"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
