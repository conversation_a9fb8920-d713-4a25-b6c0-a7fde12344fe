# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .pg_vector_distance_method import PgVectorDistanceMethod
from .pg_vector_vector_type import PgVectorVectorType

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PgVectorHnswSettings(pydantic.BaseModel):
    """
    HNSW settings for PGVector.
    """

    ef_construction: typing.Optional[int] = pydantic.Field(
        description="The number of edges to use during the construction phase."
    )
    ef_search: typing.Optional[int] = pydantic.Field(description="The number of edges to use during the search phase.")
    m: typing.Optional[int] = pydantic.Field(
        description="The number of bi-directional links created for each new element."
    )
    vector_type: typing.Optional[PgVectorVectorType] = pydantic.Field(description="The type of vector to use.")
    distance_method: typing.Optional[PgVectorDistanceMethod] = pydantic.Field(description="The distance method to use.")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
