# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .progress_event import ProgressEvent
from .report_state_event import ReportStateEvent
from .report_update_event import ReportUpdateEvent


class ReportEventItemEventData_Progress(ProgressEvent):
    type: typing_extensions.Literal["progress"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class ReportEventItemEventData_ReportBlockUpdate(ReportUpdateEvent):
    type: typing_extensions.Literal["report_block_update"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class ReportEventItemEventData_ReportStateUpdate(ReportStateEvent):
    type: typing_extensions.Literal["report_state_update"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


ReportEventItemEventData = typing.Union[
    ReportEventItemEventData_Progress,
    ReportEventItemEventData_ReportBlockUpdate,
    ReportEventItemEventData_ReportStateUpdate,
]
