# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ReRankerType(str, enum.Enum):
    """
    Enum for the reranker type.
    """

    SYSTEM_DEFAULT = "system_default"
    LLM = "llm"
    COHERE = "cohere"
    BEDROCK = "bedrock"
    SCORE = "score"
    DISABLED = "disabled"

    def visit(
        self,
        system_default: typing.Callable[[], T_Result],
        llm: typing.Callable[[], T_Result],
        cohere: typing.Callable[[], T_Result],
        bedrock: typing.Callable[[], T_Result],
        score: typing.Callable[[], T_Result],
        disabled: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ReRankerType.SYSTEM_DEFAULT:
            return system_default()
        if self is ReRankerType.LLM:
            return llm()
        if self is ReRankerType.COHERE:
            return cohere()
        if self is ReRankerType.BEDROCK:
            return bedrock()
        if self is ReRankerType.SCORE:
            return score()
        if self is ReRankerType.DISABLED:
            return disabled()
