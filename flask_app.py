"""
Flask Web Interface for Medical Insurance RAG Agent
"""

from flask import Flask, render_template, request, jsonify, session
import time
import uuid
from pathlib import Path
from dotenv import load_dotenv
import os
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, Settings, StorageContext, load_index_from_storage
from llama_index.core.node_parser import Sen<PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.memory import Chat<PERSON>emoryBuffer
from llama_parse import LlamaParse
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

# Load environment variables
load_dotenv()

# Configure Flask app
app = Flask(__name__)
app.secret_key = os.urandom(24)

# Configure global settings
Settings.llm = OpenAI(model="gpt-4o-mini", temperature=0.1)
Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-small")

class FlaskInsuranceRAG:
    """Flask-based Insurance RAG Agent."""
    
    def __init__(self, storage_dir="./storage"):
        self.storage_dir = storage_dir
        self.index = None
        self.query_engine = None
        self.chat_engines = {}  # Store chat engines per session
        self.load_rag_system()
        
    def load_rag_system(self):
        """Load RAG system."""
        try:
            if Path(self.storage_dir).exists():
                # Load existing index
                storage_context = StorageContext.from_defaults(persist_dir=self.storage_dir)
                self.index = load_index_from_storage(storage_context)
                
                # Create query engine
                self.query_engine = self.index.as_query_engine(
                    similarity_top_k=8,
                    response_mode="compact"
                )
                
                return True
            else:
                return False
        except Exception as e:
            print(f"Error loading RAG system: {e}")
            return False
    
    def get_chat_engine(self, session_id):
        """Get or create chat engine for session."""
        if session_id not in self.chat_engines:
            memory = ChatMemoryBuffer.from_defaults(token_limit=3000)
            self.chat_engines[session_id] = SimpleChatEngine.from_defaults(
                llm=Settings.llm,
                memory=memory,
                system_prompt=(
                    "You are a helpful assistant specializing in medical insurance policies. "
                    "Answer questions about insurance coverage, benefits, exclusions, and procedures. "
                    "Be specific about coverage amounts, time limits, and any conditions."
                )
            )
        return self.chat_engines[session_id]
    
    def chat_query(self, message, session_id):
        """Process chat query."""
        if not self.query_engine:
            return "RAG system not loaded. Please check your setup.", 0
        
        try:
            start_time = time.time()
            
            # Get information using query engine for better retrieval
            query_response = self.query_engine.query(message)
            
            # Use chat engine with retrieved information
            chat_engine = self.get_chat_engine(session_id)
            enhanced_message = f"Based on the insurance policy information: {query_response}\n\nUser question: {message}"
            chat_response = chat_engine.chat(enhanced_message)
            
            response_time = time.time() - start_time
            return str(chat_response), response_time
            
        except Exception as e:
            return f"Error processing query: {e}", 0
    
    def query_with_sources(self, question):
        """Query with source information."""
        if not self.query_engine:
            return "RAG system not loaded. Please check your setup.", [], 0
        
        try:
            start_time = time.time()
            response = self.query_engine.query(question)
            response_time = time.time() - start_time
            
            sources = []
            if hasattr(response, 'source_nodes') and response.source_nodes:
                for i, node in enumerate(response.source_nodes[:3], 1):
                    sources.append({
                        'rank': i,
                        'score': getattr(node, 'score', 0),
                        'text': node.text[:200] + "..." if len(node.text) > 200 else node.text,
                        'metadata': node.metadata
                    })
            
            return str(response), sources, response_time
            
        except Exception as e:
            return f"Error processing query: {e}", [], 0
    
    def reset_chat(self, session_id):
        """Reset chat for session."""
        if session_id in self.chat_engines:
            del self.chat_engines[session_id]
        return True

# Initialize RAG agent
rag_agent = FlaskInsuranceRAG()

@app.route('/')
def index():
    """Main page."""
    # Initialize session
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())
    
    system_loaded = rag_agent.index is not None
    
    suggested_questions = [
        "What is my annual dental coverage limit?",
        "What vision benefits do I have?",
        "Are there any deductibles I need to pay?",
        "What are the main exclusions in my policy?",
        "How do I submit a claim?",
        "What happens to my benefits when I retire?",
        "What is covered for prescription drugs?",
        "Do I have travel insurance coverage?"
    ]
    
    return render_template('index.html', 
                         system_loaded=system_loaded,
                         suggested_questions=suggested_questions)

@app.route('/chat', methods=['POST'])
def chat():
    """Handle chat requests."""
    data = request.get_json()
    message = data.get('message', '')
    session_id = session.get('session_id')
    
    if not message:
        return jsonify({'error': 'No message provided'}), 400
    
    response, response_time = rag_agent.chat_query(message, session_id)
    
    return jsonify({
        'response': response,
        'response_time': round(response_time, 2)
    })

@app.route('/query', methods=['POST'])
def query():
    """Handle query with sources requests."""
    data = request.get_json()
    question = data.get('question', '')
    
    if not question:
        return jsonify({'error': 'No question provided'}), 400
    
    response, sources, response_time = rag_agent.query_with_sources(question)
    
    return jsonify({
        'response': response,
        'sources': sources,
        'response_time': round(response_time, 2)
    })

@app.route('/reset', methods=['POST'])
def reset():
    """Reset chat conversation."""
    session_id = session.get('session_id')
    rag_agent.reset_chat(session_id)
    return jsonify({'success': True})

@app.route('/status')
def status():
    """Get system status."""
    return jsonify({
        'system_loaded': rag_agent.index is not None,
        'storage_exists': Path(rag_agent.storage_dir).exists(),
        'session_id': session.get('session_id')
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
