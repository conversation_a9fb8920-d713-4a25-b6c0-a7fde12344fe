# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class BasePlanPlanFrequency(str, enum.Enum):
    MONTHLY = "MONTHLY"
    QUARTERLY = "QUARTERLY"
    ANNUAL = "ANNUAL"

    def visit(
        self,
        monthly: typing.Callable[[], T_Result],
        quarterly: typing.Callable[[], T_Result],
        annual: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is BasePlanPlanFrequency.MONTHLY:
            return monthly()
        if self is BasePlanPlanFrequency.QUARTERLY:
            return quarterly()
        if self is BasePlanPlanFrequency.ANNUAL:
            return annual()
