# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .report_event_item_event_data import ReportEventItemEventData

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ReportEventItem(pydantic.BaseModel):
    """
    From backend schema
    """

    id: str = pydantic.Field(description="The id of the event")
    report_id: str = pydantic.Field(description="The id of the report")
    event_type: str = pydantic.Field(description="The type of the event")
    event_data: ReportEventItemEventData = pydantic.Field(description="The data for the event")
    timestamp: dt.datetime = pydantic.Field(description="The timestamp for the event")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
