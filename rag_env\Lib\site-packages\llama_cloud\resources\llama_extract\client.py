# This file was auto-generated by <PERSON>rn from our API Definition.

import typing
import urllib.parse
from json.decoder import JSONDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...core.remove_none_from_dict import remove_none_from_dict
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.extract_agent import ExtractAgent
from ...types.extract_config import ExtractConfig
from ...types.extract_job import ExtractJob
from ...types.extract_job_create import ExtractJobCreate
from ...types.extract_resultset import ExtractResultset
from ...types.extract_run import ExtractRun
from ...types.extract_schema_generate_response import ExtractSchemaGenerateResponse
from ...types.extract_schema_validate_response import ExtractSchemaValidateResponse
from ...types.file_data import FileData
from ...types.http_validation_error import HttpValidationError
from ...types.llama_extract_settings import LlamaExtractSettings
from ...types.paginated_extract_runs_response import PaginatedExtractRunsResponse
from ...types.webhook_configuration import WebhookConfiguration
from .types.extract_agent_create_data_schema import ExtractAgentCreateDataSchema
from .types.extract_agent_update_data_schema import ExtractAgentUpdateDataSchema
from .types.extract_job_create_batch_data_schema_override import ExtractJobCreateBatchDataSchemaOverride
from .types.extract_schema_validate_request_data_schema import ExtractSchemaValidateRequestDataSchema
from .types.extract_stateless_request_data_schema import ExtractStatelessRequestDataSchema

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class LlamaExtractClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list_extraction_agents(
        self, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.List[ExtractAgent]:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.list_extraction_agents()
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractAgent], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_extraction_agent(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: str,
        data_schema: ExtractAgentCreateDataSchema,
        config: ExtractConfig,
    ) -> ExtractAgent:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: str. The name of the extraction schema

            - data_schema: ExtractAgentCreateDataSchema. The schema of the data.

            - config: ExtractConfig. The configuration parameters for the extraction agent.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.create_extraction_agent(
            name="string",
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder({"name": name, "data_schema": data_schema, "config": config}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def validate_extraction_schema(
        self, *, data_schema: ExtractSchemaValidateRequestDataSchema
    ) -> ExtractSchemaValidateResponse:
        """
        Validates an extraction agent's schema definition.
        Returns the normalized and validated schema if valid, otherwise raises an HTTP 400.

        Parameters:
            - data_schema: ExtractSchemaValidateRequestDataSchema.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.validate_extraction_schema()
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents/schema/validation"
            ),
            json=jsonable_encoder({"data_schema": data_schema}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractSchemaValidateResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def generate_extraction_schema(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        prompt: typing.Optional[str] = OMIT,
        file_id: typing.Optional[str] = OMIT,
    ) -> ExtractSchemaGenerateResponse:
        """
        Generates an extraction agent's schema definition from a file and/or natural language prompt.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - prompt: typing.Optional[str].

            - file_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.generate_extraction_schema()
        """
        _request: typing.Dict[str, typing.Any] = {}
        if prompt is not OMIT:
            _request["prompt"] = prompt
        if file_id is not OMIT:
            _request["file_id"] = file_id
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents/schema/generate"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractSchemaGenerateResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_extraction_agent_by_name(
        self, name: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractAgent:
        """
        Parameters:
            - name: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_extraction_agent_by_name(
            name="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/by-name/{name}"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_extraction_agent(self, extraction_agent_id: str) -> ExtractAgent:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_extraction_agent(
            extraction_agent_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_extraction_agent(
        self, extraction_agent_id: str, *, data_schema: ExtractAgentUpdateDataSchema, config: ExtractConfig
    ) -> ExtractAgent:
        """
        Parameters:
            - extraction_agent_id: str.

            - data_schema: ExtractAgentUpdateDataSchema. The schema of the data

            - config: ExtractConfig. The configuration parameters for the extraction agent.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.update_extraction_agent(
            extraction_agent_id="string",
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            json=jsonable_encoder({"data_schema": data_schema, "config": config}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_extraction_agent(self, extraction_agent_id: str) -> typing.Any:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.delete_extraction_agent(
            extraction_agent_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_jobs(self, *, extraction_agent_id: str) -> typing.List[ExtractJob]:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.list_jobs(
            extraction_agent_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractJob], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def run_job(self, *, from_ui: typing.Optional[bool] = None, request: ExtractJobCreate) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - request: ExtractJobCreate.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractJobCreate,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.run_job(
            request=ExtractJobCreate(
                extraction_agent_id="string",
                file_id="string",
                config_override=ExtractConfig(
                    priority=ExtractConfigPriority.LOW,
                    extraction_target=ExtractTarget.PER_DOC,
                    extraction_mode=ExtractMode.FAST,
                    chunk_mode=DocumentChunkMode.PAGE,
                ),
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_job(self, job_id: str) -> ExtractJob:
        """
        Parameters:
            - job_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_job(
            job_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/jobs/{job_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def run_job_test_user(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        job_create: ExtractJobCreate,
        extract_settings: typing.Optional[LlamaExtractSettings] = OMIT,
    ) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - job_create: ExtractJobCreate.

            - extract_settings: typing.Optional[LlamaExtractSettings].
        ---
        from llama_cloud import (
            ChunkMode,
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractJobCreate,
            ExtractMode,
            ExtractTarget,
            FailPageMode,
            LlamaExtractSettings,
            LlamaParseParameters,
            LlamaParseParametersPriority,
            MultimodalParseResolution,
            ParsingMode,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.run_job_test_user(
            job_create=ExtractJobCreate(
                extraction_agent_id="string",
                file_id="string",
                config_override=ExtractConfig(
                    priority=ExtractConfigPriority.LOW,
                    extraction_target=ExtractTarget.PER_DOC,
                    extraction_mode=ExtractMode.FAST,
                    chunk_mode=DocumentChunkMode.PAGE,
                ),
            ),
            extract_settings=LlamaExtractSettings(
                chunk_mode=ChunkMode.PAGE,
                llama_parse_params=LlamaParseParameters(
                    priority=LlamaParseParametersPriority.LOW,
                    parse_mode=ParsingMode.PARSE_PAGE_WITHOUT_LLM,
                    replace_failed_page_mode=FailPageMode.RAW_TEXT,
                ),
                multimodal_parse_resolution=MultimodalParseResolution.MEDIUM,
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"job_create": job_create}
        if extract_settings is not OMIT:
            _request["extract_settings"] = extract_settings
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/test"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def run_job_on_file(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        extraction_agent_id: str,
        file: typing.IO,
        data_schema_override: typing.Optional[str] = None,
        config_override: typing.Optional[str] = None,
    ) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - extraction_agent_id: str.

            - file: typing.IO.

            - data_schema_override: typing.Optional[str].

            - config_override: typing.Optional[str].
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/file"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            data=jsonable_encoder(
                {
                    "extraction_agent_id": extraction_agent_id,
                    "data_schema_override": data_schema_override,
                    "config_override": config_override,
                }
            ),
            files={"file": file},
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def run_batch_jobs(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        extraction_agent_id: str,
        file_ids: typing.List[str],
        data_schema_override: typing.Optional[ExtractJobCreateBatchDataSchemaOverride] = OMIT,
        config_override: typing.Optional[ExtractConfig] = OMIT,
    ) -> typing.List[ExtractJob]:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - extraction_agent_id: str. The id of the extraction agent

            - file_ids: typing.List[str]. The ids of the files

            - data_schema_override: typing.Optional[ExtractJobCreateBatchDataSchemaOverride]. The data schema to override the extraction agent's data schema with

            - config_override: typing.Optional[ExtractConfig].
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.run_batch_jobs(
            extraction_agent_id="string",
            file_ids=[],
            config_override=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"extraction_agent_id": extraction_agent_id, "file_ids": file_ids}
        if data_schema_override is not OMIT:
            _request["data_schema_override"] = data_schema_override
        if config_override is not OMIT:
            _request["config_override"] = config_override
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/batch"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractJob], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_job_result(
        self, job_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractResultset:
        """
        Parameters:
            - job_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_job_result(
            job_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/jobs/{job_id}/result"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractResultset, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_extract_runs(
        self, *, extraction_agent_id: str, skip: typing.Optional[int] = None, limit: typing.Optional[int] = None
    ) -> PaginatedExtractRunsResponse:
        """
        Parameters:
            - extraction_agent_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.list_extract_runs(
            extraction_agent_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/runs"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id, "skip": skip, "limit": limit}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedExtractRunsResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_latest_run_from_ui(self, *, extraction_agent_id: str) -> typing.Optional[ExtractRun]:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_latest_run_from_ui(
            extraction_agent_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/runs/latest-from-ui"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Optional[ExtractRun], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_run_by_job_id(
        self, job_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractRun:
        """
        Parameters:
            - job_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_run_by_job_id(
            job_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/by-job/{job_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractRun, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_run(
        self, run_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractRun:
        """
        Parameters:
            - run_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.get_run(
            run_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/{run_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractRun, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_extraction_run(
        self, run_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.Any:
        """
        Parameters:
            - run_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.delete_extraction_run(
            run_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/{run_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def extract_stateless(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        webhook_configurations: typing.Optional[typing.List[WebhookConfiguration]] = OMIT,
        data_schema: ExtractStatelessRequestDataSchema,
        config: ExtractConfig,
        file_id: typing.Optional[str] = OMIT,
        text: typing.Optional[str] = OMIT,
        file: typing.Optional[FileData] = OMIT,
    ) -> ExtractJob:
        """
        Stateless extraction endpoint that uses a default extraction agent in the user's default project.
        Requires data_schema, config, and either file_id, text, or base64 encoded file data.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - webhook_configurations: typing.Optional[typing.List[WebhookConfiguration]].

            - data_schema: ExtractStatelessRequestDataSchema. The schema of the data to extract

            - config: ExtractConfig. The configuration parameters for the extraction

            - file_id: typing.Optional[str].

            - text: typing.Optional[str].

            - file: typing.Optional[FileData].
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
            FileData,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.llama_extract.extract_stateless(
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
            file=FileData(
                data="string",
                mime_type="string",
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"data_schema": data_schema, "config": config}
        if webhook_configurations is not OMIT:
            _request["webhook_configurations"] = webhook_configurations
        if file_id is not OMIT:
            _request["file_id"] = file_id
        if text is not OMIT:
            _request["text"] = text
        if file is not OMIT:
            _request["file"] = file
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/run"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncLlamaExtractClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list_extraction_agents(
        self, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.List[ExtractAgent]:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.list_extraction_agents()
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractAgent], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_extraction_agent(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: str,
        data_schema: ExtractAgentCreateDataSchema,
        config: ExtractConfig,
    ) -> ExtractAgent:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: str. The name of the extraction schema

            - data_schema: ExtractAgentCreateDataSchema. The schema of the data.

            - config: ExtractConfig. The configuration parameters for the extraction agent.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.create_extraction_agent(
            name="string",
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder({"name": name, "data_schema": data_schema, "config": config}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def validate_extraction_schema(
        self, *, data_schema: ExtractSchemaValidateRequestDataSchema
    ) -> ExtractSchemaValidateResponse:
        """
        Validates an extraction agent's schema definition.
        Returns the normalized and validated schema if valid, otherwise raises an HTTP 400.

        Parameters:
            - data_schema: ExtractSchemaValidateRequestDataSchema.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.validate_extraction_schema()
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents/schema/validation"
            ),
            json=jsonable_encoder({"data_schema": data_schema}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractSchemaValidateResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def generate_extraction_schema(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        prompt: typing.Optional[str] = OMIT,
        file_id: typing.Optional[str] = OMIT,
    ) -> ExtractSchemaGenerateResponse:
        """
        Generates an extraction agent's schema definition from a file and/or natural language prompt.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - prompt: typing.Optional[str].

            - file_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.generate_extraction_schema()
        """
        _request: typing.Dict[str, typing.Any] = {}
        if prompt is not OMIT:
            _request["prompt"] = prompt
        if file_id is not OMIT:
            _request["file_id"] = file_id
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/extraction-agents/schema/generate"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractSchemaGenerateResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_extraction_agent_by_name(
        self, name: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractAgent:
        """
        Parameters:
            - name: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_extraction_agent_by_name(
            name="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/by-name/{name}"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_extraction_agent(self, extraction_agent_id: str) -> ExtractAgent:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_extraction_agent(
            extraction_agent_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_extraction_agent(
        self, extraction_agent_id: str, *, data_schema: ExtractAgentUpdateDataSchema, config: ExtractConfig
    ) -> ExtractAgent:
        """
        Parameters:
            - extraction_agent_id: str.

            - data_schema: ExtractAgentUpdateDataSchema. The schema of the data

            - config: ExtractConfig. The configuration parameters for the extraction agent.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.update_extraction_agent(
            extraction_agent_id="string",
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            json=jsonable_encoder({"data_schema": data_schema, "config": config}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractAgent, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_extraction_agent(self, extraction_agent_id: str) -> typing.Any:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.delete_extraction_agent(
            extraction_agent_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/extraction-agents/{extraction_agent_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_jobs(self, *, extraction_agent_id: str) -> typing.List[ExtractJob]:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.list_jobs(
            extraction_agent_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractJob], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def run_job(self, *, from_ui: typing.Optional[bool] = None, request: ExtractJobCreate) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - request: ExtractJobCreate.
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractJobCreate,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.run_job(
            request=ExtractJobCreate(
                extraction_agent_id="string",
                file_id="string",
                config_override=ExtractConfig(
                    priority=ExtractConfigPriority.LOW,
                    extraction_target=ExtractTarget.PER_DOC,
                    extraction_mode=ExtractMode.FAST,
                    chunk_mode=DocumentChunkMode.PAGE,
                ),
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_job(self, job_id: str) -> ExtractJob:
        """
        Parameters:
            - job_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_job(
            job_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/jobs/{job_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def run_job_test_user(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        job_create: ExtractJobCreate,
        extract_settings: typing.Optional[LlamaExtractSettings] = OMIT,
    ) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - job_create: ExtractJobCreate.

            - extract_settings: typing.Optional[LlamaExtractSettings].
        ---
        from llama_cloud import (
            ChunkMode,
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractJobCreate,
            ExtractMode,
            ExtractTarget,
            FailPageMode,
            LlamaExtractSettings,
            LlamaParseParameters,
            LlamaParseParametersPriority,
            MultimodalParseResolution,
            ParsingMode,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.run_job_test_user(
            job_create=ExtractJobCreate(
                extraction_agent_id="string",
                file_id="string",
                config_override=ExtractConfig(
                    priority=ExtractConfigPriority.LOW,
                    extraction_target=ExtractTarget.PER_DOC,
                    extraction_mode=ExtractMode.FAST,
                    chunk_mode=DocumentChunkMode.PAGE,
                ),
            ),
            extract_settings=LlamaExtractSettings(
                chunk_mode=ChunkMode.PAGE,
                llama_parse_params=LlamaParseParameters(
                    priority=LlamaParseParametersPriority.LOW,
                    parse_mode=ParsingMode.PARSE_PAGE_WITHOUT_LLM,
                    replace_failed_page_mode=FailPageMode.RAW_TEXT,
                ),
                multimodal_parse_resolution=MultimodalParseResolution.MEDIUM,
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"job_create": job_create}
        if extract_settings is not OMIT:
            _request["extract_settings"] = extract_settings
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/test"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def run_job_on_file(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        extraction_agent_id: str,
        file: typing.IO,
        data_schema_override: typing.Optional[str] = None,
        config_override: typing.Optional[str] = None,
    ) -> ExtractJob:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - extraction_agent_id: str.

            - file: typing.IO.

            - data_schema_override: typing.Optional[str].

            - config_override: typing.Optional[str].
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/file"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            data=jsonable_encoder(
                {
                    "extraction_agent_id": extraction_agent_id,
                    "data_schema_override": data_schema_override,
                    "config_override": config_override,
                }
            ),
            files={"file": file},
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def run_batch_jobs(
        self,
        *,
        from_ui: typing.Optional[bool] = None,
        extraction_agent_id: str,
        file_ids: typing.List[str],
        data_schema_override: typing.Optional[ExtractJobCreateBatchDataSchemaOverride] = OMIT,
        config_override: typing.Optional[ExtractConfig] = OMIT,
    ) -> typing.List[ExtractJob]:
        """
        Parameters:
            - from_ui: typing.Optional[bool].

            - extraction_agent_id: str. The id of the extraction agent

            - file_ids: typing.List[str]. The ids of the files

            - data_schema_override: typing.Optional[ExtractJobCreateBatchDataSchemaOverride]. The data schema to override the extraction agent's data schema with

            - config_override: typing.Optional[ExtractConfig].
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.run_batch_jobs(
            extraction_agent_id="string",
            file_ids=[],
            config_override=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"extraction_agent_id": extraction_agent_id, "file_ids": file_ids}
        if data_schema_override is not OMIT:
            _request["data_schema_override"] = data_schema_override
        if config_override is not OMIT:
            _request["config_override"] = config_override
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/jobs/batch"),
            params=remove_none_from_dict({"from_ui": from_ui}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ExtractJob], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_job_result(
        self, job_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractResultset:
        """
        Parameters:
            - job_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_job_result(
            job_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/jobs/{job_id}/result"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractResultset, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_extract_runs(
        self, *, extraction_agent_id: str, skip: typing.Optional[int] = None, limit: typing.Optional[int] = None
    ) -> PaginatedExtractRunsResponse:
        """
        Parameters:
            - extraction_agent_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.list_extract_runs(
            extraction_agent_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/runs"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id, "skip": skip, "limit": limit}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedExtractRunsResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_latest_run_from_ui(self, *, extraction_agent_id: str) -> typing.Optional[ExtractRun]:
        """
        Parameters:
            - extraction_agent_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_latest_run_from_ui(
            extraction_agent_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/runs/latest-from-ui"),
            params=remove_none_from_dict({"extraction_agent_id": extraction_agent_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Optional[ExtractRun], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_run_by_job_id(
        self, job_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractRun:
        """
        Parameters:
            - job_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_run_by_job_id(
            job_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/by-job/{job_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractRun, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_run(
        self, run_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> ExtractRun:
        """
        Parameters:
            - run_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.get_run(
            run_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/{run_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractRun, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_extraction_run(
        self, run_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.Any:
        """
        Parameters:
            - run_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.delete_extraction_run(
            run_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/extraction/runs/{run_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def extract_stateless(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        webhook_configurations: typing.Optional[typing.List[WebhookConfiguration]] = OMIT,
        data_schema: ExtractStatelessRequestDataSchema,
        config: ExtractConfig,
        file_id: typing.Optional[str] = OMIT,
        text: typing.Optional[str] = OMIT,
        file: typing.Optional[FileData] = OMIT,
    ) -> ExtractJob:
        """
        Stateless extraction endpoint that uses a default extraction agent in the user's default project.
        Requires data_schema, config, and either file_id, text, or base64 encoded file data.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - webhook_configurations: typing.Optional[typing.List[WebhookConfiguration]].

            - data_schema: ExtractStatelessRequestDataSchema. The schema of the data to extract

            - config: ExtractConfig. The configuration parameters for the extraction

            - file_id: typing.Optional[str].

            - text: typing.Optional[str].

            - file: typing.Optional[FileData].
        ---
        from llama_cloud import (
            DocumentChunkMode,
            ExtractConfig,
            ExtractConfigPriority,
            ExtractMode,
            ExtractTarget,
            FileData,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.llama_extract.extract_stateless(
            config=ExtractConfig(
                priority=ExtractConfigPriority.LOW,
                extraction_target=ExtractTarget.PER_DOC,
                extraction_mode=ExtractMode.FAST,
                chunk_mode=DocumentChunkMode.PAGE,
            ),
            file=FileData(
                data="string",
                mime_type="string",
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {"data_schema": data_schema, "config": config}
        if webhook_configurations is not OMIT:
            _request["webhook_configurations"] = webhook_configurations
        if file_id is not OMIT:
            _request["file_id"] = file_id
        if text is not OMIT:
            _request["text"] = text
        if file is not OMIT:
            _request["file"] = file
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/extraction/run"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ExtractJob, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
