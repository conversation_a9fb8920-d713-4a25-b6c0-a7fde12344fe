const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./data-grid-overlay-editor.DRTHOydk.js","./index.CD8HuT3N.js","../css/index.C8X8rNzw.css","./withFullScreenWrapper.DLp1ENGm.js","./Toolbar.DSnK1fUh.js","./FormClearHelper.DHh1GFzm.js","./sprintf.D7DtBTRn.js","./checkbox.D8W881TL.js","./createDownloadLinkElement.ZaXNnPK4.js","./toConsumableArray.D-Dx88BQ.js","./possibleConstructorReturn.DgiPnZ9N.js","./createSuper.B6W-Dh9S.js","./FileDownload.esm.DgBvV6Pq.js","./number-overlay-editor.DaRFzZEO.js","./es6.B8zRNPZ-.js"])))=>i.map(i=>d[i]);
import{r as f,E as Ws,_ as Ae,c7 as Dh,a5 as vr,bF as Ya,b0 as wo,b1 as Oh,c8 as Ph,c9 as Lh,ca as $c,cb as Nc,am as $t,cc as Fh,cd as Bc,ce as _h,cf as Ah,cg as Hh,ch as zh,ci as Vh,cj as $h,ab as Or,bd as Us,a4 as Nh,ck as Bh,m as ze,b3 as Wc,o as lt,cl as no,bs as Gr,cm as Wh,c4 as ja,j as He,cn as Uh,co as qh,v as nr,aA as rn,aP as Gh,cp as Bl,cq as Xh,cr as Yh,cs as jh,ct as Kh,cu as Uc,cv as Zh,cw as Jh,cx as Es,cy as qc,cz as Gc,cA as Qh,cB as eg,cC as tg,cD as ng,cE as rg,cF as ig,cG as og,cH as ag,cI as sg,cJ as Xc,cK as lg,cL as ug,cM as cg,cN as dg,cO as fg,aN as hg,aO as gg,cP as pg,cQ as mg,aM as vg,cR as bg,cS as wg,cT as yg,aL as Cg,u as Kr,cU as on,x as qs,cV as Sg,bM as ro,cW as ur,cX as Fe,a_ as xg,aa as Yc,cY as jc,a1 as kg,cZ as Mg,t as mi,c_ as Rg,c$ as Ig,aR as Ka,F as Kc,bK as xa,d0 as Zc,h as ka,e as vn,y as Jn,bI as Ma,d1 as Eg,bL as Jc,d2 as Tg,S as Dg,J as Wl,d3 as Og,d4 as Pg,L as Lg,d5 as Fg,d6 as Ul}from"./index.CD8HuT3N.js";import{w as _g,E as Ag}from"./withFullScreenWrapper.DLp1ENGm.js";import{T as Hg,a as gi}from"./Toolbar.DSnK1fUh.js";import{u as zg}from"./FormClearHelper.DHh1GFzm.js";import{s as Vg}from"./sprintf.D7DtBTRn.js";import{L as $g,a as Ng,S as Bg}from"./checkbox.D8W881TL.js";import{c as Wg}from"./createDownloadLinkElement.ZaXNnPK4.js";import{_ as gr,a as xi,C as Ug}from"./toConsumableArray.D-Dx88BQ.js";import{d as qg,f as Gg,e as Xg}from"./possibleConstructorReturn.DgiPnZ9N.js";import{_ as Yg}from"./createSuper.B6W-Dh9S.js";import{D as jg,F as Kg}from"./FileDownload.esm.DgBvV6Pq.js";var Qc=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ws,Ae({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))});Qc.displayName="Add";var ed=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ws,Ae({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))});ed.displayName="Search";var td=f.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return f.createElement(Ws,Ae({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),f.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),f.createElement("path",{d:"M12 6a9.77 9.77 0 018.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0112 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 010 5 2.5 2.5 0 010-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}))});td.displayName="Visibility";var Za,ql;function Zg(){if(ql)return Za;ql=1;var e=Object.prototype,t=e.hasOwnProperty;function n(r,i){return r!=null&&t.call(r,i)}return Za=n,Za}var Ja,Gl;function Jg(){if(Gl)return Ja;Gl=1;var e=Zg(),t=Dh();function n(r,i){return r!=null&&t(r,i,e)}return Ja=n,Ja}var Qg=Jg();const ep=vr(Qg);function nd(e="This should not happen"){throw new Error(e)}function zn(e,t="Assertion failed"){if(!e)return nd(t)}function co(e,t){return nd(t??"Hell froze over")}function tp(e,t){try{return e()}catch{return t}}const Xl=Object.prototype.hasOwnProperty;function Mi(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&Mi(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Xl.call(e,n)&&++r&&!Xl.call(t,n)||!(n in t)||!Mi(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}const sa=null,Gs=void 0;var Z;(function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"})(Z||(Z={}));var Yl;(function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"})(Yl||(Yl={}));var la;(function(e){e.Triangle="triangle",e.Dots="dots"})(la||(la={}));function $o(e){return"width"in e&&typeof e.width=="number"}async function jl(e){return typeof e=="object"?e:await e()}function vi(e){return!(e.kind===Z.Loading||e.kind===Z.Bubble||e.kind===Z.RowID||e.kind===Z.Protected||e.kind===Z.Drilldown)}function yi(e){return e.kind===Xn.Marker||e.kind===Xn.NewRow}function bi(e){if(!vi(e)||e.kind===Z.Image)return!1;switch(e.kind){case Z.Text:case Z.Number:case Z.Markdown:case Z.Uri:case Z.Custom:case Z.Boolean:return e.readonly!==!0;default:co(e,"A cell was passed with an invalid kind")}}function np(e){return ep(e,"editor")}function Xs(e){return!(e.readonly??!1)}var Xn;(function(e){e.NewRow="new-row",e.Marker="marker"})(Xn||(Xn={}));const ua={shape:"square",size:4,offsetX:-2,offsetY:-2,outline:0};function Qa(e){if(e.length===0)return[];const t=[...e],n=[];t.sort(function(r,i){return r[0]-i[0]}),n.push([...t[0]]);for(const r of t.slice(1)){const i=n[n.length-1];i[1]<r[0]?n.push([...r]):i[1]<r[1]&&(i[1]=r[1])}return n}let Kl;class Qe{items;constructor(t){this.items=t}static create=t=>new Qe(Qa(t));static empty=()=>Kl??(Kl=new Qe([]));static fromSingleSelection=t=>Qe.empty().add(t);static fromArray=t=>{if(t.length===0)return Qe.empty();const n=t.map(i=>[i,i+1]),r=Qa(n);return new Qe(r)};offset(t){if(t===0)return this;const n=this.items.map(r=>[r[0]+t,r[1]+t]);return new Qe(n)}add(t){const n=typeof t=="number"?[t,t+1]:t,r=Qa([...this.items,n]);return new Qe(r)}remove(t){const n=[...this.items],r=typeof t=="number"?t:t[0],i=typeof t=="number"?t+1:t[1];for(const[o,s]of n.entries()){const[a,l]=s;if(a<=i&&r<=l){const u=[];a<r&&u.push([a,r]),i<l&&u.push([i,l]),n.splice(o,1,...u)}}return new Qe(n)}first(){if(this.items.length!==0)return this.items[0][0]}last(){if(this.items.length!==0)return this.items.slice(-1)[0][1]-1}hasIndex(t){for(let n=0;n<this.items.length;n++){const[r,i]=this.items[n];if(t>=r&&t<i)return!0}return!1}hasAll(t){for(let n=t[0];n<t[1];n++)if(!this.hasIndex(n))return!1;return!0}some(t){for(const n of this)if(t(n))return!0;return!1}equals(t){if(t===this)return!0;if(t.items.length!==this.items.length)return!1;for(let n=0;n<this.items.length;n++){const r=t.items[n],i=this.items[n];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0}toArray(){const t=[];for(const[n,r]of this.items)for(let i=n;i<r;i++)t.push(i);return t}get length(){let t=0;for(const[n,r]of this.items)t+=r-n;return t}*[Symbol.iterator](){for(const[t,n]of this.items)for(let r=t;r<n;r++)yield r}}var rp=function(){const t=Array.prototype.slice.call(arguments).filter(Boolean),n={},r=[];t.forEach(o=>{(o?o.split(" "):[]).forEach(a=>{if(a.startsWith("atm_")){const[,l]=a.split("_");n[l]=a}else r.push(a)})});const i=[];for(const o in n)Object.prototype.hasOwnProperty.call(n,o)&&i.push(n[o]);return i.push(...r),i.join(" ")},Zl=rp,ip=e=>e.toUpperCase()===e,op=e=>t=>e.indexOf(t)===-1,rd=(e,t)=>{const n={};return Object.keys(e).filter(op(t)).forEach(r=>{n[r]=e[r]}),n};function ap(e,t,n){const r=rd(t,n);if(!e){const i=typeof Ya=="function"?{default:Ya}:Ya;Object.keys(r).forEach(o=>{i.default(o)||delete r[o]})}return r}var sp=(e,t)=>{};function lp(e){let t="";return n=>{const r=(o,s)=>{const{as:a=e,class:l=t}=o,u=n.propsAsIs===void 0?!(typeof a=="string"&&a.indexOf("-")===-1&&!ip(a[0])):n.propsAsIs,c=ap(u,o,["as","class"]);c.ref=s,c.className=n.atomic?Zl(n.class,c.className||l):Zl(c.className||l,n.class);const{vars:d}=n;if(d){const g={};for(const m in d){const y=d[m],b=y[0],v=y[1]||"",C=typeof b=="function"?b(o):b;sp(C,n.name),g[`--${m}`]=`${C}${v}`}const h=c.style||{},p=Object.keys(h);p.length>0&&p.forEach(m=>{g[m]=h[m]}),c.style=g}return e.__wyw_meta&&e!==a?(c.as=a,f.createElement(e,c)):f.createElement(a,c)},i=f.forwardRef?f.forwardRef(r):o=>{const s=rd(o,["innerRef"]);return r(s,o.innerRef)};return i.displayName=n.name,i.__wyw_meta={className:n.class||t,extends:e},i}}var sn=lp;const up=sn("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var es={},Ui={},No={},Bo={},Jl;function cp(){return Jl||(Jl=1,function(e){(function(t,n){n(e,wo(),Oh())})(Bo,function(t,n,r){Object.defineProperty(t,"__esModule",{value:!0}),t.setHasSupportToCaptureOption=p;var i=s(n),o=s(r);function s(v){return v&&v.__esModule?v:{default:v}}var a=Object.assign||function(v){for(var C=1;C<arguments.length;C++){var E=arguments[C];for(var I in E)Object.prototype.hasOwnProperty.call(E,I)&&(v[I]=E[I])}return v};function l(v,C){var E={};for(var I in v)C.indexOf(I)>=0||Object.prototype.hasOwnProperty.call(v,I)&&(E[I]=v[I]);return E}function u(v,C){if(!(v instanceof C))throw new TypeError("Cannot call a class as a function")}var c=function(){function v(C,E){for(var I=0;I<E.length;I++){var R=E[I];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(C,R.key,R)}}return function(C,E,I){return E&&v(C.prototype,E),I&&v(C,I),C}}();function d(v,C){if(!v)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return C&&(typeof C=="object"||typeof C=="function")?C:v}function g(v,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof C);v.prototype=Object.create(C&&C.prototype,{constructor:{value:v,enumerable:!1,writable:!0,configurable:!0}}),C&&(Object.setPrototypeOf?Object.setPrototypeOf(v,C):v.__proto__=C)}var h=!1;function p(v){h=v}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){p(!0)}}))}catch{}function m(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{capture:!0};return h?v:v.capture}function y(v){if("touches"in v){var C=v.touches[0],E=C.pageX,I=C.pageY;return{x:E,y:I}}var R=v.screenX,P=v.screenY;return{x:R,y:P}}var b=function(v){g(C,v);function C(){var E;u(this,C);for(var I=arguments.length,R=Array(I),P=0;P<I;P++)R[P]=arguments[P];var x=d(this,(E=C.__proto__||Object.getPrototypeOf(C)).call.apply(E,[this].concat(R)));return x._handleSwipeStart=x._handleSwipeStart.bind(x),x._handleSwipeMove=x._handleSwipeMove.bind(x),x._handleSwipeEnd=x._handleSwipeEnd.bind(x),x._onMouseDown=x._onMouseDown.bind(x),x._onMouseMove=x._onMouseMove.bind(x),x._onMouseUp=x._onMouseUp.bind(x),x._setSwiperRef=x._setSwiperRef.bind(x),x}return c(C,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,m({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,m({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(I){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(I))}},{key:"_onMouseMove",value:function(I){this.mouseDown&&this._handleSwipeMove(I)}},{key:"_onMouseUp",value:function(I){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(I)}},{key:"_handleSwipeStart",value:function(I){var R=y(I),P=R.x,x=R.y;this.moveStart={x:P,y:x},this.props.onSwipeStart(I)}},{key:"_handleSwipeMove",value:function(I){if(this.moveStart){var R=y(I),P=R.x,x=R.y,S=P-this.moveStart.x,F=x-this.moveStart.y;this.moving=!0;var D=this.props.onSwipeMove({x:S,y:F},I);D&&I.cancelable&&I.preventDefault(),this.movePosition={deltaX:S,deltaY:F}}}},{key:"_handleSwipeEnd",value:function(I){this.props.onSwipeEnd(I);var R=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-R?this.props.onSwipeLeft(1,I):this.movePosition.deltaX>R&&this.props.onSwipeRight(1,I),this.movePosition.deltaY<-R?this.props.onSwipeUp(1,I):this.movePosition.deltaY>R&&this.props.onSwipeDown(1,I)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(I){this.swiper=I,this.props.innerRef(I)}},{key:"render",value:function(){var I=this.props;I.tagName;var R=I.className,P=I.style,x=I.children;I.allowMouseEvents,I.onSwipeUp,I.onSwipeDown,I.onSwipeLeft,I.onSwipeRight,I.onSwipeStart,I.onSwipeMove,I.onSwipeEnd,I.innerRef,I.tolerance;var S=l(I,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]);return i.default.createElement(this.props.tagName,a({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:R,style:P},S),x)}}]),C}(n.Component);b.displayName="ReactSwipe",b.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},b.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},t.default=b})}(Bo)),Bo}var Ql;function id(){return Ql||(Ql=1,function(e){(function(t,n){n(e,cp())})(No,function(t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}t.default=r.default})}(No)),No}var qi={},eu;function od(){if(eu)return qi;eu=1,Object.defineProperty(qi,"__esModule",{value:!0}),qi.default=void 0;var e=t(Ph());function t(i){return i&&i.__esModule?i:{default:i}}function n(i,o,s){return o in i?Object.defineProperty(i,o,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[o]=s,i}var r={ROOT:function(o){return(0,e.default)(n({"carousel-root":!0},o||"",!!o))},CAROUSEL:function(o){return(0,e.default)({carousel:!0,"carousel-slider":o})},WRAPPER:function(o,s){return(0,e.default)({"thumbs-wrapper":!o,"slider-wrapper":o,"axis-horizontal":s==="horizontal","axis-vertical":s!=="horizontal"})},SLIDER:function(o,s){return(0,e.default)({thumbs:!o,slider:o,animated:!s})},ITEM:function(o,s,a){return(0,e.default)({thumb:!o,slide:o,selected:s,previous:a})},ARROW_PREV:function(o){return(0,e.default)({"control-arrow control-prev":!0,"control-disabled":o})},ARROW_NEXT:function(o){return(0,e.default)({"control-arrow control-next":!0,"control-disabled":o})},DOT:function(o){return(0,e.default)({dot:!0,selected:o})}};return qi.default=r,qi}var Gi={},Xi={},tu;function dp(){if(tu)return Xi;tu=1,Object.defineProperty(Xi,"__esModule",{value:!0}),Xi.outerWidth=void 0;var e=function(n){var r=n.offsetWidth,i=getComputedStyle(n);return r+=parseInt(i.marginLeft)+parseInt(i.marginRight),r};return Xi.outerWidth=e,Xi}var Yi={},nu;function Ys(){if(nu)return Yi;nu=1,Object.defineProperty(Yi,"__esModule",{value:!0}),Yi.default=void 0;var e=function(n,r,i){var o=n===0?n:n+r,s=i==="horizontal"?[o,0,0]:[0,o,0],a="translate3d",l="("+s.join(",")+")";return a+l};return Yi.default=e,Yi}var ji={},ru;function ad(){if(ru)return ji;ru=1,Object.defineProperty(ji,"__esModule",{value:!0}),ji.default=void 0;var e=function(){return window};return ji.default=e,ji}var iu;function sd(){if(iu)return Gi;iu=1,Object.defineProperty(Gi,"__esModule",{value:!0}),Gi.default=void 0;var e=l(wo()),t=s(od()),n=dp(),r=s(Ys()),i=s(id()),o=s(ad());function s(x){return x&&x.__esModule?x:{default:x}}function a(){if(typeof WeakMap!="function")return null;var x=new WeakMap;return a=function(){return x},x}function l(x){if(x&&x.__esModule)return x;if(x===null||u(x)!=="object"&&typeof x!="function")return{default:x};var S=a();if(S&&S.has(x))return S.get(x);var F={},D=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var M in x)if(Object.prototype.hasOwnProperty.call(x,M)){var T=D?Object.getOwnPropertyDescriptor(x,M):null;T&&(T.get||T.set)?Object.defineProperty(F,M,T):F[M]=x[M]}return F.default=x,S&&S.set(x,F),F}function u(x){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?u=function(F){return typeof F}:u=function(F){return F&&typeof Symbol=="function"&&F.constructor===Symbol&&F!==Symbol.prototype?"symbol":typeof F},u(x)}function c(){return c=Object.assign||function(x){for(var S=1;S<arguments.length;S++){var F=arguments[S];for(var D in F)Object.prototype.hasOwnProperty.call(F,D)&&(x[D]=F[D])}return x},c.apply(this,arguments)}function d(x,S){if(!(x instanceof S))throw new TypeError("Cannot call a class as a function")}function g(x,S){for(var F=0;F<S.length;F++){var D=S[F];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(x,D.key,D)}}function h(x,S,F){return S&&g(x.prototype,S),x}function p(x,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function");x.prototype=Object.create(S&&S.prototype,{constructor:{value:x,writable:!0,configurable:!0}}),S&&m(x,S)}function m(x,S){return m=Object.setPrototypeOf||function(D,M){return D.__proto__=M,D},m(x,S)}function y(x){var S=C();return function(){var D=E(x),M;if(S){var T=E(this).constructor;M=Reflect.construct(D,arguments,T)}else M=D.apply(this,arguments);return b(this,M)}}function b(x,S){return S&&(u(S)==="object"||typeof S=="function")?S:v(x)}function v(x){if(x===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return x}function C(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function E(x){return E=Object.setPrototypeOf?Object.getPrototypeOf:function(F){return F.__proto__||Object.getPrototypeOf(F)},E(x)}function I(x,S,F){return S in x?Object.defineProperty(x,S,{value:F,enumerable:!0,configurable:!0,writable:!0}):x[S]=F,x}var R=function(S){return S.hasOwnProperty("key")},P=function(x){p(F,x);var S=y(F);function F(D){var M;return d(this,F),M=S.call(this,D),I(v(M),"itemsWrapperRef",void 0),I(v(M),"itemsListRef",void 0),I(v(M),"thumbsRef",void 0),I(v(M),"setItemsWrapperRef",function(T){M.itemsWrapperRef=T}),I(v(M),"setItemsListRef",function(T){M.itemsListRef=T}),I(v(M),"setThumbsRef",function(T,O){M.thumbsRef||(M.thumbsRef=[]),M.thumbsRef[O]=T}),I(v(M),"updateSizes",function(){if(!(!M.props.children||!M.itemsWrapperRef||!M.thumbsRef)){var T=e.Children.count(M.props.children),O=M.itemsWrapperRef.clientWidth,k=M.props.thumbWidth?M.props.thumbWidth:(0,n.outerWidth)(M.thumbsRef[0]),$=Math.floor(O/k),G=$<T,j=G?T-$:0;M.setState(function(ue,Q){return{itemSize:k,visibleItems:$,firstItem:G?M.getFirstItem(Q.selectedItem):0,lastPosition:j,showArrows:G}})}}),I(v(M),"handleClickItem",function(T,O,k){if(!R(k)||k.key==="Enter"){var $=M.props.onSelectItem;typeof $=="function"&&$(T,O)}}),I(v(M),"onSwipeStart",function(){M.setState({swiping:!0})}),I(v(M),"onSwipeEnd",function(){M.setState({swiping:!1})}),I(v(M),"onSwipeMove",function(T){var O=T.x;if(!M.state.itemSize||!M.itemsWrapperRef||!M.state.visibleItems)return!1;var k=0,$=e.Children.count(M.props.children),G=-(M.state.firstItem*100)/M.state.visibleItems,j=Math.max($-M.state.visibleItems,0),ue=-j*100/M.state.visibleItems;G===k&&O>0&&(O=0),G===ue&&O<0&&(O=0);var Q=M.itemsWrapperRef.clientWidth,ee=G+100/(Q/O);return M.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(ce){M.itemsListRef.style[ce]=(0,r.default)(ee,"%",M.props.axis)}),!0}),I(v(M),"slideRight",function(T){M.moveTo(M.state.firstItem-(typeof T=="number"?T:1))}),I(v(M),"slideLeft",function(T){M.moveTo(M.state.firstItem+(typeof T=="number"?T:1))}),I(v(M),"moveTo",function(T){T=T<0?0:T,T=T>=M.state.lastPosition?M.state.lastPosition:T,M.setState({firstItem:T})}),M.state={selectedItem:D.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},M}return h(F,[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(M){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==M.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(M){var T=M;return M>=this.state.lastPosition&&(T=this.state.lastPosition),M<this.state.firstItem+this.state.visibleItems&&(T=this.state.firstItem),M<this.state.firstItem&&(T=M),T}},{key:"renderItems",value:function(){var M=this;return this.props.children.map(function(T,O){var k=t.default.ITEM(!1,O===M.state.selectedItem),$={key:O,ref:function(j){return M.setThumbsRef(j,O)},className:k,onClick:M.handleClickItem.bind(M,O,M.props.children[O]),onKeyDown:M.handleClickItem.bind(M,O,M.props.children[O]),"aria-label":"".concat(M.props.labels.item," ").concat(O+1),style:{width:M.props.thumbWidth}};return e.default.createElement("li",c({},$,{role:"button",tabIndex:0}),T)})}},{key:"render",value:function(){var M=this;if(!this.props.children)return null;var T=e.Children.count(this.props.children)>1,O=this.state.showArrows&&this.state.firstItem>0,k=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,$={},G=-this.state.firstItem*(this.state.itemSize||0),j=(0,r.default)(G,"px",this.props.axis),ue=this.props.transitionTime+"ms";return $={WebkitTransform:j,MozTransform:j,MsTransform:j,OTransform:j,transform:j,msTransform:j,WebkitTransitionDuration:ue,MozTransitionDuration:ue,MsTransitionDuration:ue,OTransitionDuration:ue,transitionDuration:ue,msTransitionDuration:ue},e.default.createElement("div",{className:t.default.CAROUSEL(!1)},e.default.createElement("div",{className:t.default.WRAPPER(!1),ref:this.setItemsWrapperRef},e.default.createElement("button",{type:"button",className:t.default.ARROW_PREV(!O),onClick:function(){return M.slideRight()},"aria-label":this.props.labels.leftArrow}),T?e.default.createElement(i.default,{tagName:"ul",className:t.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:$,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):e.default.createElement("ul",{className:t.default.SLIDER(!1,this.state.swiping),ref:function(ee){return M.setItemsListRef(ee)},style:$},this.renderItems()),e.default.createElement("button",{type:"button",className:t.default.ARROW_NEXT(!k),onClick:function(){return M.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}]),F}(e.Component);return Gi.default=P,I(P,"displayName","Thumbs"),I(P,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350}),Gi}var Ki={},ou;function fp(){if(ou)return Ki;ou=1,Object.defineProperty(Ki,"__esModule",{value:!0}),Ki.default=void 0;var e=function(){return document};return Ki.default=e,Ki}var Hn={},au;function ld(){if(au)return Hn;au=1,Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.setPosition=Hn.getPosition=Hn.isKeyboardEvent=Hn.defaultStatusFormatter=Hn.noop=void 0;var e=wo(),t=n(Ys());function n(l){return l&&l.__esModule?l:{default:l}}var r=function(){};Hn.noop=r;var i=function(u,c){return"".concat(u," of ").concat(c)};Hn.defaultStatusFormatter=i;var o=function(u){return u?u.hasOwnProperty("key"):!1};Hn.isKeyboardEvent=o;var s=function(u,c){if(c.infiniteLoop&&++u,u===0)return 0;var d=e.Children.count(c.children);if(c.centerMode&&c.axis==="horizontal"){var g=-u*c.centerSlidePercentage,h=d-1;return u&&(u!==h||c.infiniteLoop)?g+=(100-c.centerSlidePercentage)/2:u===h&&(g+=100-c.centerSlidePercentage),g}return-u*100};Hn.getPosition=s;var a=function(u,c){var d={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(g){d[g]=(0,t.default)(u,"%",c)}),d};return Hn.setPosition=a,Hn}var Zn={},su;function hp(){if(su)return Zn;su=1,Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.fadeAnimationHandler=Zn.slideStopSwipingHandler=Zn.slideSwipeAnimationHandler=Zn.slideAnimationHandler=void 0;var e=wo(),t=r(Ys()),n=ld();function r(d){return d&&d.__esModule?d:{default:d}}function i(d,g){var h=Object.keys(d);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(d);g&&(p=p.filter(function(m){return Object.getOwnPropertyDescriptor(d,m).enumerable})),h.push.apply(h,p)}return h}function o(d){for(var g=1;g<arguments.length;g++){var h=arguments[g]!=null?arguments[g]:{};g%2?i(Object(h),!0).forEach(function(p){s(d,p,h[p])}):Object.getOwnPropertyDescriptors?Object.defineProperties(d,Object.getOwnPropertyDescriptors(h)):i(Object(h)).forEach(function(p){Object.defineProperty(d,p,Object.getOwnPropertyDescriptor(h,p))})}return d}function s(d,g,h){return g in d?Object.defineProperty(d,g,{value:h,enumerable:!0,configurable:!0,writable:!0}):d[g]=h,d}var a=function(g,h){var p={},m=h.selectedItem,y=m,b=e.Children.count(g.children)-1,v=g.infiniteLoop&&(m<0||m>b);if(v)return y<0?g.centerMode&&g.centerSlidePercentage&&g.axis==="horizontal"?p.itemListStyle=(0,n.setPosition)(-(b+2)*g.centerSlidePercentage-(100-g.centerSlidePercentage)/2,g.axis):p.itemListStyle=(0,n.setPosition)(-(b+2)*100,g.axis):y>b&&(p.itemListStyle=(0,n.setPosition)(0,g.axis)),p;var C=(0,n.getPosition)(m,g),E=(0,t.default)(C,"%",g.axis),I=g.transitionTime+"ms";return p.itemListStyle={WebkitTransform:E,msTransform:E,OTransform:E,transform:E},h.swiping||(p.itemListStyle=o(o({},p.itemListStyle),{},{WebkitTransitionDuration:I,MozTransitionDuration:I,OTransitionDuration:I,transitionDuration:I,msTransitionDuration:I})),p};Zn.slideAnimationHandler=a;var l=function(g,h,p,m){var y={},b=h.axis==="horizontal",v=e.Children.count(h.children),C=0,E=(0,n.getPosition)(p.selectedItem,h),I=h.infiniteLoop?(0,n.getPosition)(v-1,h)-100:(0,n.getPosition)(v-1,h),R=b?g.x:g.y,P=R;E===C&&R>0&&(P=0),E===I&&R<0&&(P=0);var x=E+100/(p.itemSize/P),S=Math.abs(R)>h.swipeScrollTolerance;return h.infiniteLoop&&S&&(p.selectedItem===0&&x>-100?x-=v*100:p.selectedItem===v-1&&x<-v*100&&(x+=v*100)),(!h.preventMovementUntilSwipeScrollTolerance||S||p.swipeMovementStarted)&&(p.swipeMovementStarted||m({swipeMovementStarted:!0}),y.itemListStyle=(0,n.setPosition)(x,h.axis)),S&&!p.cancelClick&&m({cancelClick:!0}),y};Zn.slideSwipeAnimationHandler=l;var u=function(g,h){var p=(0,n.getPosition)(h.selectedItem,g),m=(0,n.setPosition)(p,g.axis);return{itemListStyle:m}};Zn.slideStopSwipingHandler=u;var c=function(g,h){var p=g.transitionTime+"ms",m="ease-in-out",y={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:m,msTransitionTimingFunction:m,MozTransitionTimingFunction:m,WebkitTransitionTimingFunction:m,OTransitionTimingFunction:m};return h.swiping||(y=o(o({},y),{},{WebkitTransitionDuration:p,MozTransitionDuration:p,OTransitionDuration:p,transitionDuration:p,msTransitionDuration:p})),{slideStyle:y,selectedStyle:o(o({},y),{},{opacity:1,position:"relative"}),prevStyle:o({},y)}};return Zn.fadeAnimationHandler=c,Zn}var lu;function gp(){if(lu)return Ui;lu=1,Object.defineProperty(Ui,"__esModule",{value:!0}),Ui.default=void 0;var e=c(wo()),t=l(id()),n=l(od()),r=l(sd()),i=l(fp()),o=l(ad()),s=ld(),a=hp();function l(D){return D&&D.__esModule?D:{default:D}}function u(){if(typeof WeakMap!="function")return null;var D=new WeakMap;return u=function(){return D},D}function c(D){if(D&&D.__esModule)return D;if(D===null||d(D)!=="object"&&typeof D!="function")return{default:D};var M=u();if(M&&M.has(D))return M.get(D);var T={},O=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var k in D)if(Object.prototype.hasOwnProperty.call(D,k)){var $=O?Object.getOwnPropertyDescriptor(D,k):null;$&&($.get||$.set)?Object.defineProperty(T,k,$):T[k]=D[k]}return T.default=D,M&&M.set(D,T),T}function d(D){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(T){return typeof T}:d=function(T){return T&&typeof Symbol=="function"&&T.constructor===Symbol&&T!==Symbol.prototype?"symbol":typeof T},d(D)}function g(){return g=Object.assign||function(D){for(var M=1;M<arguments.length;M++){var T=arguments[M];for(var O in T)Object.prototype.hasOwnProperty.call(T,O)&&(D[O]=T[O])}return D},g.apply(this,arguments)}function h(D,M){var T=Object.keys(D);if(Object.getOwnPropertySymbols){var O=Object.getOwnPropertySymbols(D);M&&(O=O.filter(function(k){return Object.getOwnPropertyDescriptor(D,k).enumerable})),T.push.apply(T,O)}return T}function p(D){for(var M=1;M<arguments.length;M++){var T=arguments[M]!=null?arguments[M]:{};M%2?h(Object(T),!0).forEach(function(O){S(D,O,T[O])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(T)):h(Object(T)).forEach(function(O){Object.defineProperty(D,O,Object.getOwnPropertyDescriptor(T,O))})}return D}function m(D,M){if(!(D instanceof M))throw new TypeError("Cannot call a class as a function")}function y(D,M){for(var T=0;T<M.length;T++){var O=M[T];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(D,O.key,O)}}function b(D,M,T){return M&&y(D.prototype,M),D}function v(D,M){if(typeof M!="function"&&M!==null)throw new TypeError("Super expression must either be null or a function");D.prototype=Object.create(M&&M.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),M&&C(D,M)}function C(D,M){return C=Object.setPrototypeOf||function(O,k){return O.__proto__=k,O},C(D,M)}function E(D){var M=P();return function(){var O=x(D),k;if(M){var $=x(this).constructor;k=Reflect.construct(O,arguments,$)}else k=O.apply(this,arguments);return I(this,k)}}function I(D,M){return M&&(d(M)==="object"||typeof M=="function")?M:R(D)}function R(D){if(D===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function P(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function x(D){return x=Object.setPrototypeOf?Object.getPrototypeOf:function(T){return T.__proto__||Object.getPrototypeOf(T)},x(D)}function S(D,M,T){return M in D?Object.defineProperty(D,M,{value:T,enumerable:!0,configurable:!0,writable:!0}):D[M]=T,D}var F=function(D){v(T,D);var M=E(T);function T(O){var k;m(this,T),k=M.call(this,O),S(R(k),"thumbsRef",void 0),S(R(k),"carouselWrapperRef",void 0),S(R(k),"listRef",void 0),S(R(k),"itemsRef",void 0),S(R(k),"timer",void 0),S(R(k),"animationHandler",void 0),S(R(k),"setThumbsRef",function(G){k.thumbsRef=G}),S(R(k),"setCarouselWrapperRef",function(G){k.carouselWrapperRef=G}),S(R(k),"setListRef",function(G){k.listRef=G}),S(R(k),"setItemsRef",function(G,j){k.itemsRef||(k.itemsRef=[]),k.itemsRef[j]=G}),S(R(k),"autoPlay",function(){e.Children.count(k.props.children)<=1||(k.clearAutoPlay(),k.props.autoPlay&&(k.timer=setTimeout(function(){k.increment()},k.props.interval)))}),S(R(k),"clearAutoPlay",function(){k.timer&&clearTimeout(k.timer)}),S(R(k),"resetAutoPlay",function(){k.clearAutoPlay(),k.autoPlay()}),S(R(k),"stopOnHover",function(){k.setState({isMouseEntered:!0},k.clearAutoPlay)}),S(R(k),"startOnLeave",function(){k.setState({isMouseEntered:!1},k.autoPlay)}),S(R(k),"isFocusWithinTheCarousel",function(){return k.carouselWrapperRef?!!((0,i.default)().activeElement===k.carouselWrapperRef||k.carouselWrapperRef.contains((0,i.default)().activeElement)):!1}),S(R(k),"navigateWithKeyboard",function(G){if(k.isFocusWithinTheCarousel()){var j=k.props.axis,ue=j==="horizontal",Q={ArrowUp:38,ArrowRight:39,ArrowDown:40,ArrowLeft:37},ee=ue?Q.ArrowRight:Q.ArrowDown,ce=ue?Q.ArrowLeft:Q.ArrowUp;ee===G.keyCode?k.increment():ce===G.keyCode&&k.decrement()}}),S(R(k),"updateSizes",function(){if(!(!k.state.initialized||!k.itemsRef||k.itemsRef.length===0)){var G=k.props.axis==="horizontal",j=k.itemsRef[0];if(j){var ue=G?j.clientWidth:j.clientHeight;k.setState({itemSize:ue}),k.thumbsRef&&k.thumbsRef.updateSizes()}}}),S(R(k),"setMountState",function(){k.setState({hasMount:!0}),k.updateSizes()}),S(R(k),"handleClickItem",function(G,j){if(e.Children.count(k.props.children)!==0){if(k.state.cancelClick){k.setState({cancelClick:!1});return}k.props.onClickItem(G,j),G!==k.state.selectedItem&&k.setState({selectedItem:G})}}),S(R(k),"handleOnChange",function(G,j){e.Children.count(k.props.children)<=1||k.props.onChange(G,j)}),S(R(k),"handleClickThumb",function(G,j){k.props.onClickThumb(G,j),k.moveTo(G)}),S(R(k),"onSwipeStart",function(G){k.setState({swiping:!0}),k.props.onSwipeStart(G)}),S(R(k),"onSwipeEnd",function(G){k.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),k.props.onSwipeEnd(G),k.clearAutoPlay(),k.state.autoPlay&&k.autoPlay()}),S(R(k),"onSwipeMove",function(G,j){k.props.onSwipeMove(j);var ue=k.props.swipeAnimationHandler(G,k.props,k.state,k.setState.bind(R(k)));return k.setState(p({},ue)),!!Object.keys(ue).length}),S(R(k),"decrement",function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem-(typeof G=="number"?G:1))}),S(R(k),"increment",function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem+(typeof G=="number"?G:1))}),S(R(k),"moveTo",function(G){if(typeof G=="number"){var j=e.Children.count(k.props.children)-1;G<0&&(G=k.props.infiniteLoop?j:0),G>j&&(G=k.props.infiniteLoop?0:j),k.selectItem({selectedItem:G}),k.state.autoPlay&&k.state.isMouseEntered===!1&&k.resetAutoPlay()}}),S(R(k),"onClickNext",function(){k.increment(1)}),S(R(k),"onClickPrev",function(){k.decrement(1)}),S(R(k),"onSwipeForward",function(){k.increment(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),S(R(k),"onSwipeBackwards",function(){k.decrement(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),S(R(k),"changeItem",function(G){return function(j){(!(0,s.isKeyboardEvent)(j)||j.key==="Enter")&&k.moveTo(G)}}),S(R(k),"selectItem",function(G){k.setState(p({previousItem:k.state.selectedItem},G),function(){k.setState(k.animationHandler(k.props,k.state))}),k.handleOnChange(G.selectedItem,e.Children.toArray(k.props.children)[G.selectedItem])}),S(R(k),"getInitialImage",function(){var G=k.props.selectedItem,j=k.itemsRef&&k.itemsRef[G],ue=j&&j.getElementsByTagName("img")||[];return ue[0]}),S(R(k),"getVariableItemHeight",function(G){var j=k.itemsRef&&k.itemsRef[G];if(k.state.hasMount&&j&&j.children.length){var ue=j.children[0].getElementsByTagName("img")||[];if(ue.length>0){var Q=ue[0];if(!Q.complete){var ee=function ge(){k.forceUpdate(),Q.removeEventListener("load",ge)};Q.addEventListener("load",ee)}}var ce=ue[0]||j.children[0],ae=ce.clientHeight;return ae>0?ae:null}return null});var $={initialized:!1,previousItem:O.selectedItem,selectedItem:O.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:O.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return k.animationHandler=typeof O.animationHandler=="function"&&O.animationHandler||O.animationHandler==="fade"&&a.fadeAnimationHandler||a.slideAnimationHandler,k.state=p(p({},$),k.animationHandler(O,$)),k}return b(T,[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(k,$){!k.children&&this.props.children&&!this.state.initialized&&this.setupCarousel(),!k.autoFocus&&this.props.autoFocus&&this.forceFocus(),$.swiping&&!this.state.swiping&&this.setState(p({},this.props.stopSwipingHandler(this.props,this.state))),(k.selectedItem!==this.props.selectedItem||k.centerMode!==this.props.centerMode)&&(this.updateSizes(),this.moveTo(this.props.selectedItem)),k.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var k=this;this.bindEvents(),this.state.autoPlay&&e.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},function(){var $=k.getInitialImage();$&&!$.complete?$.addEventListener("load",k.setMountState):k.setMountState()})}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.addEventListener("mouseenter",this.stopOnHover),k.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.removeEventListener("mouseenter",this.stopOnHover),k.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,i.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var k=this.getInitialImage();k&&k.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,i.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var k;(k=this.carouselWrapperRef)===null||k===void 0||k.focus()}},{key:"renderItems",value:function(k){var $=this;return this.props.children?e.Children.map(this.props.children,function(G,j){var ue=j===$.state.selectedItem,Q=j===$.state.previousItem,ee=ue&&$.state.selectedStyle||Q&&$.state.prevStyle||$.state.slideStyle||{};$.props.centerMode&&$.props.axis==="horizontal"&&(ee=p(p({},ee),{},{minWidth:$.props.centerSlidePercentage+"%"})),$.state.swiping&&$.state.swipeMovementStarted&&(ee=p(p({},ee),{},{pointerEvents:"none"}));var ce={ref:function(ge){return $.setItemsRef(ge,j)},key:"itemKey"+j+(k?"clone":""),className:n.default.ITEM(!0,j===$.state.selectedItem,j===$.state.previousItem),onClick:$.handleClickItem.bind($,j,G),style:ee};return e.default.createElement("li",ce,$.props.renderItem(G,{isSelected:j===$.state.selectedItem,isPrevious:j===$.state.previousItem}))}):[]}},{key:"renderControls",value:function(){var k=this,$=this.props,G=$.showIndicators,j=$.labels,ue=$.renderIndicator,Q=$.children;return G?e.default.createElement("ul",{className:"control-dots"},e.Children.map(Q,function(ee,ce){return ue&&ue(k.changeItem(ce),ce===k.state.selectedItem,ce,j.item)})):null}},{key:"renderStatus",value:function(){return this.props.showStatus?e.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,e.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return!this.props.showThumbs||!this.props.children||e.Children.count(this.props.children)===0?null:e.default.createElement(r.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children))}},{key:"render",value:function(){var k=this;if(!this.props.children||e.Children.count(this.props.children)===0)return null;var $=this.props.swipeable&&e.Children.count(this.props.children)>1,G=this.props.axis==="horizontal",j=this.props.showArrows&&e.Children.count(this.props.children)>1,ue=j&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,Q=j&&(this.state.selectedItem<e.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,ee=this.renderItems(!0),ce=ee.shift(),ae=ee.pop(),ge={className:n.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},de={};if(G){if(ge.onSwipeLeft=this.onSwipeForward,ge.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var re=this.getVariableItemHeight(this.state.selectedItem);de.height=re||"auto"}}else ge.onSwipeUp=this.props.verticalSwipe==="natural"?this.onSwipeBackwards:this.onSwipeForward,ge.onSwipeDown=this.props.verticalSwipe==="natural"?this.onSwipeForward:this.onSwipeBackwards,ge.style=p(p({},ge.style),{},{height:this.state.itemSize}),de.height=this.state.itemSize;return e.default.createElement("div",{"aria-label":this.props.ariaLabel,className:n.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},e.default.createElement("div",{className:n.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,ue,this.props.labels.leftArrow),e.default.createElement("div",{className:n.default.WRAPPER(!0,this.props.axis),style:de},$?e.default.createElement(t.default,g({tagName:"ul",innerRef:this.setListRef},ge,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&ae,this.renderItems(),this.props.infiniteLoop&&ce):e.default.createElement("ul",{className:n.default.SLIDER(!0,this.state.swiping),ref:function(L){return k.setListRef(L)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&ae,this.renderItems(),this.props.infiniteLoop&&ce)),this.props.renderArrowNext(this.onClickNext,Q,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}]),T}(e.default.Component);return Ui.default=F,S(F,"displayName","Carousel"),S(F,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:s.noop,onClickThumb:s.noop,onChange:s.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(M,T,O){return e.default.createElement("button",{type:"button","aria-label":O,className:n.default.ARROW_PREV(!T),onClick:M})},renderArrowNext:function(M,T,O){return e.default.createElement("button",{type:"button","aria-label":O,className:n.default.ARROW_NEXT(!T),onClick:M})},renderIndicator:function(M,T,O,k){return e.default.createElement("li",{className:n.default.DOT(T),onClick:M,onKeyDown:M,value:O,key:O,role:"button",tabIndex:0,"aria-label":"".concat(k," ").concat(O+1)})},renderItem:function(M){return M},renderThumbs:function(M){var T=e.Children.map(M,function(O){var k=O;if(O.type!=="img"&&(k=e.Children.toArray(O.props.children).find(function($){return $.type==="img"})),!!k)return k});return T.filter(function(O){return O}).length===0?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):T},statusFormatter:s.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:a.slideSwipeAnimationHandler,stopSwipingHandler:a.slideStopSwipingHandler}),Ui}var uu={},cu;function pp(){return cu||(cu=1),uu}var du;function mp(){return du||(du=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Carousel",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"CarouselProps",{enumerable:!0,get:function(){return n.CarouselProps}}),Object.defineProperty(e,"Thumbs",{enumerable:!0,get:function(){return r.default}});var t=i(gp()),n=pp(),r=i(sd());function i(o){return o&&o.__esModule?o:{default:o}}}(es)),es}var vp=mp(),ts,fu;function bp(){if(fu)return ts;fu=1;var e=Lh(),t=function(){return e.Date.now()};return ts=t,ts}var ns,hu;function ud(){if(hu)return ns;hu=1;var e=$c(),t=bp(),n=Nc(),r="Expected a function",i=Math.max,o=Math.min;function s(a,l,u){var c,d,g,h,p,m,y=0,b=!1,v=!1,C=!0;if(typeof a!="function")throw new TypeError(r);l=n(l)||0,e(u)&&(b=!!u.leading,v="maxWait"in u,g=v?i(n(u.maxWait)||0,l):g,C="trailing"in u?!!u.trailing:C);function E(T){var O=c,k=d;return c=d=void 0,y=T,h=a.apply(k,O),h}function I(T){return y=T,p=setTimeout(x,l),b?E(T):h}function R(T){var O=T-m,k=T-y,$=l-O;return v?o($,g-k):$}function P(T){var O=T-m,k=T-y;return m===void 0||O>=l||O<0||v&&k>=g}function x(){var T=t();if(P(T))return S(T);p=setTimeout(x,R(T))}function S(T){return p=void 0,C&&c?E(T):(c=d=void 0,h)}function F(){p!==void 0&&clearTimeout(p),y=0,c=m=d=p=void 0}function D(){return p===void 0?h:S(t())}function M(){var T=t(),O=P(T);if(c=arguments,d=this,m=T,O){if(p===void 0)return I(m);if(v)return clearTimeout(p),p=setTimeout(x,l),E(m)}return p===void 0&&(p=setTimeout(x,l)),h}return M.cancel=F,M.flush=D,M}return ns=s,ns}var wp=ud();const cd=vr(wp);function xn(e,t,n,r,i=!1){const o=f.useRef();o.current=t,f.useEffect(()=>{if(n===null||n.addEventListener===void 0)return;const s=n,a=l=>{o.current?.call(s,l)};return s.addEventListener(e,a,{passive:r,capture:i}),()=>{s.removeEventListener(e,a,{capture:i})}},[e,n,r,i])}function Er(e,t){return e===void 0?void 0:t}const yp=Math.PI;function gu(e){return e*yp/180}const dd=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),fd=(e,t,n,r,i)=>{switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},hd=(e,t,n)=>Math.min(e,t-n*2),gd=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,js=e=>{const t=e.fgColor??"currentColor";return f.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},f.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),f.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},Cp=e=>{const t=e.fgColor??"currentColor";return f.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},f.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function Sp(e,t,n){const[r,i]=f.useState(e),o=f.useRef(!0);f.useEffect(()=>()=>{o.current=!1},[]);const s=f.useRef(cd(a=>{o.current&&i(a)},n));return f.useLayoutEffect(()=>{o.current&&s.current(()=>e())},t),r}const xp="֑-߿יִ-﷽ﹰ-ﻼ",kp="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",Mp=new RegExp("^[^"+kp+"]*["+xp+"]");function Ks(e){return Mp.test(e)?"rtl":"not-rtl"}let Wo;function Ts(){if(typeof document>"u")return 0;if(Wo!==void 0)return Wo;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Wo=n-r,Wo}const Ur=Symbol();function Rp(e){const t=f.useRef([Ur,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=f.useState(e),[,i]=f.useState(),o=f.useCallback(a=>{const l=t.current[0];l!==Ur&&(a=typeof a=="function"?a(l):a,a===l)||(l!==Ur&&i({}),r(u=>typeof a=="function"?a(l===Ur?u:l):a),t.current[0]=Ur)},[]),s=f.useCallback(()=>{t.current[0]=Ur,i({})},[]);return[t.current[0]===Ur?n:t.current[0],o,s]}function pd(e){if(e.length===0)return"";let t=0,n=0;for(const r of e){if(n+=r.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function Ip(e){const t=f.useRef(e);return Mi(e,t.current)||(t.current=e),t.current}const Ep=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,o=t.filter(a=>a!=="");if(o.length===0)return null;const s=o.length>1;return f.createElement(up,{"data-testid":"GDG-default-image-overlay-editor"},f.createElement(vp.Carousel,{showArrows:s,showThumbs:!1,swipeable:s,emulateTouch:s,infiniteLoop:s},o.map(a=>{const l=i?.(a)??f.createElement("img",{draggable:!1,src:a});return f.createElement("div",{className:"gdg-centering-container",key:a},l)})),n&&r&&f.createElement("button",{className:"gdg-edit-icon",onClick:r},f.createElement(js,null)))};function md(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Zr=md();function Tp(e){Zr=e}const vd=/[&<>"']/,Dp=new RegExp(vd.source,"g"),bd=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Op=new RegExp(bd.source,"g"),Pp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},pu=e=>Pp[e];function bn(e,t){if(t){if(vd.test(e))return e.replace(Dp,pu)}else if(bd.test(e))return e.replace(Op,pu);return e}const Lp=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function wd(e){return e.replace(Lp,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const Fp=/(^|[^\[])\^/g;function Ht(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,i)=>(i=i.source||i,i=i.replace(Fp,"$1"),e=e.replace(r,i),n),getRegex:()=>new RegExp(e,t)};return n}const _p=/[^\w:]/g,Ap=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function mu(e,t,n){if(e){let r;try{r=decodeURIComponent(wd(n)).replace(_p,"").toLowerCase()}catch{return null}if(r.indexOf("javascript:")===0||r.indexOf("vbscript:")===0||r.indexOf("data:")===0)return null}t&&!Ap.test(n)&&(n=$p(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const Uo={},Hp=/^[^:]+:\/*[^/]*$/,zp=/^([^:]+:)[\s\S]*$/,Vp=/^([^:]+:\/*[^/]*)[\s\S]*$/;function $p(e,t){Uo[" "+e]||(Hp.test(e)?Uo[" "+e]=e+"/":Uo[" "+e]=ta(e,"/",!0)),e=Uo[" "+e];const n=e.indexOf(":")===-1;return t.substring(0,2)==="//"?n?t:e.replace(zp,"$1")+t:t.charAt(0)==="/"?n?t:e.replace(Vp,"$1")+t:e+t}const ca={exec:function(){}};function vu(e,t){const n=e.replace(/\|/g,(o,s,a)=>{let l=!1,u=s;for(;--u>=0&&a[u]==="\\";)l=!l;return l?"|":" |"}),r=n.split(/ \|/);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function ta(e,t,n){const r=e.length;if(r===0)return"";let i=0;for(;i<r;){const o=e.charAt(r-i-1);if(o===t&&!n)i++;else if(o!==t&&n)i++;else break}return e.slice(0,r-i)}function Np(e,t){if(e.indexOf(t[1])===-1)return-1;const n=e.length;let r=0,i=0;for(;i<n;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function Bp(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function bu(e,t){if(t<1)return"";let n="";for(;t>1;)t&1&&(n+=e),t>>=1,e+=e;return n+e}function wu(e,t,n,r){const i=t.href,o=t.title?bn(t.title):null,s=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const a={type:"link",raw:n,href:i,title:o,text:s,tokens:r.inlineTokens(s)};return r.state.inLink=!1,a}return{type:"image",raw:n,href:i,title:o,text:bn(s)}}function Wp(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(i=>{const o=i.match(/^\s+/);if(o===null)return i;const[s]=o;return s.length>=r.length?i.slice(r.length):i}).join(`
`)}class Zs{constructor(t){this.options=t||Zr}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:ta(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],i=Wp(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:i}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const i=ta(r,"#");(this.options.pedantic||!i||/ $/.test(i))&&(r=i.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=n[0].replace(/^ *>[ \t]?/gm,""),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(r);return this.lexer.state.top=i,{type:"blockquote",raw:n[0],tokens:o,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r,i,o,s,a,l,u,c,d,g,h,p,m=n[1].trim();const y=m.length>1,b={type:"list",raw:"",ordered:y,start:y?+m.slice(0,-1):"",loose:!1,items:[]};m=y?`\\d{1,9}\\${m.slice(-1)}`:`\\${m}`,this.options.pedantic&&(m=y?m:"[*+-]");const v=new RegExp(`^( {0,3}${m})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;t&&(p=!1,!(!(n=v.exec(t))||this.rules.block.hr.test(t)));){if(r=n[0],t=t.substring(r.length),c=n[2].split(`
`,1)[0].replace(/^\t+/,E=>" ".repeat(3*E.length)),d=t.split(`
`,1)[0],this.options.pedantic?(s=2,h=c.trimLeft()):(s=n[2].search(/[^ ]/),s=s>4?1:s,h=c.slice(s),s+=n[1].length),l=!1,!c&&/^ *$/.test(d)&&(r+=d+`
`,t=t.substring(d.length+1),p=!0),!p){const E=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),I=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),R=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),P=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);for(;t&&(g=t.split(`
`,1)[0],d=g,this.options.pedantic&&(d=d.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(R.test(d)||P.test(d)||E.test(d)||I.test(t)));){if(d.search(/[^ ]/)>=s||!d.trim())h+=`
`+d.slice(s);else{if(l||c.search(/[^ ]/)>=4||R.test(c)||P.test(c)||I.test(c))break;h+=`
`+d}!l&&!d.trim()&&(l=!0),r+=g+`
`,t=t.substring(g.length+1),c=d.slice(s)}}b.loose||(u?b.loose=!0:/\n *\n *$/.test(r)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(h),i&&(o=i[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),b.items.push({type:"list_item",raw:r,task:!!i,checked:o,loose:!1,text:h}),b.raw+=r}b.items[b.items.length-1].raw=r.trimRight(),b.items[b.items.length-1].text=h.trimRight(),b.raw=b.raw.trimRight();const C=b.items.length;for(a=0;a<C;a++)if(this.lexer.state.top=!1,b.items[a].tokens=this.lexer.blockTokens(b.items[a].text,[]),!b.loose){const E=b.items[a].tokens.filter(R=>R.type==="space"),I=E.length>0&&E.some(R=>/\n.*\n/.test(R.raw));b.loose=I}if(b.loose)for(a=0;a<C;a++)b.items[a].loose=!0;return b}}html(t){const n=this.rules.block.html.exec(t);if(n){const r={type:"html",raw:n[0],pre:!this.options.sanitizer&&(n[1]==="pre"||n[1]==="script"||n[1]==="style"),text:n[0]};if(this.options.sanitize){const i=this.options.sanitizer?this.options.sanitizer(n[0]):bn(n[0]);r.type="paragraph",r.text=i,r.tokens=this.lexer.inline(i)}return r}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),i=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:i,title:o}}}table(t){const n=this.rules.block.table.exec(t);if(n){const r={type:"table",header:vu(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){r.raw=n[0];let i=r.align.length,o,s,a,l;for(o=0;o<i;o++)/^ *-+: *$/.test(r.align[o])?r.align[o]="right":/^ *:-+: *$/.test(r.align[o])?r.align[o]="center":/^ *:-+ *$/.test(r.align[o])?r.align[o]="left":r.align[o]=null;for(i=r.rows.length,o=0;o<i;o++)r.rows[o]=vu(r.rows[o],r.header.length).map(u=>({text:u}));for(i=r.header.length,s=0;s<i;s++)r.header[s].tokens=this.lexer.inline(r.header[s].text);for(i=r.rows.length,s=0;s<i;s++)for(l=r.rows[s],a=0;a<l.length;a++)l[a].tokens=this.lexer.inline(l[a].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:bn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):bn(n[0]):n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const s=ta(r.slice(0,-1),"\\");if((r.length-s.length)%2===0)return}else{const s=Np(n[2],"()");if(s>-1){const l=(n[0].indexOf("!")===0?5:4)+n[1].length+s;n[2]=n[2].substring(0,s),n[0]=n[0].substring(0,l).trim(),n[3]=""}}let i=n[2],o="";if(this.options.pedantic){const s=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);s&&(i=s[1],o=s[3])}else o=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(r)?i=i.slice(1):i=i.slice(1,-1)),wu(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let i=(r[2]||r[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i){const o=r[0].charAt(0);return{type:"text",raw:o,text:o}}return wu(r,i,r[0],this.lexer)}}emStrong(t,n,r=""){let i=this.rules.inline.emStrong.lDelim.exec(t);if(!i||i[3]&&r.match(/[\p{L}\p{N}]/u))return;const o=i[1]||i[2]||"";if(!o||o&&(r===""||this.rules.inline.punctuation.exec(r))){const s=i[0].length-1;let a,l,u=s,c=0;const d=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(d.lastIndex=0,n=n.slice(-1*t.length+s);(i=d.exec(n))!=null;){if(a=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!a)continue;if(l=a.length,i[3]||i[4]){u+=l;continue}else if((i[5]||i[6])&&s%3&&!((s+l)%3)){c+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+c);const g=t.slice(0,s+i.index+(i[0].length-a.length)+l);if(Math.min(s,l)%2){const p=g.slice(1,-1);return{type:"em",raw:g,text:p,tokens:this.lexer.inlineTokens(p)}}const h=g.slice(2,-2);return{type:"strong",raw:g,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const i=/[^ ]/.test(r),o=/^ /.test(r)&&/ $/.test(r);return i&&o&&(r=r.substring(1,r.length-1)),r=bn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t,n){const r=this.rules.inline.autolink.exec(t);if(r){let i,o;return r[2]==="@"?(i=bn(this.options.mangle?n(r[1]):r[1]),o="mailto:"+i):(i=bn(r[1]),o=i),{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}url(t,n){let r;if(r=this.rules.inline.url.exec(t)){let i,o;if(r[2]==="@")i=bn(this.options.mangle?n(r[0]):r[0]),o="mailto:"+i;else{let s;do s=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])[0];while(s!==r[0]);i=bn(r[0]),r[1]==="www."?o="http://"+r[0]:o=r[0]}return{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t,n){const r=this.rules.inline.text.exec(t);if(r){let i;return this.lexer.state.inRawBlock?i=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):bn(r[0]):r[0]:i=bn(this.options.smartypants?n(r[0]):r[0]),{type:"text",raw:r[0],text:i}}}}const Ve={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:ca,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};Ve._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;Ve._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;Ve.def=Ht(Ve.def).replace("label",Ve._label).replace("title",Ve._title).getRegex();Ve.bullet=/(?:[*+-]|\d{1,9}[.)])/;Ve.listItemStart=Ht(/^( *)(bull) */).replace("bull",Ve.bullet).getRegex();Ve.list=Ht(Ve.list).replace(/bull/g,Ve.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+Ve.def.source+")").getRegex();Ve._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";Ve._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;Ve.html=Ht(Ve.html,"i").replace("comment",Ve._comment).replace("tag",Ve._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();Ve.paragraph=Ht(Ve._paragraph).replace("hr",Ve.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ve._tag).getRegex();Ve.blockquote=Ht(Ve.blockquote).replace("paragraph",Ve.paragraph).getRegex();Ve.normal={...Ve};Ve.gfm={...Ve.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};Ve.gfm.table=Ht(Ve.gfm.table).replace("hr",Ve.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ve._tag).getRegex();Ve.gfm.paragraph=Ht(Ve._paragraph).replace("hr",Ve.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",Ve.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ve._tag).getRegex();Ve.pedantic={...Ve.normal,html:Ht(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ve._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ca,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Ht(Ve.normal._paragraph).replace("hr",Ve.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ve.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const be={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:ca,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:ca,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};be._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";be.punctuation=Ht(be.punctuation).replace(/punctuation/g,be._punctuation).getRegex();be.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;be.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;be._comment=Ht(Ve._comment).replace("(?:-->|$)","-->").getRegex();be.emStrong.lDelim=Ht(be.emStrong.lDelim).replace(/punct/g,be._punctuation).getRegex();be.emStrong.rDelimAst=Ht(be.emStrong.rDelimAst,"g").replace(/punct/g,be._punctuation).getRegex();be.emStrong.rDelimUnd=Ht(be.emStrong.rDelimUnd,"g").replace(/punct/g,be._punctuation).getRegex();be._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;be._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;be._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;be.autolink=Ht(be.autolink).replace("scheme",be._scheme).replace("email",be._email).getRegex();be._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;be.tag=Ht(be.tag).replace("comment",be._comment).replace("attribute",be._attribute).getRegex();be._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;be._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;be._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;be.link=Ht(be.link).replace("label",be._label).replace("href",be._href).replace("title",be._title).getRegex();be.reflink=Ht(be.reflink).replace("label",be._label).replace("ref",Ve._label).getRegex();be.nolink=Ht(be.nolink).replace("ref",Ve._label).getRegex();be.reflinkSearch=Ht(be.reflinkSearch,"g").replace("reflink",be.reflink).replace("nolink",be.nolink).getRegex();be.normal={...be};be.pedantic={...be.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Ht(/^!?\[(label)\]\((.*?)\)/).replace("label",be._label).getRegex(),reflink:Ht(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",be._label).getRegex()};be.gfm={...be.normal,escape:Ht(be.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};be.gfm.url=Ht(be.gfm.url,"i").replace("email",be.gfm._extended_email).getRegex();be.breaks={...be.gfm,br:Ht(be.br).replace("{2,}","*").getRegex(),text:Ht(be.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function Up(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function yu(e){let t="",n,r;const i=e.length;for(n=0;n<i;n++)r=e.charCodeAt(n),Math.random()>.5&&(r="x"+r.toString(16)),t+="&#"+r+";";return t}class Pr{constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Zr,this.options.tokenizer=this.options.tokenizer||new Zs,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:Ve.normal,inline:be.normal};this.options.pedantic?(n.block=Ve.pedantic,n.inline=be.pedantic):this.options.gfm&&(n.block=Ve.gfm,this.options.breaks?n.inline=be.breaks:n.inline=be.gfm),this.tokenizer.rules=n}static get rules(){return{block:Ve,inline:be}}static lex(t,n){return new Pr(n).lex(t)}static lexInline(t,n){return new Pr(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(a,l,u)=>l+"    ".repeat(u.length));let r,i,o,s;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>(r=a.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let u;this.options.extensions.startBlock.forEach(function(c){u=c.call({lexer:this},l),typeof u=="number"&&u>=0&&(a=Math.min(a,u))}),a<1/0&&a>=0&&(o=t.substring(0,a+1))}if(this.state.top&&(r=this.tokenizer.paragraph(o))){i=n[n.length-1],s&&i.type==="paragraph"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r),s=o.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&i.type==="text"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,i,o,s=t,a,l,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(a=this.tokenizer.rules.inline.reflinkSearch.exec(s))!=null;)c.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,a.index)+"["+bu("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(a=this.tokenizer.rules.inline.blockSkip.exec(s))!=null;)s=s.slice(0,a.index)+"["+bu("a",a[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(a=this.tokenizer.rules.inline.escapedEmSt.exec(s))!=null;)s=s.slice(0,a.index+a[0].length-2)+"++"+s.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;t;)if(l||(u=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(r=c.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,s,u)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t,yu)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t,yu))){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=t.slice(1);let g;this.options.extensions.startInline.forEach(function(h){g=h.call({lexer:this},d),typeof g=="number"&&g>=0&&(c=Math.min(c,g))}),c<1/0&&c>=0&&(o=t.substring(0,c+1))}if(r=this.tokenizer.inlineText(o,Up)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(u=r.raw.slice(-1)),l=!0,i=n[n.length-1],i&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Js{constructor(t){this.options=t||Zr}code(t,n,r){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const o=this.options.highlight(t,i);o!=null&&o!==t&&(r=!0,t=o)}return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="'+this.options.langPrefix+bn(i)+'">'+(r?t:bn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:bn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t){return t}heading(t,n,r,i){if(this.options.headerIds){const o=this.options.headerPrefix+i.slug(r);return`<h${n} id="${o}">${t}</h${n}>
`}return`<h${n}>${t}</h${n}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(t,n,r){const i=n?"ol":"ul",o=n&&r!==1?' start="'+r+'"':"";return"<"+i+o+`>
`+t+"</"+i+`>
`}listitem(t){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){if(t=mu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i='<a href="'+t+'"';return n&&(i+=' title="'+n+'"'),i+=">"+r+"</a>",i}image(t,n,r){if(t=mu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(t){return t}}class yd{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class Cd{constructor(){this.seen={}}serialize(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(t,n){let r=t,i=0;if(this.seen.hasOwnProperty(r)){i=this.seen[t];do i++,r=t+"-"+i;while(this.seen.hasOwnProperty(r))}return n||(this.seen[t]=i,this.seen[r]=0),r}slug(t,n={}){const r=this.serialize(t);return this.getNextSafeSlug(r,n.dryrun)}}class Lr{constructor(t){this.options=t||Zr,this.options.renderer=this.options.renderer||new Js,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new yd,this.slugger=new Cd}static parse(t,n){return new Lr(n).parse(t)}static parseInline(t,n){return new Lr(n).parseInline(t)}parse(t,n=!0){let r="",i,o,s,a,l,u,c,d,g,h,p,m,y,b,v,C,E,I,R;const P=t.length;for(i=0;i<P;i++){if(h=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(R=this.options.extensions.renderers[h.type].call({parser:this},h),R!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type))){r+=R||"";continue}switch(h.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{r+=this.renderer.heading(this.parseInline(h.tokens),h.depth,wd(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue}case"code":{r+=this.renderer.code(h.text,h.lang,h.escaped);continue}case"table":{for(d="",c="",a=h.header.length,o=0;o<a;o++)c+=this.renderer.tablecell(this.parseInline(h.header[o].tokens),{header:!0,align:h.align[o]});for(d+=this.renderer.tablerow(c),g="",a=h.rows.length,o=0;o<a;o++){for(u=h.rows[o],c="",l=u.length,s=0;s<l;s++)c+=this.renderer.tablecell(this.parseInline(u[s].tokens),{header:!1,align:h.align[s]});g+=this.renderer.tablerow(c)}r+=this.renderer.table(d,g);continue}case"blockquote":{g=this.parse(h.tokens),r+=this.renderer.blockquote(g);continue}case"list":{for(p=h.ordered,m=h.start,y=h.loose,a=h.items.length,g="",o=0;o<a;o++)v=h.items[o],C=v.checked,E=v.task,b="",v.task&&(I=this.renderer.checkbox(C),y?v.tokens.length>0&&v.tokens[0].type==="paragraph"?(v.tokens[0].text=I+" "+v.tokens[0].text,v.tokens[0].tokens&&v.tokens[0].tokens.length>0&&v.tokens[0].tokens[0].type==="text"&&(v.tokens[0].tokens[0].text=I+" "+v.tokens[0].tokens[0].text)):v.tokens.unshift({type:"text",text:I}):b+=I),b+=this.parse(v.tokens,y),g+=this.renderer.listitem(b,E,C);r+=this.renderer.list(g,p,m);continue}case"html":{r+=this.renderer.html(h.text);continue}case"paragraph":{r+=this.renderer.paragraph(this.parseInline(h.tokens));continue}case"text":{for(g=h.tokens?this.parseInline(h.tokens):h.text;i+1<P&&t[i+1].type==="text";)h=t[++i],g+=`
`+(h.tokens?this.parseInline(h.tokens):h.text);r+=n?this.renderer.paragraph(g):g;continue}default:{const x='Token with "'+h.type+'" type was not found.';if(this.options.silent){console.error(x);return}else throw new Error(x)}}}return r}parseInline(t,n){n=n||this.renderer;let r="",i,o,s;const a=t.length;for(i=0;i<a;i++){if(o=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(s=this.options.extensions.renderers[o.type].call({parser:this},o),s!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type))){r+=s||"";continue}switch(o.type){case"escape":{r+=n.text(o.text);break}case"html":{r+=n.html(o.text);break}case"link":{r+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{r+=n.image(o.href,o.title,o.text);break}case"strong":{r+=n.strong(this.parseInline(o.tokens,n));break}case"em":{r+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{r+=n.codespan(o.text);break}case"br":{r+=n.br();break}case"del":{r+=n.del(this.parseInline(o.tokens,n));break}case"text":{r+=n.text(o.text);break}default:{const l='Token with "'+o.type+'" type was not found.';if(this.options.silent){console.error(l);return}else throw new Error(l)}}}return r}}class Ds{constructor(t){this.options=t||Zr}static passThroughHooks=new Set(["preprocess","postprocess"]);preprocess(t){return t}postprocess(t){return t}}function qp(e,t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+bn(r.message+"",!0)+"</pre>";if(t)return Promise.resolve(i);if(n){n(null,i);return}return i}if(t)return Promise.reject(r);if(n){n(r);return}throw r}}function Sd(e,t){return(n,r,i)=>{typeof r=="function"&&(i=r,r=null);const o={...r};r={...Ne.defaults,...o};const s=qp(r.silent,r.async,i);if(typeof n>"u"||n===null)return s(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return s(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(Bp(r),r.hooks&&(r.hooks.options=r),i){const a=r.highlight;let l;try{r.hooks&&(n=r.hooks.preprocess(n)),l=e(n,r)}catch(d){return s(d)}const u=function(d){let g;if(!d)try{r.walkTokens&&Ne.walkTokens(l,r.walkTokens),g=t(l,r),r.hooks&&(g=r.hooks.postprocess(g))}catch(h){d=h}return r.highlight=a,d?s(d):i(null,g)};if(!a||a.length<3||(delete r.highlight,!l.length))return u();let c=0;Ne.walkTokens(l,function(d){d.type==="code"&&(c++,setTimeout(()=>{a(d.text,d.lang,function(g,h){if(g)return u(g);h!=null&&h!==d.text&&(d.text=h,d.escaped=!0),c--,c===0&&u()})},0))}),c===0&&u();return}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(a=>e(a,r)).then(a=>r.walkTokens?Promise.all(Ne.walkTokens(a,r.walkTokens)).then(()=>a):a).then(a=>t(a,r)).then(a=>r.hooks?r.hooks.postprocess(a):a).catch(s);try{r.hooks&&(n=r.hooks.preprocess(n));const a=e(n,r);r.walkTokens&&Ne.walkTokens(a,r.walkTokens);let l=t(a,r);return r.hooks&&(l=r.hooks.postprocess(l)),l}catch(a){return s(a)}}}function Ne(e,t,n){return Sd(Pr.lex,Lr.parse)(e,t,n)}Ne.options=Ne.setOptions=function(e){return Ne.defaults={...Ne.defaults,...e},Tp(Ne.defaults),Ne};Ne.getDefaults=md;Ne.defaults=Zr;Ne.use=function(...e){const t=Ne.defaults.extensions||{renderers:{},childTokens:{}};e.forEach(n=>{const r={...n};if(r.async=Ne.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if(i.renderer){const o=t.renderers[i.name];o?t.renderers[i.name]=function(...s){let a=i.renderer.apply(this,s);return a===!1&&(a=o.apply(this,s)),a}:t.renderers[i.name]=i.renderer}if(i.tokenizer){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");t[i.level]?t[i.level].unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){const i=Ne.defaults.renderer||new Js;for(const o in n.renderer){const s=i[o];i[o]=(...a)=>{let l=n.renderer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.renderer=i}if(n.tokenizer){const i=Ne.defaults.tokenizer||new Zs;for(const o in n.tokenizer){const s=i[o];i[o]=(...a)=>{let l=n.tokenizer[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.tokenizer=i}if(n.hooks){const i=Ne.defaults.hooks||new Ds;for(const o in n.hooks){const s=i[o];Ds.passThroughHooks.has(o)?i[o]=a=>{if(Ne.defaults.async)return Promise.resolve(n.hooks[o].call(i,a)).then(u=>s.call(i,u));const l=n.hooks[o].call(i,a);return s.call(i,l)}:i[o]=(...a)=>{let l=n.hooks[o].apply(i,a);return l===!1&&(l=s.apply(i,a)),l}}r.hooks=i}if(n.walkTokens){const i=Ne.defaults.walkTokens;r.walkTokens=function(o){let s=[];return s.push(n.walkTokens.call(this,o)),i&&(s=s.concat(i.call(this,o))),s}}Ne.setOptions(r)})};Ne.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(Ne,r)),r.type){case"table":{for(const i of r.header)n=n.concat(Ne.walkTokens(i.tokens,t));for(const i of r.rows)for(const o of i)n=n.concat(Ne.walkTokens(o.tokens,t));break}case"list":{n=n.concat(Ne.walkTokens(r.items,t));break}default:Ne.defaults.extensions&&Ne.defaults.extensions.childTokens&&Ne.defaults.extensions.childTokens[r.type]?Ne.defaults.extensions.childTokens[r.type].forEach(function(i){n=n.concat(Ne.walkTokens(r[i],t))}):r.tokens&&(n=n.concat(Ne.walkTokens(r.tokens,t)))}return n};Ne.parseInline=Sd(Pr.lexInline,Lr.parseInline);Ne.Parser=Lr;Ne.parser=Lr.parse;Ne.Renderer=Js;Ne.TextRenderer=yd;Ne.Lexer=Pr;Ne.lexer=Pr.lex;Ne.Tokenizer=Zs;Ne.Slugger=Cd;Ne.Hooks=Ds;Ne.parse=Ne;Ne.options;Ne.setOptions;Ne.use;Ne.walkTokens;Ne.parseInline;Lr.parse;Pr.lex;const Gp=sn("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class Xp extends $t.PureComponent{targetElement=null;renderMarkdownIntoDiv(){const{targetElement:t,props:n}=this;if(t===null)return;const{contents:r,createNode:i}=n,o=Ne(r),s=document.createRange();s.selectNodeContents(t),s.deleteContents();let a=i?.(o);if(a===void 0){const u=document.createElement("template");u.innerHTML=o,a=u.content}t.append(a);const l=t.getElementsByTagName("a");for(const u of l)u.target="_blank",u.rel="noreferrer noopener"}containerRefHook=t=>{this.targetElement=t,this.renderMarkdownIntoDiv()};render(){return this.renderMarkdownIntoDiv(),$t.createElement(Gp,{ref:this.containerRefHook})}}const Yp=sn("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),jp=sn("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),Kp=sn("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});let Cu=0;const Jr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:o,validatedSelection:s,...a}=e,{onChange:l,className:u}=a,c=f.useRef(null),d=n??"";zn(l!==void 0,"GrowingEntry must be a controlled input area");const[g]=f.useState(()=>"input-box-"+(Cu=(Cu+1)%1e7));f.useEffect(()=>{const p=c.current;if(p===null||p.disabled)return;const m=d.toString().length;p.focus(),p.setSelectionRange(i?0:m,m)},[]),f.useLayoutEffect(()=>{if(s!==void 0){const p=typeof s=="number"?[s,null]:s;c.current?.setSelectionRange(p[0],p[1])}},[s]);const h=f.useCallback(p=>{p.key==="Enter"&&p.shiftKey&&o===!0||r?.(p)},[o,r]);return f.createElement(Kp,{className:"gdg-growing-entry"},f.createElement(jp,{className:u},d+`
`),f.createElement(Yp,{...a,className:(u??"")+" gdg-input",id:g,ref:c,onKeyDown:h,value:d,placeholder:t,dir:"auto"}))},rs={};let Rr=null;function Zp(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}function fo(e){const t=e.toLowerCase().trim();if(rs[t]!==void 0)return rs[t];Rr=Rr||Zp(),Rr.style.color="#000",Rr.style.color=t;const n=getComputedStyle(Rr).color;Rr.style.color="#fff",Rr.style.color=t;const r=getComputedStyle(Rr).color;if(r!==n)return[0,0,0,1];let i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map(o=>Number.isNaN(o)?0:o),rs[t]=i,i}function jr(e,t){const[n,r,i]=fo(e);return`rgba(${n}, ${r}, ${i}, ${t})`}const Su=new Map;function xu(e,t){const n=`${e}-${t}`,r=Su.get(n);if(r!==void 0)return r;const i=Vn(e,t);return Su.set(n,i),i}function Vn(e,t){if(t===void 0)return e;const[n,r,i,o]=fo(e);if(o===1)return e;const[s,a,l,u]=fo(t),c=o+u*(1-o),d=(o*n+u*s*(1-o))/c,g=(o*r+u*a*(1-o))/c,h=(o*i+u*l*(1-o))/c;return`rgba(${d}, ${g}, ${h}, ${c})`}function Os(e){if(e==="transparent")return 0;function t(o){const s=o/255;return s<=.04045?s/12.92:Math.pow((s+.055)/1.055,2.4)}const[n,r,i]=fo(e);return .2126*t(n)+.7152*t(r)+.0722*t(i)}var wi=new Map,Ci=new Map,Ps=new Map;function Jp(){wi.clear(),Ps.clear(),Ci.clear()}function Qp(e,t,n,r,i){var o,s,a;let l=0,u={};for(let d of e)l+=(o=n.get(d))!=null?o:i,u[d]=((s=u[d])!=null?s:0)+1;let c=t-l;for(let d of Object.keys(u)){let g=u[d],h=(a=n.get(d))!=null?a:i,p=h*g/l,m=c*p*r/g,y=h+m;n.set(d,y)}}function em(e,t){var n;let r=new Map,i=0;for(let u of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let c=e.measureText(u).width;r.set(u,c),i+=c}let o=i/r.size,s=3,a=(t/o+s)/(s+1),l=r.keys();for(let u of l)r.set(u,((n=r.get(u))!=null?n:o)*a);return r}function ao(e,t,n,r){var i,o;let s=Ci.get(n);if(r&&s!==void 0&&s.count>2e4){let u=Ps.get(n);if(u===void 0&&(u=em(e,s.size),Ps.set(n,u)),s.count>5e5){let d=0;for(let g of t)d+=(i=u.get(g))!=null?i:s.size;return d*1.01}let c=e.measureText(t);return Qp(t,c.width,u,Math.max(.05,1-s.count/2e5),s.size),Ci.set(n,{count:s.count+t.length,size:s.size}),c.width}let a=e.measureText(t),l=a.width/t.length;if(((o=s?.count)!=null?o:0)>2e4)return a.width;if(s===void 0)Ci.set(n,{count:t.length,size:l});else{let u=l-s.size,c=t.length/(s.count+t.length),d=s.size+u*c;Ci.set(n,{count:s.count+t.length,size:d})}return a.width}function tm(e,t,n,r,i,o,s,a){if(t.length<=1)return t.length;if(i<n)return-1;let l=Math.floor(n/i*o),u=ao(e,t.slice(0,Math.max(0,l)),r,s);if(u!==n)if(u<n){for(;u<n;)l++,u=ao(e,t.slice(0,Math.max(0,l)),r,s);l--}else for(;u>n;){let c=t.lastIndexOf(" ",l-1);c>0?l=c:l--,u=ao(e,t.slice(0,Math.max(0,l)),r,s)}if(t[l]!==" "){let c=0;c=t.lastIndexOf(" ",l),c>0&&(l=c)}return l}function nm(e,t,n,r,i,o){let s=`${t}_${n}_${r}px`,a=wi.get(s);if(a!==void 0)return a;if(r<=0)return[];let l=[],u=t.split(`
`),c=Ci.get(n),d=c===void 0?t.length:r/c.size*1.5,g=i&&c!==void 0&&c.count>2e4;for(let h of u){let p=ao(e,h.slice(0,Math.max(0,d)),n,g),m=Math.min(h.length,d);if(p<=r)l.push(h);else{for(;p>r;){let y=tm(e,h,r,n,p,m,g),b=h.slice(0,Math.max(0,y));h=h.slice(b.length),l.push(b),p=ao(e,h.slice(0,Math.max(0,d)),n,g),m=Math.min(h.length,d)}p>0&&l.push(h)}}return l=l.map((h,p)=>p===0?h.trimEnd():h.trim()),wi.set(s,l),wi.size>500&&wi.delete(wi.keys().next().value),l}function rm(e,t){return $t.useMemo(()=>e.map((n,r)=>({group:n.group,grow:n.grow,hasMenu:n.hasMenu,icon:n.icon,id:n.id,menuIcon:n.menuIcon,overlayIcon:n.overlayIcon,sourceIndex:r,sticky:r<t,indicatorIcon:n.indicatorIcon,style:n.style,themeOverride:n.themeOverride,title:n.title,trailingRowOptions:n.trailingRowOptions,width:n.width,growOffset:n.growOffset,rowMarker:n.rowMarker,rowMarkerChecked:n.rowMarkerChecked,headerRowMarkerTheme:n.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:n.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:n.headerRowMarkerDisabled})),[e,t])}function im(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(e.current!==void 0){if(so(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const o of i)if(n>=o.x&&n<o.x+o.width&&r>=o.y&&r<o.y+o.height)return!0}return!1}function ho(e,t){return(e??"")===(t??"")}function om(e,t,n){return n.current===void 0||e[1]!==n.current.cell[1]?!1:t.span===void 0?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]}function xd(e,t){const[n,r]=e;return n>=t.x&&n<t.x+t.width&&r>=t.y&&r<t.y+t.height}function so(e,t){return e?.[0]===t?.[0]&&e?.[1]===t?.[1]}function kd(e){return[e.x+e.width-1,e.y+e.height-1]}function ku(e,t,n){const r=n.x,i=n.x+n.width-1,o=n.y,s=n.y+n.height-1,[a,l]=e;if(l<o||l>s)return!1;if(t.span===void 0)return a>=r&&a<=i;const[u,c]=t.span;return u>=r&&u<=i||c>=r&&u<=i||u<r&&c>i}function am(e,t,n,r){let i=0;if(n.current===void 0)return i;const o=n.current.range;(r||o.height*o.width>1)&&ku(e,t,o)&&i++;for(const s of n.current.rangeStack)ku(e,t,s)&&i++;return i}function Md(e,t){let n=e;if(t!==void 0){let r=[...e];const i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),r=r.map((o,s)=>({...o,sticky:e[s].sticky})),n=r}return n}function Ri(e,t){let n=0;const r=Md(e,t);for(let i=0;i<r.length;i++){const o=r[i];if(o.sticky)n+=o.width;else break}return n}function Qr(e,t,n){if(typeof n=="number")return t*n;{let r=0;for(let i=e-t;i<e;i++)r+=n(i);return r}}function Ls(e,t,n,r,i){const o=Md(e,r),s=[];for(const u of o)if(u.sticky)s.push(u);else break;if(s.length>0)for(const u of s)n-=u.width;let a=t,l=i??0;for(;l<=n&&a<o.length;)l+=o[a].width,a++;for(let u=t;u<a;u++){const c=o[u];c.sticky||s.push(c)}return s}function sm(e,t,n){let r=0;for(const i of t){const o=i.sticky?r:r+(n??0);if(e<=o+i.width)return i.sourceIndex;r+=i.width}return-1}function lm(e,t,n,r,i,o,s,a,l,u){const c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;let d=t;for(let p=0;p<u;p++){const m=o-1-p,y=typeof s=="number"?s:s(m);if(d-=y,e>=d)return m}const g=o-u,h=e-(l??0);if(typeof s=="number"){const p=Math.floor((h-c)/s)+a;return p>=g?void 0:p}else{let p=c;for(let m=a;m<g;m++){const y=s(m);if(h<=p+y)return m;p+=y}return}}let na=0,lo={};const um=typeof window>"u";async function cm(){um||document?.fonts?.ready===void 0||(await document.fonts.ready,na=0,lo={},Jp())}cm();function Rd(e,t,n,r){return`${e}_${r??t?.font}_${n}`}function Fr(e,t,n,r="middle"){const i=Rd(e,t,r,n);let o=lo[i];return o===void 0&&(o=t.measureText(e),lo[i]=o,na++),na>1e4&&(lo={},na=0),o}function Id(e,t){const n=Rd(e,void 0,"middle",t);return lo[n]}function rr(e,t){return typeof t!="string"&&(t=t.baseFontFull),dm(e,t)}function Mu(e,t){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ";e.save(),e.textBaseline=t;const r=e.measureText(n);return e.restore(),r}const Ru=[];function dm(e,t){for(const o of Ru)if(o.key===t)return o.val;const n=Mu(e,"alphabetic"),i=-(Mu(e,"middle").actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return Ru.push({key:t,val:i}),i}function fm(e,t,n,r,i,o){const{ctx:s,rect:a,theme:l}=e;let u=Number.MAX_SAFE_INTEGER;const c=500;if(t!==void 0&&(u=n-t,u<c)){const d=1-u/c;s.globalAlpha=d,s.fillStyle=l.bgSearchResult,s.fillRect(a.x+1,a.y+1,a.width-(i?2:1),a.height-(o?2:1)),s.globalAlpha=1,r!==void 0&&(r.fillStyle=l.bgSearchResult)}return u<c}function yo(e,t,n){const{ctx:r,theme:i}=e,o=t??{},s=n??i.textDark;return s!==o.fillStyle&&(r.fillStyle=s,o.fillStyle=s),o}function Qs(e,t,n,r,i){const{rect:o,ctx:s,theme:a}=e;s.fillStyle=a.textDark,lr({ctx:s,rect:o,theme:a},t,n,r,i)}function Ed(e,t,n,r,i,o,s,a,l){l==="right"?e.fillText(t,n+i-(a.cellHorizontalPadding+.5),r+o/2+s):l==="center"?e.fillText(t,n+i/2,r+o/2+s):e.fillText(t,n+a.cellHorizontalPadding+.5,r+o/2+s)}function el(e,t){const n=Fr("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function hm(e,t){e.includes(`
`)&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}function gm(e,t,n,r,i,o,s,a,l,u){const c=a.baseFontFull,d=nm(e,t,c,i-a.cellHorizontalPadding*2,u??!1),g=el(e,c),h=a.lineHeight*g,p=g+h*(d.length-1),m=p+a.cellVerticalPadding>o;m&&(e.save(),e.rect(n,r,i,o),e.clip());const y=r+o/2-p/2;let b=Math.max(r+a.cellVerticalPadding,y);for(const v of d)if(Ed(e,v,n,b,i,g,s,a,l),b+=h,b>r+o)break;m&&e.restore()}function lr(e,t,n,r,i){const{ctx:o,rect:s,theme:a}=e,{x:l,y:u,width:c,height:d}=s;r=r??!1,r||(t=hm(t,c));const g=rr(o,a),h=Ks(t)==="rtl";if(n===void 0&&h&&(n="right"),h&&(o.direction="rtl"),t.length>0){let p=!1;n==="right"?(o.textAlign="right",p=!0):n!==void 0&&n!=="left"&&(o.textAlign=n,p=!0),r?gm(o,t,l,u,c,d,g,a,n,i):Ed(o,t,l,u,c,d,g,a,n),p&&(o.textAlign="start"),h&&(o.direction="inherit")}}function tr(e,t,n,r,i,o){typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.max(0,Math.min(o.tl,i/2,r/2)),tr:Math.max(0,Math.min(o.tr,i/2,r/2)),bl:Math.max(0,Math.min(o.bl,i/2,r/2)),br:Math.max(0,Math.min(o.br,i/2,r/2))},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}function pm(e,t,n){e.arc(t,n-1.25*3.5,1.25,0,2*Math.PI,!1),e.arc(t,n,1.25,0,2*Math.PI,!1),e.arc(t,n******3.5,1.25,0,2*Math.PI,!1)}function mm(e,t,n){const r=function(a,l){const u=l.x-a.x,c=l.y-a.y,d=Math.sqrt(u*u+c*c),g=u/d,h=c/d;return{x:u,y:l.y-a.y,len:d,nx:g,ny:h,ang:Math.atan2(h,g)}};let i;const o=t.length;let s=t[o-1];for(let a=0;a<o;a++){let l=t[a%o];const u=t[(a+1)%o],c=r(l,s),d=r(l,u),g=c.nx*d.ny-c.ny*d.nx,h=c.nx*d.nx-c.ny*-d.ny;let p=Math.asin(g<-1?-1:g>1?1:g),m=1,y=!1;h<0?p<0?p=Math.PI+p:(p=Math.PI-p,m=-1,y=!0):p>0&&(m=-1,y=!0),i=l.radius!==void 0?l.radius:n;const b=p/2;let v=Math.abs(Math.cos(b)*i/Math.sin(b)),C;v>Math.min(c.len/2,d.len/2)?(v=Math.min(c.len/2,d.len/2),C=Math.abs(v*Math.sin(b)/Math.cos(b))):C=i;let E=l.x+d.nx*v,I=l.y+d.ny*v;E+=-d.ny*C*m,I+=d.nx*C*m,e.arc(E,I,C,c.ang+Math.PI/2*m,d.ang-Math.PI/2*m,y),s=l,l=u}e.closePath()}function Fs(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p){const m={x:0,y:o+u,width:0,height:0};if(e>=h.length||t>=c||t<-2||e<0)return m;const y=o-i;if(e>=d){const b=s>e?-1:1,v=Ri(h);m.x+=v+l;for(let C=s;C!==e;C+=b)m.x+=h[b===1?C:C-1].width*b}else for(let b=0;b<e;b++)m.x+=h[b].width;if(m.width=h[e].width+1,t===-1)m.y=i,m.height=y;else if(t===-2){m.y=0,m.height=i;let b=e;const v=h[e].group,C=h[e].sticky;for(;b>0&&ho(h[b-1].group,v)&&h[b-1].sticky===C;){const I=h[b-1];m.x-=I.width,m.width+=I.width,b--}let E=e;for(;E+1<h.length&&ho(h[E+1].group,v)&&h[E+1].sticky===C;){const I=h[E+1];m.width+=I.width,E++}if(!C){const I=Ri(h),R=m.x-I;R<0&&(m.x-=R,m.width+=R),m.x+m.width>n&&(m.width=n-m.x)}}else if(t>=c-g){let b=c-t;for(m.y=r;b>0;){const v=t+b-1;m.height=typeof p=="number"?p:p(v),m.y-=m.height,b--}m.height+=1}else{const b=a>t?-1:1;if(typeof p=="number"){const v=t-a;m.y+=v*p}else for(let v=a;v!==t;v+=b)m.y+=p(v)*b;m.height=(typeof p=="number"?p:p(t))+1}return m}const tl=1<<21;function er(e,t){return(t+2)*tl+e}function Td(e){return e%tl}function nl(e){return Math.floor(e/tl)-2}function rl(e){const t=Td(e),n=nl(e);return[t,n]}class Dd{visibleWindow={x:0,y:0,width:0,height:0};freezeCols=0;freezeRows=[];isInWindow=t=>{const n=Td(t),r=nl(t),i=this.visibleWindow,o=n>=i.x&&n<=i.x+i.width||n<this.freezeCols,s=r>=i.y&&r<=i.y+i.height||this.freezeRows.includes(r);return o&&s};setWindow(t,n,r){this.visibleWindow.x===t.x&&this.visibleWindow.y===t.y&&this.visibleWindow.width===t.width&&this.visibleWindow.height===t.height&&this.freezeCols===n&&Mi(this.freezeRows,r)||(this.visibleWindow=t,this.freezeCols=n,this.freezeRows=r,this.clearOutOfWindow())}}class vm extends Dd{cache=new Map;setValue=(t,n)=>{this.cache.set(er(t[0],t[1]),n)};getValue=t=>this.cache.get(er(t[0],t[1]));clearOutOfWindow=()=>{for(const[t]of this.cache.entries())this.isInWindow(t)||this.cache.delete(t)}}class uo{cells;constructor(t=[]){this.cells=new Set(t.map(n=>er(n[0],n[1])))}add(t){this.cells.add(er(t[0],t[1]))}has(t){return t===void 0?!1:this.cells.has(er(t[0],t[1]))}remove(t){this.cells.delete(er(t[0],t[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const t of this.cells)if(nl(t)<0)return!0;return!1}hasItemInRectangle(t){for(let n=t.y;n<t.y+t.height;n++)for(let r=t.x;r<t.x+t.width;r++)if(this.cells.has(er(r,n)))return!0;return!1}hasItemInRegion(t){for(const n of t)if(this.hasItemInRectangle(n))return!0;return!1}*values(){for(const t of this.cells)yield rl(t)}}function bm(e){return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":e.textGroupHeader??e.textHeader,"--gdg-bg-group-header":e.bgGroupHeader??e.bgHeader,"--gdg-bg-group-header-hovered":e.bgGroupHeaderHovered??e.bgHeaderHovered,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bubble-height":`${e.bubbleHeight}px`,"--gdg-bubble-padding":`${e.bubblePadding}px`,"--gdg-bubble-margin":`${e.bubbleMargin}px`,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":e.horizontalBorderColor??e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":`${e.cellHorizontalPadding}px`,"--gdg-cell-vertical-padding":`${e.cellVerticalPadding}px`,"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,"--gdg-checkbox-max-size":`${e.checkboxMaxSize}px`,...e.resizeIndicatorColor===void 0?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...e.headerBottomBorderColor===void 0?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...e.roundingRadius===void 0?{}:{"--gdg-rounding-radius":`${e.roundingRadius}px`}}}const Od={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bubbleHeight:20,bubblePadding:6,bubbleMargin:4,bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4,checkboxMaxSize:18};function Pd(){return Od}const Ld=$t.createContext(Od);function Fd(){return $t.useContext(Ld)}function pr(e,...t){const n={...e};for(const r of t)if(r!==void 0)for(const i in r)r.hasOwnProperty(i)&&(i==="bgCell"?n[i]=Vn(r[i],n[i]):n[i]=r[i]);return(n.headerFontFull===void 0||e.fontFamily!==n.fontFamily||e.headerFontStyle!==n.headerFontStyle)&&(n.headerFontFull=`${n.headerFontStyle} ${n.fontFamily}`),(n.baseFontFull===void 0||e.fontFamily!==n.fontFamily||e.baseFontStyle!==n.baseFontStyle)&&(n.baseFontFull=`${n.baseFontStyle} ${n.fontFamily}`),(n.markerFontFull===void 0||e.fontFamily!==n.fontFamily||e.markerFontStyle!==n.markerFontStyle)&&(n.markerFontFull=`${n.markerFontStyle} ${n.fontFamily}`),n}const _s=150;function wm(e,t,n,r){return r(t)?.measure?.(e,t,n)??_s}function _d(e,t,n,r,i,o,s,a,l){let u=0;const c=i===void 0?[]:i.map(h=>{const p=wm(e,h[r],t,l);return u=Math.max(u,p),p});if(c.length>5&&a){u=0;let h=0;for(const m of c)h+=m;const p=h/c.length;for(let m=0;m<c.length;m++)c[m]>=p*2?c[m]=0:u=Math.max(u,c[m])}const d=e.font;e.font=t.headerFontFull,u=Math.max(u,e.measureText(n.title).width+t.cellHorizontalPadding*2+(n.icon===void 0?0:28)),e.font=d;const g=Math.max(Math.ceil(o),Math.min(Math.floor(s),Math.ceil(u)));return{...n,width:g}}function ym(e,t,n,r,i,o,s,a,l){const u=f.useRef(t),c=f.useRef(n),d=f.useRef(s);u.current=t,c.current=n,d.current=s;const[g,h]=f.useMemo(()=>{if(typeof window>"u")return[null,null];const v=document.createElement("canvas");return v.style.display="none",v.style.opacity="0",v.style.position="fixed",[v,v.getContext("2d",{alpha:!1})]},[]);f.useLayoutEffect(()=>(g&&document.documentElement.append(g),()=>{g?.remove()}),[g]);const p=f.useRef({}),m=f.useRef(),[y,b]=f.useState();return f.useLayoutEffect(()=>{const v=c.current;if(v===void 0||e.every($o))return;let C=Math.max(1,10-Math.floor(e.length/1e4)),E=0;C<u.current&&C>1&&(C--,E=1);const I={x:0,y:0,width:e.length,height:Math.min(u.current,C)},R={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const x=v(I,l.signal),S=E>0?v(R,l.signal):void 0;let F;typeof x=="object"?F=x:F=await jl(x),S!==void 0&&(typeof S=="object"?F=[...F,...S]:F=[...F,...await jl(S)]),m.current=e,b(F)})()},[l.signal,e]),f.useMemo(()=>{let C=e.every($o)?e:h===null?e.map(P=>$o(P)?P:{...P,width:_s}):(h.font=d.current.baseFontFull,e.map((P,x)=>{if($o(P))return P;if(p.current[P.id]!==void 0)return{...P,width:p.current[P.id]};if(y===void 0||m.current!==e||P.id===void 0)return{...P,width:_s};const S=_d(h,s,P,x,y,i,o,!0,a);return p.current[P.id]=S.width,S})),E=0,I=0;const R=[];for(const[P,x]of C.entries())E+=x.width,x.grow!==void 0&&x.grow>0&&(I+=x.grow,R.push(P));if(E<r&&R.length>0){const P=[...C],x=r-E;let S=x;for(let F=0;F<R.length;F++){const D=R[F],M=(C[D].grow??0)/I,T=F===R.length-1?S:Math.min(S,Math.floor(x*M));P[D]={...C[D],growOffset:T,width:C[D].width+T},S-=T}C=P}return{sizedColumns:C,nonGrowWidth:E}},[r,e,h,y,s,i,o,a])}var is,Iu;function Cm(){if(Iu)return is;Iu=1;function e(t,n,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),n!==void 0&&(t=t>=n?t:n)),t}return is=e,is}var os,Eu;function Sm(){if(Eu)return os;Eu=1;var e=Cm(),t=Nc();function n(r,i,o){return o===void 0&&(o=i,i=void 0),o!==void 0&&(o=t(o),o=o===o?o:0),i!==void 0&&(i=t(i),i=i===i?i:0),e(t(r),i,o)}return os=n,os}var xm=Sm();const Gn=vr(xm);var as,Tu;function km(){if(Tu)return as;Tu=1;function e(){}return as=e,as}var ss,Du;function Mm(){if(Du)return ss;Du=1;var e=Fh(),t=km(),n=Bc(),r=1/0,i=e&&1/n(new e([,-0]))[1]==r?function(o){return new e(o)}:t;return ss=i,ss}var ls,Ou;function Rm(){if(Ou)return ls;Ou=1;var e=_h(),t=Ah(),n=zh(),r=Hh(),i=Mm(),o=Bc(),s=200;function a(l,u,c){var d=-1,g=t,h=l.length,p=!0,m=[],y=m;if(c)p=!1,g=n;else if(h>=s){var b=u?null:i(l);if(b)return o(b);p=!1,g=r,y=new e}else y=u?[]:m;e:for(;++d<h;){var v=l[d],C=u?u(v):v;if(v=c||v!==0?v:0,p&&C===C){for(var E=y.length;E--;)if(y[E]===C)continue e;u&&y.push(C),m.push(v)}else g(y,C,c)||(y!==m&&y.push(C),m.push(v))}return m}return ls=a,ls}var us,Pu;function Im(){if(Pu)return us;Pu=1;var e=Rm();function t(n){return n&&n.length?e(n):[]}return us=t,us}var Em=Im();const Tm=vr(Em);var Dm=Vh();const Lu=vr(Dm),Nt='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',Om=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}<rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="${t}"/></svg>`},Pm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}<rect x="2" y="2" width="16" height="16" rx="4" fill="${n}"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="${t}"/></svg>`},Lm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="${t}"/>
  </svg>`},Fm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="${t}"/>
</svg>`},_m=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
    <path
        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"
        fill="${n}"
    />
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"
        fill="${t}"
    />
</svg>`},Ad=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="${t}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="${t}"/>
</svg>
  `},Am=e=>{const t=e.bgColor;return`${Nt}
    <path stroke="${t}" stroke-width="2" d="M12 3v14"/>
    <path stroke="${t}" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>
    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="${t}"/>
  </svg>
`},Hm=Ad,zm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="${t}"/>
</svg>`},Vm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="${t}"/>
    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="${t}"/>
    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
  </svg>`},$m=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="${t}"/>
</svg>`},Nm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <path fill="${t}" d="M3 3h14v14H3z"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="${n}"/>
  </svg>`},Bm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="${t}"/>
  </svg>`},Wm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="${t}"/>
</svg>`},Um=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="${t}"/>
    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="${t}"/>
  </svg>`},qm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="${t}"/>
</svg>`},Gm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <rect x="2" y="8" width="10" height="8" rx="2" fill="${n}"/>
    <rect x="8" y="4" width="10" height="8" rx="2" fill="${n}"/>
    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="${t}"/>
  </svg>`},Xm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path fill="${t}" d="M4 3h12v14H4z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="${n}"/>
</svg>`},Ym=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="${t}"/>
  </svg>`},jm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="${t}"/>
  </svg>`},Km=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="${t}"/>
</svg>`},Zm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="${t}"/>
  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="${t}"/>
</svg>`},Jm=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 1.4.7.7 0 0 1 0-1.4z" fill="${t}"/>
  </svg>`},Qm=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="${t}"/>
</svg>`},e0=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${Nt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="${t}"/>
  </svg>`},t0=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="${t}"/>
</svg>`},n0=e=>{const t=e.fgColor,n=e.bgColor;return`${Nt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="${t}"/>
</svg>`},r0=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>`},i0=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>
`},o0={headerRowID:Om,headerNumber:Lm,headerCode:Pm,headerString:Fm,headerBoolean:_m,headerAudioUri:Hm,headerVideoUri:zm,headerEmoji:Vm,headerImage:$m,headerUri:Ad,headerPhone:Nm,headerMarkdown:Bm,headerDate:Wm,headerTime:Um,headerEmail:qm,headerReference:Gm,headerIfThenElse:Xm,headerSingleValue:Ym,headerLookup:jm,headerTextTemplate:Km,headerMath:Zm,headerRollup:Jm,headerJoinStrings:Qm,headerSplitString:e0,headerGeoDistance:t0,headerArray:n0,rowOwnerOverlay:r0,protectedColumnOverlay:i0,renameIcon:Am};function a0(e,t){return e==="normal"?[t.bgIconHeader,t.fgIconHeader]:e==="selected"?["white",t.accentColor]:[t.accentColor,t.bgHeader]}class s0{onSettled;spriteMap=new Map;headerIcons;inFlight=0;constructor(t,n){this.onSettled=n,this.headerIcons=t??{}}drawSprite(t,n,r,i,o,s,a,l=1){const[u,c]=a0(n,a),d=s*Math.ceil(window.devicePixelRatio),g=`${u}_${c}_${d}_${t}`;let h=this.spriteMap.get(g);if(h===void 0){const p=this.headerIcons[t];if(p===void 0)return;h=document.createElement("canvas");const m=h.getContext("2d");if(m===null)return;const y=new Image;y.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(p({fgColor:c,bgColor:u}))}`,this.spriteMap.set(g,h);const b=y.decode();if(b===void 0)return;this.inFlight++,b.then(()=>{m.drawImage(y,0,0,d,d)}).finally(()=>{this.inFlight--,this.inFlight===0&&this.onSettled()})}else l<1&&(r.globalAlpha=l),r.drawImage(h,0,0,d,d,i,o,s,s),l<1&&(r.globalAlpha=1)}}function Hd(e){if(e.length===0)return;let t;for(const n of e)t=Math.min(t??n.y,n.y);return t}function Ra(e,t,n,r,i,o,s,a,l){a=a??t;let u=t,c=e;const d=r-o;let g=!1;for(;u<n&&c<d;){const h=i(c);if(u+h>a&&l(u,c,h,!1,s&&c===r-1)===!0){g=!0;break}u+=h,c++}if(!g){u=n;for(let h=0;h<o;h++){c=r-1-h;const p=i(c);u-=p,l(u,c,p,!0,s&&c===r-1)}}}function _r(e,t,n,r,i,o){let s=0,a=0;const l=i+r;for(const u of e){const c=u.sticky?a:s+n;if(o(u,c,l,u.sticky?0:a,t)===!0)break;s+=u.width,a+=u.sticky?u.width:0}}function zd(e,t,n,r,i){let o=0,s=0;for(let a=0;a<e.length;a++){const l=e[a];let u=a+1,c=l.width;for(l.sticky&&(s+=c);u<e.length&&ho(e[u].group,l.group)&&e[u].sticky===e[a].sticky;){const m=e[u];c+=m.width,u++,a++,m.sticky&&(s+=m.width)}const d=l.sticky?0:n,g=o+d,h=l.sticky?0:Math.max(0,s-g),p=Math.min(c-h,t-(g+h));i([l.sourceIndex,e[u-1].sourceIndex],l.group??"",g+h,0,p,r),o+=c}}function Vd(e,t,n,r,i,o,s){const[a,l]=e;let u,c;const d=s.find(g=>!g.sticky)?.sourceIndex??0;if(l>d){const g=Math.max(a,d);let h=t,p=r;for(let m=o.sourceIndex-1;m>=g;m--)h-=s[m].width,p+=s[m].width;for(let m=o.sourceIndex+1;m<=l;m++)p+=s[m].width;c={x:h,y:n,width:p,height:i}}if(d>a){const g=Math.min(l,d-1);let h=t,p=r;for(let m=o.sourceIndex-1;m>=a;m--)h-=s[m].width,p+=s[m].width;for(let m=o.sourceIndex+1;m<=g;m++)p+=s[m].width;u={x:h,y:n,width:p,height:i}}return[u,c]}function l0(e,t,n,r){if(r==="any")return $d(e,{x:t,y:n,width:1,height:1});if(r==="vertical"&&(t=e.x),r==="horizontal"&&(n=e.y),xd([t,n],e))return;const i=t-e.x,o=e.x+e.width-t,s=n-e.y+1,a=e.y+e.height-n,l=Math.min(r==="vertical"?Number.MAX_SAFE_INTEGER:i,r==="vertical"?Number.MAX_SAFE_INTEGER:o,r==="horizontal"?Number.MAX_SAFE_INTEGER:s,r==="horizontal"?Number.MAX_SAFE_INTEGER:a);return l===a?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:l===s?{x:e.x,y:n,width:e.width,height:e.y-n}:l===o?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}function go(e,t,n,r,i,o,s,a){return e<=i+s&&i<=e+n&&t<=o+a&&o<=t+r}function Yr(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function $d(e,t){const n=Math.min(e.x,t.x),r=Math.min(e.y,t.y),i=Math.max(e.x+e.width,t.x+t.width)-n,o=Math.max(e.y+e.height,t.y+t.height)-r;return{x:n,y:r,width:i,height:o}}function u0(e,t){return e.x<=t.x&&e.y<=t.y&&e.x+e.width>=t.x+t.width&&e.y+e.height>=t.y+t.height}function c0(e,t,n,r){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=-4,o=-4,s=t+4,a=n+4,l=i-e.x,u=e.x+e.width-s,c=o-e.y,d=e.y+e.height-a,g=l>0?e.x+Math.floor(l/r)*r:e.x,h=u>0?e.x+e.width-Math.floor(u/r)*r:e.x+e.width,p=c>0?e.y+Math.floor(c/r)*r:e.y,m=d>0?e.y+e.height-Math.floor(d/r)*r:e.y+e.height;return{x:g,y:p,width:h-g,height:m-p}}function d0(e,t,n,r,i){const[o,s,a,l]=t,[u,c,d,g]=i,{x:h,y:p,width:m,height:y}=e,b=[];if(m<=0||y<=0)return b;const v=h+m,C=p+y,E=h<o,I=p<s,R=h+m>a,P=p+y>l,x=h>=o&&h<a||v>o&&v<=a||h<o&&v>a,S=p>=s&&p<l||C>s&&C<=l||p<s&&C>l;if(x&&S){const D=Math.max(h,o),M=Math.max(p,s),T=Math.min(v,a),O=Math.min(C,l);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:u,y:c,width:d-u+1,height:g-c+1}})}if(E&&I){const D=h,M=p,T=Math.min(v,o),O=Math.min(C,s);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:0,y:0,width:u+1,height:c+1}})}if(I&&x){const D=Math.max(h,o),M=p,T=Math.min(v,a),O=Math.min(C,s);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:u,y:0,width:d-u+1,height:c+1}})}if(I&&R){const D=Math.max(h,a),M=p,T=v,O=Math.min(C,s);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:d,y:0,width:n-d+1,height:c+1}})}if(E&&S){const D=h,M=Math.max(p,s),T=Math.min(v,o),O=Math.min(C,l);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:0,y:c,width:u+1,height:g-c+1}})}if(R&&S){const D=Math.max(h,a),M=Math.max(p,s),T=v,O=Math.min(C,l);b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:d,y:c,width:n-d+1,height:g-c+1}})}if(E&&P){const D=h,M=Math.max(p,l),T=Math.min(v,o),O=C;b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:0,y:g,width:u+1,height:r-g+1}})}if(P&&x){const D=Math.max(h,o),M=Math.max(p,l),T=Math.min(v,a),O=C;b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:u,y:g,width:d-u+1,height:r-g+1}})}if(R&&P){const D=Math.max(h,a),M=Math.max(p,l),T=v,O=C;b.push({rect:{x:D,y:M,width:T-D,height:O-M},clip:{x:d,y:g,width:n-d+1,height:r-g+1}})}return b}const f0={kind:Z.Loading,allowOverlay:!1};function Fu(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b,v,C,E,I,R,P,x,S,F,D,M,T,O,k,$,G,j){let ue=C?.size??Number.MAX_SAFE_INTEGER;const Q=performance.now();let ee=T.baseFontFull;e.font=ee;const ce={ctx:e},ae=[0,0],ge=y>0?Qr(l,y,u):0;let de,re;const H=Hd(v);return _r(t,a,o,s,i,(L,X,te,ie,Se)=>{const me=Math.max(0,ie-X),nt=X+me,xe=i+1,gt=L.width-me,Mt=r-i-1;if(v.length>0){let $e=!1;for(let Pe=0;Pe<v.length;Pe++){const it=v[Pe];if(go(nt,xe,gt,Mt,it.x,it.y,it.width,it.height)){$e=!0;break}}if(!$e)return}const Ue=()=>{e.save(),e.beginPath(),e.rect(nt,xe,gt,Mt),e.clip()},ne=E.columns.hasIndex(L.sourceIndex),oe=d(L.group??"").overrideTheme,we=L.themeOverride===void 0&&oe===void 0?T:pr(T,oe,L.themeOverride),fe=we.baseFontFull;fe!==ee&&(ee=fe,e.font=fe),Ue();let ye;return Ra(Se,te,r,l,u,y,b,H,($e,Pe,it,Me,qe)=>{if(Pe<0||(ae[0]=L.sourceIndex,ae[1]=Pe,C!==void 0&&!C.has(ae)))return;if(v.length>0){let Le=!1;for(let St=0;St<v.length;St++){const mt=v[St];if(go(X,$e,L.width,it,mt.x,mt.y,mt.width,mt.height)){Le=!0;break}}if(!Le)return}const Te=E.rows.hasIndex(Pe),Ee=h.hasIndex(Pe),De=Pe<l?c(ae):f0;let ft=X,ot=L.width,Xe=!1,wt=!1;if(De.span!==void 0){const[Le,St]=De.span,mt=`${Pe},${Le},${St},${L.sticky}`;if(re===void 0&&(re=new Set),re.has(mt)){ue--;return}else{const Dn=Vd(De.span,X,$e,L.width,it,L,n),Gt=L.sticky?Dn[0]:Dn[1];if(!L.sticky&&Dn[0]!==void 0&&(wt=!0),Gt!==void 0){ft=Gt.x,ot=Gt.width,re.add(mt),e.restore(),ye=void 0,e.save(),e.beginPath();const hn=Math.max(0,ie-Gt.x);e.rect(Gt.x+hn,$e,Gt.width-hn,it),de===void 0&&(de=[]),de.push({x:Gt.x+hn,y:$e,width:Gt.width-hn,height:it}),e.clip(),Xe=!0}}}const yt=g?.(Pe),Ft=qe&&L.trailingRowOptions?.themeOverride!==void 0?L.trailingRowOptions?.themeOverride:void 0,It=De.themeOverride===void 0&&yt===void 0&&Ft===void 0?we:pr(we,yt,Ft,De.themeOverride);e.beginPath();const dn=om(ae,De,E);let Zt=am(ae,De,E,m);const fn=De.span!==void 0&&E.columns.some(Le=>De.span!==void 0&&Le>=De.span[0]&&Le<=De.span[1]);dn&&!p&&m?Zt=0:dn&&m&&(Zt=Math.max(Zt,1)),fn&&Zt++,dn||(Te&&Zt++,ne&&!qe&&Zt++);const Ot=De.kind===Z.Protected?It.bgCellMedium:It.bgCell;let Ct;if((Me||Ot!==T.bgCell)&&(Ct=Vn(Ot,Ct)),Zt>0||Ee){Ee&&(Ct=Vn(It.bgHeader,Ct));for(let Le=0;Le<Zt;Le++)Ct=Vn(It.accentLight,Ct)}else if(I!==void 0){for(const Le of I)if(Le[0]===L.sourceIndex&&Le[1]===Pe){Ct=Vn(It.bgSearchResult,Ct);break}}if(R!==void 0)for(let Le=0;Le<R.length;Le++){const St=R[Le],mt=St.range;St.style!=="solid-outline"&&mt.x<=L.sourceIndex&&L.sourceIndex<mt.x+mt.width&&mt.y<=Pe&&Pe<mt.y+mt.height&&(Ct=Vn(St.color,Ct))}let Tn=!1;if(C!==void 0){const Le=$e+1,mt=(Me?Le+it-1:Math.min(Le+it-1,r-ge))-Le;(mt!==it-1||ft+1<=ie)&&(Tn=!0,e.save(),e.beginPath(),e.rect(ft+1,Le,ot-1,mt),e.clip()),Ct=Ct===void 0?It.bgCell:Vn(Ct,It.bgCell)}const pt=L.sourceIndex===n.length-1,en=Pe===l-1;Ct!==void 0&&(e.fillStyle=Ct,ye!==void 0&&(ye.fillStyle=Ct),C!==void 0?e.fillRect(ft+1,$e+1,ot-(pt?2:1),it-(en?2:1)):e.fillRect(ft,$e,ot,it)),De.style==="faded"&&(e.globalAlpha=.6);let Bt;for(let Le=0;Le<S.length;Le++){const St=S[Le];if(St.item[0]===L.sourceIndex&&St.item[1]===Pe){Bt=St;break}}if(ot>j&&!wt){const Le=It.baseFontFull;Le!==ee&&(e.font=Le,ee=Le),ye=Nd(e,De,L.sourceIndex,Pe,pt,en,ft,$e,ot,it,Zt>0,It,Ct??It.bgCell,P,x,Bt?.hoverAmount??0,F,M,Q,D,ye,O,k,$,G)}return Tn&&e.restore(),De.style==="faded"&&(e.globalAlpha=1),ue--,Xe&&(e.restore(),ye?.deprep?.(ce),ye=void 0,Ue(),ee=fe,e.font=fe),ue<=0}),e.restore(),ue<=0}),de}const Zi=[0,0],Ji={x:0,y:0,width:0,height:0},cs=[void 0,()=>{}];let As=!1;function h0(){As=!0}function Nd(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b,v,C,E,I,R,P,x){let S,F;y!==void 0&&y[0][0]===n&&y[0][1]===r&&(S=y[1][0],F=y[1][1]);let D;Zi[0]=n,Zi[1]=r,Ji.x=s,Ji.y=a,Ji.width=l,Ji.height=u,cs[0]=R.getValue(Zi),cs[1]=k=>R.setValue(Zi,k),As=!1;const M={ctx:e,theme:d,col:n,row:r,cell:t,rect:Ji,highlighted:c,cellFillColor:g,hoverAmount:m,frameTime:v,hoverX:S,drawState:cs,hoverY:F,imageLoader:h,spriteManager:p,hyperWrapping:b,overrideCursor:S!==void 0?x:void 0,requestAnimationFrame:h0},T=fm(M,t.lastUpdated,v,E,i,o),O=P(t);if(O!==void 0){E?.renderer!==O&&(E?.deprep?.(M),E=void 0);const k=O.drawPrep?.(M,E);C!==void 0&&!yi(M.cell)?C(M,()=>O.draw(M,t)):O.draw(M,t),D=k===void 0?void 0:{deprep:k?.deprep,fillStyle:k?.fillStyle,font:k?.font,renderer:O}}return(T||As)&&I?.(Zi),D}function il(e,t,n,r,i,o,s,a,l=-20,u=-20,c=void 0,d="center",g="square"){const h=Math.floor(i+s/2),p=g==="circle"?1e4:t.roundingRadius??4;let m=hd(c??t.checkboxMaxSize,s,t.cellVerticalPadding),y=m/2;const b=fd(d,r,o,t.cellHorizontalPadding,m),v=dd(b,h,m),C=gd(r+l,i+u,v);switch(n){case!0:{e.beginPath(),tr(e,b-m/2,h-m/2,m,m,p),g==="circle"&&(y*=.8,m*=.8),e.fillStyle=a?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(b-y+m/4.23,h-y+m/1.97),e.lineTo(b-y+m/2.42,h-y+m/1.44),e.lineTo(b-y+m/1.29,h-y+m/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break}case sa:case!1:{e.beginPath(),tr(e,b-m/2+.5,h-m/2+.5,m-1,m-1,p),e.lineWidth=1,e.strokeStyle=C?t.textDark:t.textMedium,e.stroke();break}case Gs:{e.beginPath(),tr(e,b-m/2,h-m/2,m,m,p),e.fillStyle=C?t.textMedium:t.textLight,e.fill(),g==="circle"&&(y*=.8,m*=.8),e.beginPath(),e.moveTo(b-m/3,h),e.lineTo(b+m/3,h),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break}default:co()}}function g0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b,v){const C=s+a;if(C<=0)return;e.fillStyle=d.bgHeader,e.fillRect(0,0,i,C);const E=r?.[0]?.[0],I=r?.[0]?.[1],R=r?.[1]?.[0],P=r?.[1]?.[1],x=d.headerFontFull;e.font=x,_r(t,0,o,0,C,(S,F,D,M)=>{if(y!==void 0&&!y.has([S.sourceIndex,-1]))return;const T=Math.max(0,M-F);e.save(),e.beginPath(),e.rect(F+T,a,S.width-T,s),e.clip();const O=m(S.group??"").overrideTheme,k=S.themeOverride===void 0&&O===void 0?d:pr(d,O,S.themeOverride);k.bgHeader!==d.bgHeader&&(e.fillStyle=k.bgHeader,e.fill()),k!==d&&(e.font=k.headerFontFull);const $=c.columns.hasIndex(S.sourceIndex),G=l!==void 0||u||S.headerRowMarkerDisabled===!0,j=!G&&I===-1&&E===S.sourceIndex,ue=G?0:h.find(ge=>ge.item[0]===S.sourceIndex&&ge.item[1]===-1)?.hoverAmount??0,Q=c?.current!==void 0&&c.current.cell[0]===S.sourceIndex,ee=$?k.accentColor:Q?k.bgHeaderHasFocus:k.bgHeader,ce=n?a:0,ae=S.sourceIndex===0?0:1;$?(e.fillStyle=ee,e.fillRect(F+ae,ce,S.width-ae,s)):(Q||ue>0)&&(e.beginPath(),e.rect(F+ae,ce,S.width-ae,s),Q&&(e.fillStyle=k.bgHeaderHasFocus,e.fill()),ue>0&&(e.globalAlpha=ue,e.fillStyle=k.bgHeaderHovered,e.fill(),e.globalAlpha=1)),Ud(e,F,ce,S.width,s,S,$,k,j,j?R:void 0,j?P:void 0,Q,ue,g,b,v),e.restore()}),n&&p0(e,t,i,o,a,r,d,g,h,p,m,y)}function p0(e,t,n,r,i,o,s,a,l,u,c,d){const[h,p]=o?.[0]??[];let m=0;zd(t,n,r,i,(y,b,v,C,E,I)=>{if(d!==void 0&&!d.hasItemInRectangle({x:y[0],y:-2,width:y[1]-y[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(v,C,E,I),e.clip();const R=c(b),P=R?.overrideTheme===void 0?s:pr(s,R.overrideTheme),x=p===-2&&h!==void 0&&h>=y[0]&&h<=y[1],S=x?P.bgGroupHeaderHovered??P.bgHeaderHovered:P.bgGroupHeader??P.bgHeader;if(S!==s.bgHeader&&(e.fillStyle=S,e.fill()),e.fillStyle=P.textGroupHeader??P.textHeader,R!==void 0){let F=v;if(R.icon!==void 0&&(a.drawSprite(R.icon,"normal",e,F+8,(i-20)/2,20,P),F+=26),e.fillText(R.name,F+8,i/2+rr(e,s.headerFontFull)),R.actions!==void 0&&x){const D=Bd({x:v,y:C,width:E,height:I},R.actions);e.beginPath();const M=D[0].x-10,T=v+E-M;e.rect(M,0,T,i);const O=e.createLinearGradient(M,0,M+T,0),k=jr(S,0);O.addColorStop(0,k),O.addColorStop(10/T,S),O.addColorStop(1,S),e.fillStyle=O,e.fill(),e.globalAlpha=.6;const[$,G]=o?.[1]??[-1,-1];for(let j=0;j<R.actions.length;j++){const ue=R.actions[j],Q=D[j],ee=Yr(Q,$+v,G);ee&&(e.globalAlpha=1),a.drawSprite(ue.icon,"normal",e,Q.x+Q.width/2-10,Q.y+Q.height/2-10,20,P),ee&&(e.globalAlpha=.6)}e.globalAlpha=1}}v!==0&&u(y[0])&&(e.beginPath(),e.moveTo(v+.5,0),e.lineTo(v+.5,i),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()),e.restore(),m=v+E}),e.beginPath(),e.moveTo(m+.5,0),e.lineTo(m+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=s.borderColor,e.lineWidth=1,e.stroke()}const qo=30;function m0(e,t,n,r,i){return{x:e+n-qo,y:Math.max(t,t+r/2-qo/2),width:qo,height:Math.min(qo,r)}}function Bd(e,t){const n=[];let r=e.x+e.width-26*t.length;const i=e.y+e.height/2-13,o=26,s=26;for(let a=0;a<t.length;a++)n.push({x:r,y:i,width:s,height:o}),r+=26;return n}function Qi(e,t,n){return!n||e===void 0||(e.x=t-(e.x-t)-e.width),e}function Wd(e,t,n,r,i,o,s,a){const l=s.cellHorizontalPadding,u=s.headerIconSize,c=m0(n,r,i,o);let d=n+l;const g=t.icon===void 0?void 0:{x:d,y:r+(o-u)/2,width:u,height:u},h=g===void 0||t.overlayIcon===void 0?void 0:{x:g.x+9,y:g.y+6,width:18,height:18};g!==void 0&&(d+=Math.ceil(u*1.3));const p={x:d,y:r,width:i-d,height:o};let m;if(t.indicatorIcon!==void 0){const b=e===void 0?Id(t.title,s.headerFontFull)?.width??0:Fr(t.title,e,s.headerFontFull).width;p.width=b,d+=b+l,m={x:d,y:r+(o-u)/2,width:u,height:u}}const y=n+i/2;return{menuBounds:Qi(c,y,a),iconBounds:Qi(g,y,a),iconOverlayBounds:Qi(h,y,a),textBounds:Qi(p,y,a),indicatorIconBounds:Qi(m,y,a)}}function _u(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m){if(o.rowMarker!==void 0&&o.headerRowMarkerDisabled!==!0){const v=o.rowMarkerChecked;v!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=d);const C=o.headerRowMarkerTheme!==void 0?pr(a,o.headerRowMarkerTheme):a;il(e,C,v,t,n,r,i,!1,void 0,void 0,a.checkboxMaxSize,"center",o.rowMarker),v!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=1);return}const y=s?a.textHeaderSelected:a.textHeader,b=o.hasMenu===!0&&(l||h&&s)&&m.menuBounds!==void 0;if(o.icon!==void 0&&m.iconBounds!==void 0){let v=s?"selected":"normal";o.style==="highlight"&&(v=s?"selected":"special"),g.drawSprite(o.icon,v,e,m.iconBounds.x,m.iconBounds.y,m.iconBounds.width,a),o.overlayIcon!==void 0&&m.iconOverlayBounds!==void 0&&g.drawSprite(o.overlayIcon,s?"selected":"special",e,m.iconOverlayBounds.x,m.iconOverlayBounds.y,m.iconOverlayBounds.width,a)}if(b&&r>35){const C=p?35:r-35,E=p?35*.7:r-35*.7,I=C/r,R=E/r,P=e.createLinearGradient(t,0,t+r,0),x=jr(y,0);P.addColorStop(p?1:0,y),P.addColorStop(I,y),P.addColorStop(R,x),P.addColorStop(p?0:1,x),e.fillStyle=P}else e.fillStyle=y;if(p&&(e.textAlign="right"),m.textBounds!==void 0&&e.fillText(o.title,p?m.textBounds.x+m.textBounds.width:m.textBounds.x,n+i/2+rr(e,a.headerFontFull)),p&&(e.textAlign="left"),o.indicatorIcon!==void 0&&m.indicatorIconBounds!==void 0&&(!b||!go(m.menuBounds.x,m.menuBounds.y,m.menuBounds.width,m.menuBounds.height,m.indicatorIconBounds.x,m.indicatorIconBounds.y,m.indicatorIconBounds.width,m.indicatorIconBounds.height))){let v=s?"selected":"normal";o.style==="highlight"&&(v=s?"selected":"special"),g.drawSprite(o.indicatorIcon,v,e,m.indicatorIconBounds.x,m.indicatorIconBounds.y,m.indicatorIconBounds.width,a)}if(b&&m.menuBounds!==void 0){const v=m.menuBounds,C=u!==void 0&&c!==void 0&&Yr(v,u+t,c+n);if(C||(e.globalAlpha=.7),o.menuIcon===void 0||o.menuIcon===la.Triangle){e.beginPath();const E=v.x+v.width/2-5.5,I=v.y+v.height/2-3;mm(e,[{x:E,y:I},{x:E+11,y:I},{x:E+5.5,y:I+6}],1),e.fillStyle=y,e.fill()}else if(o.menuIcon===la.Dots){e.beginPath();const E=v.x+v.width/2,I=v.y+v.height/2;pm(e,E,I),e.fillStyle=y,e.fill()}else{const E=v.x+(v.width-a.headerIconSize)/2,I=v.y+(v.height-a.headerIconSize)/2;g.drawSprite(o.menuIcon,"normal",e,E,I,a.headerIconSize,a)}C||(e.globalAlpha=1)}}function Ud(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m){const y=Ks(o.title)==="rtl",b=Wd(e,o,t,n,r,i,a,y);p!==void 0?p({ctx:e,theme:a,rect:{x:t,y:n,width:r,height:i},column:o,columnIndex:o.sourceIndex,isSelected:s,hoverAmount:g,isHovered:l,hasSelectedCell:d,spriteManager:h,menuBounds:b?.menuBounds??{x:0,y:0,height:0,width:0},hoverX:u,hoverY:c},()=>_u(e,t,n,r,i,o,s,a,l,u,c,g,h,m,y,b)):_u(e,t,n,r,i,o,s,a,l,u,c,g,h,m,y,b)}function v0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b,v){if(b!==void 0||t[t.length-1]!==n[t.length-1])return;const C=Hd(y);_r(t,l,s,a,o,(E,I,R,P,x)=>{if(E!==t[t.length-1])return;I+=E.width;const S=Math.max(I,P);S>r||(e.save(),e.beginPath(),e.rect(S,o+1,1e4,i-o-1),e.clip(),Ra(x,R,i,u,c,p,m,C,(F,D,M,T)=>{if(!T&&y.length>0&&!y.some(j=>go(I,F,1e4,M,j.x,j.y,j.width,j.height)))return;const O=g.hasIndex(D),k=h.hasIndex(D);e.beginPath();const $=d?.(D),G=$===void 0?v:pr(v,$);G.bgCell!==v.bgCell&&(e.fillStyle=G.bgCell,e.fillRect(I,F,1e4,M)),k&&(e.fillStyle=G.bgHeader,e.fillRect(I,F,1e4,M)),O&&(e.fillStyle=G.accentLight,e.fillRect(I,F,1e4,M))}),e.restore())})}function b0(e,t,n,r,i,o,s,a,l){let u=!1;for(const p of t)if(!p.sticky){u=s(p.sourceIndex);break}const c=l.horizontalBorderColor??l.borderColor,d=l.borderColor,g=u?Ri(t):0;let h;if(g!==0&&(h=xu(d,l.bgCell),e.beginPath(),e.moveTo(g+.5,0),e.lineTo(g+.5,r),e.strokeStyle=h,e.stroke()),i>0){const p=d===c&&h!==void 0?h:xu(c,l.bgCell),m=Qr(o,i,a);e.beginPath(),e.moveTo(0,r-m+.5),e.lineTo(n,r-m+.5),e.strokeStyle=p,e.stroke()}}const qd=(e,t,n)=>{let r=0,i=t,o=0,s=n;if(e!==void 0&&e.length>0){r=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;for(const a of e)r=Math.min(r,a.x-1),i=Math.max(i,a.x+a.width+1),o=Math.min(o,a.y-1),s=Math.max(s,a.y+a.height+1)}return{minX:r,maxX:i,minY:o,maxY:s}};function w0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p){const m=p.bgCell,{minX:y,maxX:b,minY:v,maxY:C}=qd(a,o,s),E=[],I=s-Qr(h,g,u);let R=l,P=n,x=0;for(;R+i<I;){const M=R+i,T=u(P);if(M>=v&&M<=C-1){const k=c?.(P)?.bgCell;k!==void 0&&k!==m&&P>=h-g&&E.push({x:y,y:M,w:b-y,h:T,color:k})}R+=T,P<h-g&&(x=R),P++}let S=0;const F=Math.min(I,C)-x;if(F>0)for(let M=0;M<t.length;M++){const T=t[M];if(T.width===0)continue;const O=T.sticky?S:S+r,k=T.themeOverride?.bgCell;k!==void 0&&k!==m&&O>=y&&O<=b&&d(M+1)&&E.push({x:O,y:x,w:T.width,h:F,color:k}),S+=T.width}if(E.length===0)return;let D;e.beginPath();for(let M=E.length-1;M>=0;M--){const T=E[M];D===void 0?D=T.color:T.color!==D&&(e.fillStyle=D,e.fill(),e.beginPath(),D=T.color),e.rect(T.x,T.y,T.w,T.h)}D!==void 0&&(e.fillStyle=D,e.fill()),e.beginPath()}function Au(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b=!1){if(l!==void 0){e.beginPath(),e.save(),e.rect(0,0,o,s);for(const M of l)e.rect(M.x+1,M.y+1,M.width-1,M.height-1);e.clip("evenodd")}const v=y.horizontalBorderColor??y.borderColor,C=y.borderColor,{minX:E,maxX:I,minY:R,maxY:P}=qd(a,o,s),x=[];e.beginPath();let S=.5;for(let M=0;M<t.length;M++){const T=t[M];if(T.width===0)continue;S+=T.width;const O=T.sticky?S:S+r;O>=E&&O<=I&&h(M+1)&&x.push({x1:O,y1:Math.max(u,R),x2:O,y2:Math.min(s,P),color:C})}let F=s+.5;for(let M=m-p;M<m;M++){const T=d(M);F-=T,x.push({x1:E,y1:F,x2:I,y2:F,color:v})}if(b!==!0){let M=c+.5,T=n;const O=F;for(;M+i<O;){const k=M+i;if(k>=R&&k<=P-1){const $=g?.(T);x.push({x1:E,y1:k,x2:I,y2:k,color:$?.horizontalBorderColor??$?.borderColor??v})}M+=d(T),T++}}const D=$h(x,M=>M.color);for(const M of Object.keys(D)){e.strokeStyle=M;for(const T of D[M])e.moveTo(T.x1,T.y1),e.lineTo(T.x2,T.y2);e.stroke(),e.beginPath()}l!==void 0&&e.restore()}function y0(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y,b,v){const C=[];e.imageSmoothingEnabled=!1;const E=Math.min(i.cellYOffset,s),I=Math.max(i.cellYOffset,s);let R=0;if(typeof b=="number")R+=(I-E)*b;else for(let O=E;O<I;O++)R+=b(O);s>i.cellYOffset&&(R=-R),R+=l-i.translateY;const P=Math.min(i.cellXOffset,o),x=Math.max(i.cellXOffset,o);let S=0;for(let O=P;O<x;O++)S+=m[O].width;o>i.cellXOffset&&(S=-S),S+=a-i.translateX;const F=Ri(y);if(S!==0&&R!==0)return{regions:[]};const D=u>0?Qr(g,u,b):0,M=c-F-Math.abs(S),T=d-h-D-Math.abs(R)-1;if(M>150&&T>150){const O={sx:0,sy:0,sw:c*p,sh:d*p,dx:0,dy:0,dw:c*p,dh:d*p};if(R>0?(O.sy=(h+1)*p,O.sh=T*p,O.dy=(R+h+1)*p,O.dh=T*p,C.push({x:0,y:h,width:c,height:R+1})):R<0&&(O.sy=(-R+h+1)*p,O.sh=T*p,O.dy=(h+1)*p,O.dh=T*p,C.push({x:0,y:d+R-D,width:c,height:-R+D})),S>0?(O.sx=F*p,O.sw=M*p,O.dx=(S+F)*p,O.dw=M*p,C.push({x:F-1,y:0,width:S+2,height:d})):S<0&&(O.sx=(F-S)*p,O.sw=M*p,O.dx=F*p,O.dw=M*p,C.push({x:c+S,y:0,width:-S,height:d})),e.setTransform(1,0,0,1,0,0),v){if(F>0&&S!==0&&R===0&&(r===void 0||n?.[1]!==!1)){const k=F*p,$=d*p;e.drawImage(t,0,0,k,$,0,0,k,$)}if(D>0&&S===0&&R!==0&&(r===void 0||n?.[0]!==!1)){const k=(d-D)*p,$=c*p,G=D*p;e.drawImage(t,0,k,$,G,0,k,$,G)}}e.drawImage(t,O.sx,O.sy,O.sw,O.sh,O.dx,O.dy,O.dw,O.dh),e.scale(p,p)}return e.imageSmoothingEnabled=!0,{regions:C}}function C0(e,t,n,r,i,o,s,a,l,u){const c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||_r(l,n,r,i,a,(d,g,h,p)=>{if(d.sourceIndex===u){const m=Math.max(g,p)+1;return c.push({x:m,y:0,width:o-m,height:s}),!0}}),c}function S0(e,t){if(t===void 0||e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const i=e.mappedColumns[r],o=t.mappedColumns[r];if(Mi(i,o))continue;if(n!==void 0||i.width===o.width)return!1;const{width:s,...a}=i,{width:l,...u}=o;if(!Mi(a,u))return!1;n=r}return n===void 0?!0:n}return!0}function Hu(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m){const y=p?.filter(P=>P.style!=="no-outline");if(y===void 0||y.length===0)return;const b=Ri(a),v=Qr(h,g,d),C=[l,0,a.length,h-g],E=[b,0,t,n-v],I=y.map(P=>{const x=P.range,S=P.style??"dashed";return d0(x,C,t,n,E).map(F=>{const D=F.rect,M=Fs(D.x,D.y,t,n,c,u+c,r,i,o,s,h,l,g,a,d),T=D.width===1&&D.height===1?M:Fs(D.x+D.width-1,D.y+D.height-1,t,n,c,u+c,r,i,o,s,h,l,g,a,d);return D.x+D.width>=a.length&&(T.width-=1),D.y+D.height>=h&&(T.height-=1),{color:P.color,style:S,clip:F.clip,rect:c0({x:M.x,y:M.y,width:T.x+T.width-M.x,height:T.y+T.height-M.y},t,n,8)}})}),R=()=>{e.lineWidth=1;let P=!1;for(const x of I)for(const S of x)if(S?.rect!==void 0&&go(0,0,t,n,S.rect.x,S.rect.y,S.rect.width,S.rect.height)){const F=P,D=!u0(S.clip,S.rect);e.beginPath(),D&&(e.save(),e.rect(S.clip.x,S.clip.y,S.clip.width,S.clip.height),e.clip()),S.style==="dashed"&&!P?(e.setLineDash([5,3]),P=!0):(S.style==="solid"||S.style==="solid-outline")&&P&&(e.setLineDash([]),P=!1),e.strokeStyle=S.style==="solid-outline"?Vn(Vn(S.color,m.borderColor),m.bgCell):jr(S.color,1),e.closePath(),e.strokeRect(S.rect.x+.5,S.rect.y+.5,S.rect.width-1,S.rect.height-1),D&&(e.restore(),P=F)}P&&e.setLineDash([])};return R(),R}function zu(e,t,n,r,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,r),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function ds(e,t,n,r,i,o,s,a,l,u,c,d,g,h,p,m,y){if(c.current===void 0)return;const b=m!==!1&&m!==void 0;if(!b)return;const v=typeof m=="object"?{...ua,...m}:ua,C=c.current.range,E=c.current.cell,I=[C.x+C.width-1,C.y+C.height-1];if(E[1]>=y&&I[1]>=y||!s.some($=>$.sourceIndex===E[0]||$.sourceIndex===I[0]))return;const[P,x]=c.current.cell,S=g(c.current.cell),F=S.span??[P,P],D=x>=y-h,M=h>0&&!D?Qr(y,h,d)-1:0,T=I[1];let O;if(_r(s,r,i,o,u,($,G,j,ue,Q)=>{if($.sticky&&P>$.sourceIndex)return;const ee=$.sourceIndex<F[0],ce=$.sourceIndex>F[1],ae=$.sourceIndex===I[0];if(!(!ae&&(ee||ce)))return Ra(Q,j,n,y,d,h,p,void 0,(ge,de,re)=>{if(de!==x&&de!==T)return;let H=G,L=$.width;if(S.span!==void 0){const te=Vd(S.span,G,ge,$.width,re,$,a),ie=$.sticky?te[0]:te[1];ie!==void 0&&(H=ie.x,L=ie.width)}return de===T&&ae&&b&&(O=()=>{ue>H&&!$.sticky&&(e.beginPath(),e.rect(ue,0,t-ue,n),e.clip());const te=v.size,ie=te/2,Se=H+L+v.offsetX-ie+.5,me=ge+re+v.offsetY-ie+.5;e.beginPath(),v.shape==="circle"?e.arc(Se+ie,me+ie,ie,0,Math.PI*2):e.rect(Se,me,te,te),e.fillStyle=$.themeOverride?.accentColor??l.accentColor,e.fill(),v.outline>0&&(e.lineWidth=v.outline,e.strokeStyle=l.bgCell,v.shape==="circle"?(e.beginPath(),e.arc(Se+ie,me+ie,ie+v.outline/2,0,Math.PI*2),e.stroke()):e.strokeRect(Se-v.outline/2,me-v.outline/2,te+v.outline,te+v.outline))}),O!==void 0}),O!==void 0}),O===void 0)return;const k=()=>{e.save(),e.beginPath(),e.rect(0,u,t,n-u-M),e.clip(),O?.(),e.restore()};return k(),k}function x0(e,t,n,r,i,o,s,a,l){l===void 0||l.size===0||(e.beginPath(),zd(t,n,o,r,(u,c,d,g,h,p)=>{l.hasItemInRectangle({x:u[0],y:-2,width:u[1]-u[0]+1,height:1})&&e.rect(d,g,h,p)}),_r(t,a,o,s,i,(u,c,d,g)=>{const h=Math.max(0,g-c),p=c+h+1,m=u.width-h-1;l.has([u.sourceIndex,-1])&&e.rect(p,r,m,i-r)}),e.clip())}function k0(e,t,n,r,i,o,s,a,l,u){let c=0;return _r(e,o,r,i,n,(d,g,h,p,m)=>(Ra(m,h,t,s,a,l,u,void 0,(y,b,v,C)=>{C||(c=Math.max(b,c))}),!0)),c}function Vu(e,t){const{canvasCtx:n,headerCanvasCtx:r,width:i,height:o,cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mappedColumns:c,enableGroups:d,freezeColumns:g,dragAndDropState:h,theme:p,drawFocus:m,headerHeight:y,groupHeaderHeight:b,disabledRows:v,rowHeight:C,verticalBorder:E,overrideCursor:I,isResizing:R,selection:P,fillHandle:x,freezeTrailingRows:S,rows:F,getCellContent:D,getGroupDetails:M,getRowThemeOverride:T,isFocused:O,drawHeaderCallback:k,prelightCells:$,drawCellCallback:G,highlightRegions:j,resizeCol:ue,imageLoader:Q,lastBlitData:ee,hoverValues:ce,hyperWrapping:ae,hoverInfo:ge,spriteManager:de,maxScaleFactor:re,hasAppendRow:H,touchMode:L,enqueue:X,renderStateProvider:te,getCellRenderer:ie,renderStrategy:Se,bufferACtx:me,bufferBCtx:nt,damage:xe,minimumCellWidth:gt,resizeIndicator:Mt}=e;if(i===0||o===0)return;const Ue=Se==="double-buffer",ne=Math.min(re,Math.ceil(window.devicePixelRatio??1)),oe=Se!=="direct"&&S0(e,t),we=n.canvas;(we.width!==i*ne||we.height!==o*ne)&&(we.width=i*ne,we.height=o*ne,we.style.width=i+"px",we.style.height=o+"px");const fe=r.canvas,ye=d?b+y:y,$e=ye+1;(fe.width!==i*ne||fe.height!==$e*ne)&&(fe.width=i*ne,fe.height=$e*ne,fe.style.width=i+"px",fe.style.height=$e+"px");const Pe=me.canvas,it=nt.canvas;Ue&&(Pe.width!==i*ne||Pe.height!==o*ne)&&(Pe.width=i*ne,Pe.height=o*ne,ee.current!==void 0&&(ee.current.aBufferScroll=void 0)),Ue&&(it.width!==i*ne||it.height!==o*ne)&&(it.width=i*ne,it.height=o*ne,ee.current!==void 0&&(ee.current.bBufferScroll=void 0));const Me=ee.current;if(oe===!0&&s===Me?.cellXOffset&&a===Me?.cellYOffset&&l===Me?.translateX&&u===Me?.translateY)return;let qe=null;Ue&&(qe=n);const Te=r;let Ee;Ue?xe!==void 0?Ee=Me?.lastBuffer==="b"?nt:me:Ee=Me?.lastBuffer==="b"?me:nt:Ee=n;const De=Ee.canvas,ft=Ue?De===Pe?it:Pe:we,ot=typeof C=="number"?()=>C:C;Te.save(),Ee.save(),Te.beginPath(),Ee.beginPath(),Te.textBaseline="middle",Ee.textBaseline="middle",ne!==1&&(Te.scale(ne,ne),Ee.scale(ne,ne));const Xe=Ls(c,s,i,h,l);let wt=[];const yt=m&&P.current?.cell[1]===a&&u===0;let Ft=!1;if(j!==void 0){for(const pt of j)if(pt.style!=="no-outline"&&pt.range.y===a&&u===0){Ft=!0;break}}const It=()=>{g0(Te,Xe,d,ge,i,l,y,b,h,R,P,p,de,ce,E,M,xe,k,L),Au(Te,Xe,a,l,u,i,o,void 0,void 0,b,ye,ot,T,E,S,F,p,!0),Te.beginPath(),Te.moveTo(0,$e-.5),Te.lineTo(i,$e-.5),Te.strokeStyle=Vn(p.headerBottomBorderColor??p.horizontalBorderColor??p.borderColor,p.bgHeader),Te.stroke(),Ft&&Hu(Te,i,o,s,a,l,u,c,g,y,b,C,S,F,j,p),yt&&ds(Te,i,o,a,l,u,Xe,c,p,ye,P,ot,D,S,H,x,F)};if(xe!==void 0){const pt=Xe[Xe.length-1].sourceIndex+1,en=xe.hasItemInRegion([{x:s,y:-2,width:pt,height:2},{x:s,y:a,width:pt,height:300},{x:0,y:a,width:g,height:300},{x:0,y:-2,width:g,height:2},{x:s,y:F-S,width:pt,height:S,when:S>0}]),Bt=Le=>{Fu(Le,Xe,c,o,ye,l,u,a,F,ot,D,M,T,v,O,m,S,H,wt,xe,P,$,j,Q,de,ce,ge,G,ae,p,X,te,ie,I,gt);const St=P.current;x!==!1&&x!==void 0&&m&&St!==void 0&&xe.has(kd(St.range))&&ds(Le,i,o,a,l,u,Xe,c,p,ye,P,ot,D,S,H,x,F)};en&&(Bt(Ee),qe!==null&&(qe.save(),qe.scale(ne,ne),qe.textBaseline="middle",Bt(qe),qe.restore()),xe.hasHeader()&&(x0(Te,Xe,i,b,ye,l,u,a,xe),It())),Ee.restore(),Te.restore();return}if((oe!==!0||s!==Me?.cellXOffset||l!==Me?.translateX||yt!==Me?.mustDrawFocusOnHeader||Ft!==Me?.mustDrawHighlightRingsOnHeader)&&It(),oe===!0){zn(ft!==void 0&&Me!==void 0);const{regions:pt}=y0(Ee,ft,ft===Pe?Me.aBufferScroll:Me.bBufferScroll,ft===Pe?Me.bBufferScroll:Me.aBufferScroll,Me,s,a,l,u,S,i,o,F,ye,ne,c,Xe,C,Ue);wt=pt}else oe!==!1&&(zn(Me!==void 0),wt=C0(Me,s,a,l,u,i,o,ye,Xe,oe));b0(Ee,Xe,i,o,S,F,E,ot,p);const dn=Hu(Ee,i,o,s,a,l,u,c,g,y,b,C,S,F,j,p),Zt=m?ds(Ee,i,o,a,l,u,Xe,c,p,ye,P,ot,D,S,H,x,F):void 0;if(Ee.fillStyle=p.bgCell,wt.length>0){Ee.beginPath();for(const pt of wt)Ee.rect(pt.x,pt.y,pt.width,pt.height);Ee.clip(),Ee.fill(),Ee.beginPath()}else Ee.fillRect(0,0,i,o);const fn=Fu(Ee,Xe,c,o,ye,l,u,a,F,ot,D,M,T,v,O,m,S,H,wt,xe,P,$,j,Q,de,ce,ge,G,ae,p,X,te,ie,I,gt);v0(Ee,Xe,c,i,o,ye,l,u,a,F,ot,T,P.rows,v,S,H,wt,xe,p),w0(Ee,Xe,a,l,u,i,o,wt,ye,ot,T,E,S,F,p),Au(Ee,Xe,a,l,u,i,o,wt,fn,b,ye,ot,T,E,S,F,p),dn?.(),Zt?.(),R&&Mt!=="none"&&_r(Xe,0,l,0,ye,(pt,en)=>pt.sourceIndex===ue?(zu(Te,en+pt.width,0,ye+1,Vn(p.resizeIndicatorColor??p.accentLight,p.bgHeader)),Mt==="full"&&zu(Ee,en+pt.width,ye,o,Vn(p.resizeIndicatorColor??p.accentLight,p.bgCell)),!0):!1),qe!==null&&(qe.fillStyle=p.bgCell,qe.fillRect(0,0,i,o),qe.drawImage(Ee.canvas,0,0));const Ot=k0(Xe,o,ye,l,u,a,F,ot,S,H);Q?.setWindow({x:s,y:a,width:Xe.length,height:Ot-a},g,Array.from({length:S},(pt,en)=>F-1-en));const Ct=Me!==void 0&&(s!==Me.cellXOffset||l!==Me.translateX),Tn=Me!==void 0&&(a!==Me.cellYOffset||u!==Me.translateY);ee.current={cellXOffset:s,cellYOffset:a,translateX:l,translateY:u,mustDrawFocusOnHeader:yt,mustDrawHighlightRingsOnHeader:Ft,lastBuffer:Ue?De===Pe?"a":"b":void 0,aBufferScroll:De===Pe?[Ct,Tn]:Me?.aBufferScroll,bBufferScroll:De===it?[Ct,Tn]:Me?.bBufferScroll},Ee.restore(),Te.restore()}const M0=80;function R0(e){const t=e-1;return t*t*t+1}class I0{callback;constructor(t){this.callback=t}currentHoveredItem=void 0;leavingItems=[];lastAnimationTime;addToLeavingItems=t=>{this.leavingItems.some(r=>so(r.item,t.item))||this.leavingItems.push(t)};removeFromLeavingItems=t=>{const n=this.leavingItems.find(r=>so(r.item,t));return this.leavingItems=this.leavingItems.filter(r=>r!==n),n?.hoverAmount??0};cleanUpLeavingElements=()=>{this.leavingItems=this.leavingItems.filter(t=>t.hoverAmount>0)};shouldStep=()=>{const t=this.leavingItems.length>0,n=this.currentHoveredItem!==void 0&&this.currentHoveredItem.hoverAmount<1;return t||n};getAnimatingItems=()=>this.currentHoveredItem!==void 0?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map(t=>({...t,hoverAmount:R0(t.hoverAmount)}));step=t=>{if(this.lastAnimationTime===void 0)this.lastAnimationTime=t;else{const r=(t-this.lastAnimationTime)/M0;for(const o of this.leavingItems)o.hoverAmount=Gn(o.hoverAmount-r,0,1);this.currentHoveredItem!==void 0&&(this.currentHoveredItem.hoverAmount=Gn(this.currentHoveredItem.hoverAmount+r,0,1));const i=this.getAnimatingItems();this.callback(i),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=t,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0};setHovered=t=>{if(!so(this.currentHoveredItem?.item,t)){if(this.currentHoveredItem!==void 0&&this.addToLeavingItems(this.currentHoveredItem),t!==void 0){const n=this.removeFromLeavingItems(t);this.currentHoveredItem={item:t,hoverAmount:n}}else this.currentHoveredItem=void 0;this.lastAnimationTime===void 0&&window.requestAnimationFrame(this.step)}}}class E0{fn;val;constructor(t){this.fn=t}get value(){return this.val??(this.val=this.fn())}}function ol(e){return new E0(e)}const T0=ol(()=>window.navigator.userAgent.includes("Firefox")),da=ol(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")),fa=ol(()=>window.navigator.platform.toLowerCase().startsWith("mac"));function D0(e){const t=f.useRef([]),n=f.useRef(0),r=f.useRef(e);r.current=e;const i=f.useCallback(()=>{const o=()=>window.requestAnimationFrame(s),s=()=>{const a=t.current.map(rl);t.current=[],r.current(new uo(a)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?o:s)},[]);return f.useCallback(o=>{t.current.length===0&&i();const s=er(o[0],o[1]);t.current.includes(s)||t.current.push(s)},[i])}const Tr="header",Qn="group-header",ha="out-of-bounds";var Si;(function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"})(Si||(Si={}));function Gd(e,t){return e===t?!0:e?.kind==="out-of-bounds"?e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]&&e?.region[0]===t?.region[0]&&e?.region[1]===t?.region[1]:e?.kind===t?.kind&&e?.location[0]===t?.location[0]&&e?.location[1]===t?.location[1]}const O0=(e,t)=>e.kind===Z.Custom?e.copyData:t?.(e)?.getAccessibilityString(e)??"",P0=(e,t)=>{const{width:n,height:r,accessibilityHeight:i,columns:o,cellXOffset:s,cellYOffset:a,headerHeight:l,fillHandle:u=!1,groupHeaderHeight:c,rowHeight:d,rows:g,getCellContent:h,getRowThemeOverride:p,onHeaderMenuClick:m,onHeaderIndicatorClick:y,enableGroups:b,isFilling:v,onCanvasFocused:C,onCanvasBlur:E,isFocused:I,selection:R,freezeColumns:P,onContextMenu:x,freezeTrailingRows:S,fixedShadowX:F=!0,fixedShadowY:D=!0,drawFocusRing:M,onMouseDown:T,onMouseUp:O,onMouseMoveRaw:k,onMouseMove:$,onItemHovered:G,dragAndDropState:j,firstColAccessible:ue,onKeyDown:Q,onKeyUp:ee,highlightRegions:ce,canvasRef:ae,onDragStart:ge,onDragEnd:de,eventTargetRef:re,isResizing:H,resizeColumn:L,isDragging:X,isDraggable:te=!1,allowResize:ie,disabledRows:Se,hasAppendRow:me,getGroupDetails:nt,theme:xe,prelightCells:gt,headerIcons:Mt,verticalBorder:Ue,drawCell:ne,drawHeader:oe,onCellFocused:we,onDragOverCell:fe,onDrop:ye,onDragLeave:$e,imageWindowLoader:Pe,smoothScrollX:it=!1,smoothScrollY:Me=!1,experimental:qe,getCellRenderer:Te,resizeIndicator:Ee="full"}=e,De=e.translateX??0,ft=e.translateY??0,ot=Math.max(P,Math.min(o.length-1,s)),Xe=f.useRef(null),wt=f.useRef(qe?.eventTarget??window),yt=wt.current,Ft=Pe,It=f.useRef(),[dn,Zt]=f.useState(!1),fn=f.useRef([]),Ot=f.useRef(),[Ct,Tn]=f.useState(),[pt,en]=f.useState(),Bt=f.useRef(null),[Le,St]=f.useState(),[mt,Dn]=f.useState(!1),Gt=f.useRef(mt);Gt.current=mt;const hn=f.useMemo(()=>new s0(Mt,()=>{ln.current=void 0,yn.current()}),[Mt]),On=b?c+l:l,Xt=f.useRef(-1),tn=(qe?.enableFirefoxRescaling??!1)&&T0.value,Be=(qe?.enableSafariRescaling??!1)&&da.value;f.useLayoutEffect(()=>{window.devicePixelRatio===1||!tn&&!Be||(Xt.current!==-1&&Zt(!0),window.clearTimeout(Xt.current),Xt.current=window.setTimeout(()=>{Zt(!1),Xt.current=-1},200))},[a,ot,De,ft,tn,Be]);const Pt=rm(o,P),$n=f.useMemo(()=>F?Ri(Pt,j):0,[Pt,j,F]),xt=f.useCallback((z,se,ke)=>{const Re=z.getBoundingClientRect();if(se>=Pt.length||ke>=g)return;const le=Re.width/n,ve=Fs(se,ke,n,r,c,On,ot,a,De,ft,g,P,S,Pt,d);return le!==1&&(ve.x*=le,ve.y*=le,ve.width*=le,ve.height*=le),ve.x+=Re.x,ve.y+=Re.y,ve},[n,r,c,On,ot,a,De,ft,g,P,S,Pt,d]),Et=f.useCallback((z,se,ke,Re)=>{const le=z.getBoundingClientRect(),ve=le.width/n,Ye=(se-le.left)/ve,Ie=(ke-le.top)/ve,je=5,ht=Ls(Pt,ot,n,void 0,De);let Qt=0,ct=0;const Tt=typeof PointerEvent<"u"&&Re instanceof PointerEvent&&Re.pointerType==="mouse"||typeof MouseEvent<"u"&&Re instanceof MouseEvent,et=typeof PointerEvent<"u"&&Re instanceof PointerEvent&&Re.pointerType==="touch"||typeof TouchEvent<"u"&&Re instanceof TouchEvent;Tt&&(Qt=Re.button,ct=Re.buttons);const Je=sm(Ye,ht,De),zt=lm(Ie,r,b,l,c,g,d,a,ft,S),Ut=Re?.shiftKey===!0,zr=Re?.ctrlKey===!0,nn=Re?.metaKey===!0,cn=[Ye<0?-1:n<Ye?1:0,Ie<On?-1:r<Ie?1:0];let Sr;if(Je===-1||Ie<0||Ye<0||zt===void 0||Ye>n||Ie>r){const Lt=Ye>n?1:Ye<0?-1:0,En=Ie>r?1:Ie<0?-1:0;let Fn=Lt*2,ii=En*2;Lt===0&&(Fn=Je===-1?Si.EndPadding:Si.Center),En===0&&(ii=zt===void 0?Si.EndPadding:Si.Center);let rt=!1;if(Je===-1&&zt===-1){const Yn=xt(z,Pt.length-1,-1);zn(Yn!==void 0),rt=se<Yn.x+Yn.width+je}const an=Ye>n&&Ye<n+Ts()||Ie>r&&Ie<r+Ts();Sr={kind:ha,location:[Je!==-1?Je:Ye<0?0:Pt.length-1,zt??g-1],region:[Fn,ii],shiftKey:Ut,ctrlKey:zr,metaKey:nn,isEdge:rt,isTouch:et,button:Qt,buttons:ct,scrollEdge:cn,isMaybeScrollbar:an}}else if(zt<=-1){let Lt=xt(z,Je,zt);zn(Lt!==void 0);let En=Lt!==void 0&&Lt.x+Lt.width-se<=je;const Fn=Je-1;se-Lt.x<=je&&Fn>=0?(En=!0,Lt=xt(z,Fn,zt),zn(Lt!==void 0),Sr={kind:b&&zt===-2?Qn:Tr,location:[Fn,zt],bounds:Lt,group:Pt[Fn].group??"",isEdge:En,shiftKey:Ut,ctrlKey:zr,metaKey:nn,isTouch:et,localEventX:se-Lt.x,localEventY:ke-Lt.y,button:Qt,buttons:ct,scrollEdge:cn}):Sr={kind:b&&zt===-2?Qn:Tr,group:Pt[Je].group??"",location:[Je,zt],bounds:Lt,isEdge:En,shiftKey:Ut,ctrlKey:zr,metaKey:nn,isTouch:et,localEventX:se-Lt.x,localEventY:ke-Lt.y,button:Qt,buttons:ct,scrollEdge:cn}}else{const Lt=xt(z,Je,zt);zn(Lt!==void 0);const En=Lt!==void 0&&Lt.x+Lt.width-se<je;let Fn=!1;if(u!==!1&&u!==void 0&&R.current!==void 0){const rt=typeof u=="object"?{...ua,...u}:ua,an=rt.size,Yn=an/2,_i=kd(R.current.range),_n=xt(z,_i[0],_i[1]);if(_n!==void 0){const Vt=_n.x+_n.width+rt.offsetX-Yn+.5,La=_n.y+_n.height+rt.offsetY-Yn+.5;Fn=Math.abs(Vt-se)<an&&Math.abs(La-ke)<an}}Sr={kind:"cell",location:[Je,zt],bounds:Lt,isEdge:En,shiftKey:Ut,ctrlKey:zr,isFillHandle:Fn,metaKey:nn,isTouch:et,localEventX:se-Lt.x,localEventY:ke-Lt.y,button:Qt,buttons:ct,scrollEdge:cn}}return Sr},[n,Pt,ot,De,r,b,l,c,g,d,a,ft,S,xt,u,R,On]),[kn]=Ct??[],cr=f.useRef(()=>{}),Nn=f.useRef(Ct);Nn.current=Ct;const[N,We]=f.useMemo(()=>{const z=document.createElement("canvas"),se=document.createElement("canvas");return z.style.display="none",z.style.opacity="0",z.style.position="fixed",se.style.display="none",se.style.opacity="0",se.style.position="fixed",[z.getContext("2d",{alpha:!1}),se.getContext("2d",{alpha:!1})]},[]);f.useLayoutEffect(()=>{if(!(N===null||We===null))return document.documentElement.append(N.canvas),document.documentElement.append(We.canvas),()=>{N.canvas.remove(),We.canvas.remove()}},[N,We]);const Ge=f.useMemo(()=>new vm,[]),Rt=tn&&dn?1:Be&&dn?2:5,gn=qe?.disableMinimumCellWidth===!0?1:10,ln=f.useRef(),Mn=f.useRef(null),wn=f.useRef(null),Wt=f.useCallback(()=>{const z=Xe.current,se=Bt.current;if(z===null||se===null||(Mn.current===null&&(Mn.current=z.getContext("2d",{alpha:!1}),z.width=0,z.height=0),wn.current===null&&(wn.current=se.getContext("2d",{alpha:!1}),se.width=0,se.height=0),Mn.current===null||wn.current===null||N===null||We===null))return;let ke=!1;const Re=Ye=>{ke=!0,St(Ye)},le=ln.current,ve={headerCanvasCtx:wn.current,canvasCtx:Mn.current,bufferACtx:N,bufferBCtx:We,width:n,height:r,cellXOffset:ot,cellYOffset:a,translateX:Math.round(De),translateY:Math.round(ft),mappedColumns:Pt,enableGroups:b,freezeColumns:P,dragAndDropState:j,theme:xe,headerHeight:l,groupHeaderHeight:c,disabledRows:Se??Qe.empty(),rowHeight:d,verticalBorder:Ue,isResizing:H,resizeCol:L,isFocused:I,selection:R,fillHandle:u,drawCellCallback:ne,hasAppendRow:me,overrideCursor:Re,maxScaleFactor:Rt,freezeTrailingRows:S,rows:g,drawFocus:M,getCellContent:h,getGroupDetails:nt??(Ye=>({name:Ye})),getRowThemeOverride:p,drawHeaderCallback:oe,prelightCells:gt,highlightRegions:ce,imageLoader:Ft,lastBlitData:Ot,damage:It.current,hoverValues:fn.current,hoverInfo:Nn.current,spriteManager:hn,scrolling:dn,hyperWrapping:qe?.hyperWrapping??!1,touchMode:mt,enqueue:cr.current,renderStateProvider:Ge,renderStrategy:qe?.renderStrategy??(da.value?"double-buffer":"single-buffer"),getCellRenderer:Te,minimumCellWidth:gn,resizeIndicator:Ee};ve.damage===void 0?(ln.current=ve,Vu(ve,le)):Vu(ve,void 0),!ke&&(ve.damage===void 0||ve.damage.has(Nn?.current?.[0]))&&St(void 0)},[N,We,n,r,ot,a,De,ft,Pt,b,P,j,xe,l,c,Se,d,Ue,H,me,L,I,R,u,S,g,M,Rt,h,nt,p,ne,oe,gt,ce,Ft,hn,dn,qe?.hyperWrapping,qe?.renderStrategy,mt,Ge,Te,gn,Ee]),yn=f.useRef(Wt);f.useLayoutEffect(()=>{Wt(),yn.current=Wt},[Wt]),f.useLayoutEffect(()=>{(async()=>{document?.fonts?.ready!==void 0&&(await document.fonts.ready,ln.current=void 0,yn.current())})()},[]);const Pn=f.useCallback(z=>{It.current=z,yn.current(),It.current=void 0},[]),Ce=D0(Pn);cr.current=Ce;const _t=f.useCallback(z=>{Pn(new uo(z.map(se=>se.cell)))},[Pn]);Ft.setCallback(Pn);const[Jt,Ln]=f.useState(!1),[un,Yt]=kn??[],jt=un!==void 0&&Yt===-1&&o[un].headerRowMarkerDisabled!==!0,Cn=un!==void 0&&Yt===-2;let Bn=!1,Ii=!1,Ar=Le;if(Ar===void 0&&un!==void 0&&Yt!==void 0&&Yt>-1&&Yt<g){const z=h([un,Yt],!0);Bn=z.kind===Xn.NewRow||z.kind===Xn.Marker&&z.markerKind!=="number",Ii=z.kind===Z.Boolean&&Xs(z),Ar=z.cursor}const Ei=X?"grabbing":(pt??!1)||H?"col-resize":Jt||v?"crosshair":Ar!==void 0?Ar:jt||Bn||Ii||Cn?"pointer":"default",Wn=f.useMemo(()=>({contain:"strict",display:"block",cursor:Ei}),[Ei]),Rn=f.useRef("default"),Ti=re?.current;Ti!=null&&Rn.current!==Wn.cursor&&(Ti.style.cursor=Rn.current=Wn.cursor);const Hr=f.useCallback((z,se,ke,Re)=>{if(nt===void 0)return;const le=nt(z);if(le.actions!==void 0){const ve=Bd(se,le.actions);for(const[Ye,Ie]of ve.entries())if(Yr(Ie,ke+se.x,Re+Ie.y))return le.actions[Ye]}},[nt]),dr=f.useCallback((z,se,ke,Re)=>{const le=Pt[se];if(!X&&!H&&!(pt??!1)){const ve=xt(z,se,-1);zn(ve!==void 0);const Ye=Wd(void 0,le,ve.x,ve.y,ve.width,ve.height,xe,Ks(le.title)==="rtl");if(le.hasMenu===!0&&Ye.menuBounds!==void 0&&Yr(Ye.menuBounds,ke,Re))return{area:"menu",bounds:Ye.menuBounds};if(le.indicatorIcon!==void 0&&Ye.indicatorIconBounds!==void 0&&Yr(Ye.indicatorIconBounds,ke,Re))return{area:"indicator",bounds:Ye.indicatorIconBounds}}},[Pt,xt,pt,X,H,xe]),ei=f.useRef(0),br=f.useRef(),wr=f.useRef(!1),ti=f.useCallback(z=>{const se=Xe.current,ke=re?.current;if(se===null||z.target!==se&&z.target!==ke)return;wr.current=!0;const Re=z.clientX,le=z.clientY;if(z.target===ke&&ke!==null){const Ye=ke.getBoundingClientRect();if(Re>Ye.right||le>Ye.bottom)return}const ve=Et(se,Re,le,z);br.current=ve.location,ve.isTouch&&(ei.current=Date.now()),Gt.current!==ve.isTouch&&Dn(ve.isTouch),!(ve.kind===Tr&&dr(se,ve.location[0],Re,le)!==void 0)&&(ve.kind===Qn&&Hr(ve.group,ve.bounds,ve.localEventX,ve.localEventY)!==void 0||(T?.(ve),!ve.isTouch&&te!==!0&&te!==ve.kind&&ve.button<3&&ve.button!==1&&z.preventDefault()))},[re,te,Et,Hr,dr,T]);xn("pointerdown",ti,yt,!1);const fr=f.useRef(0),ni=f.useCallback(z=>{const se=fr.current;fr.current=Date.now();const ke=Xe.current;if(wr.current=!1,O===void 0||ke===null)return;const Re=re?.current,le=z.target!==ke&&z.target!==Re,ve=z.clientX,Ye=z.clientY,Ie=z.pointerType==="mouse"?z.button<3:!0;let je=Et(ke,ve,Ye,z);je.isTouch&&ei.current!==0&&Date.now()-ei.current>500&&(je={...je,isLongTouch:!0}),se!==0&&Date.now()-se<(je.isTouch?1e3:500)&&(je={...je,isDoubleClick:!0}),Gt.current!==je.isTouch&&Dn(je.isTouch),!le&&z.cancelable&&Ie&&z.preventDefault();const[ht]=je.location,Qt=dr(ke,ht,ve,Ye);if(je.kind===Tr&&Qt!==void 0){(je.button!==0||br.current?.[0]!==ht||br.current?.[1]!==-1)&&O(je,!0);return}else if(je.kind===Qn){const ct=Hr(je.group,je.bounds,je.localEventX,je.localEventY);if(ct!==void 0){je.button===0&&ct.onClick(je);return}}O(je,le)},[O,re,Et,dr,Hr]);xn("pointerup",ni,yt,!1);const Ta=f.useCallback(z=>{const se=Xe.current;if(se===null)return;const ke=re?.current,Re=z.target!==se&&z.target!==ke;let le,ve,Ye=!0;z instanceof MouseEvent?(le=z.clientX,ve=z.clientY,Ye=z.button<3):(le=z.changedTouches[0].clientX,ve=z.changedTouches[0].clientY);const Ie=Et(se,le,ve,z);Gt.current!==Ie.isTouch&&Dn(Ie.isTouch),!Re&&z.cancelable&&Ye&&z.preventDefault();const[je]=Ie.location;if(Ie.kind===Tr){const ht=dr(se,je,le,ve);ht!==void 0&&Ie.button===0&&br.current?.[0]===je&&br.current?.[1]===-1&&(ht.area==="menu"?m?.(je,ht.bounds):ht.area==="indicator"&&y?.(je,ht.bounds))}},[re,Et,dr,m,y]);xn("click",Ta,yt,!1);const So=f.useCallback(z=>{const se=Xe.current,ke=re?.current;if(se===null||z.target!==se&&z.target!==ke||x===void 0)return;const Re=Et(se,z.clientX,z.clientY,z);x(Re,()=>{z.cancelable&&z.preventDefault()})},[re,Et,x]);xn("contextmenu",So,re?.current??null,!1);const _e=f.useCallback(z=>{It.current=new uo(z.map(se=>se.item)),fn.current=z,yn.current(),It.current=void 0},[]),Di=f.useMemo(()=>new I0(_e),[_e]),xo=f.useRef(Di);xo.current=Di,f.useLayoutEffect(()=>{const z=xo.current;if(kn===void 0||kn[1]<0){z.setHovered(kn);return}const se=h(kn,!0),ke=Te(se),Re=ke===void 0&&se.kind===Z.Custom||ke?.needsHover!==void 0&&(typeof ke.needsHover=="boolean"?ke.needsHover:ke.needsHover(se));z.setHovered(Re?kn:void 0)},[h,Te,kn]);const yr=f.useRef(),Da=f.useCallback(z=>{const se=Xe.current;if(se===null)return;const ke=re?.current,Re=z.target!==se&&z.target!==ke,le=Et(se,z.clientX,z.clientY,z);if(le.kind!=="out-of-bounds"&&Re&&!wr.current&&!le.isTouch)return;const ve=(Ie,je)=>{Tn(ht=>ht===Ie||ht?.[0][0]===Ie?.[0][0]&&ht?.[0][1]===Ie?.[0][1]&&(ht?.[1][0]===Ie?.[1][0]&&ht?.[1][1]===Ie?.[1][1]||!je)?ht:Ie)};if(!Gd(le,yr.current))St(void 0),G?.(le),ve(le.kind===ha?void 0:[le.location,[le.localEventX,le.localEventY]],!0),yr.current=le;else if(le.kind==="cell"||le.kind===Tr||le.kind===Qn){let Ie=!1,je=!0;if(le.kind==="cell"){const Qt=h(le.location);je=Te(Qt)?.needsHoverPosition??Qt.kind===Z.Custom,Ie=je}else Ie=!0;const ht=[le.location,[le.localEventX,le.localEventY]];ve(ht,je),Nn.current=ht,Ie&&Pn(new uo([le.location]))}const Ye=le.location[0]>=(ue?0:1);en(le.kind===Tr&&le.isEdge&&Ye&&ie===!0),Ln(le.kind==="cell"&&le.isFillHandle),k?.(z),$(le)},[re,Et,ue,ie,k,$,G,h,Te,Pn]);xn("pointermove",Da,yt,!0);const ir=f.useCallback(z=>{const se=Xe.current;if(se===null)return;let ke,Re;R.current!==void 0&&(ke=xt(se,R.current.cell[0],R.current.cell[1]),Re=R.current.cell),Q?.({bounds:ke,stopPropagation:()=>z.stopPropagation(),preventDefault:()=>z.preventDefault(),cancel:()=>{},ctrlKey:z.ctrlKey,metaKey:z.metaKey,shiftKey:z.shiftKey,altKey:z.altKey,key:z.key,keyCode:z.keyCode,rawEvent:z,location:Re})},[Q,R,xt]),Oi=f.useCallback(z=>{const se=Xe.current;if(se===null)return;let ke,Re;R.current!==void 0&&(ke=xt(se,R.current.cell[0],R.current.cell[1]),Re=R.current.cell),ee?.({bounds:ke,stopPropagation:()=>z.stopPropagation(),preventDefault:()=>z.preventDefault(),cancel:()=>{},ctrlKey:z.ctrlKey,metaKey:z.metaKey,shiftKey:z.shiftKey,altKey:z.altKey,key:z.key,keyCode:z.keyCode,rawEvent:z,location:Re})},[ee,R,xt]),ko=f.useCallback(z=>{if(Xe.current=z,ae!==void 0&&(ae.current=z),qe?.eventTarget)wt.current=qe.eventTarget;else if(z===null)wt.current=window;else{const se=z.getRootNode();se===document&&(wt.current=window),wt.current=se}},[ae,qe?.eventTarget]),Mo=f.useCallback(z=>{const se=Xe.current;if(se===null||te===!1||H){z.preventDefault();return}let ke,Re;const le=Et(se,z.clientX,z.clientY);if(te!==!0&&le.kind!==te){z.preventDefault();return}const ve=(ct,Tt)=>{ke=ct,Re=Tt};let Ye,Ie,je;const ht=(ct,Tt,et)=>{Ye=ct,Ie=Tt,je=et};let Qt=!1;if(ge?.({...le,setData:ve,setDragImage:ht,preventDefault:()=>Qt=!0,defaultPrevented:()=>Qt}),!Qt&&ke!==void 0&&Re!==void 0&&z.dataTransfer!==null)if(z.dataTransfer.setData(ke,Re),z.dataTransfer.effectAllowed="copyLink",Ye!==void 0&&Ie!==void 0&&je!==void 0)z.dataTransfer.setDragImage(Ye,Ie,je);else{const[ct,Tt]=le.location;if(Tt!==void 0){const et=document.createElement("canvas"),Je=xt(se,ct,Tt);zn(Je!==void 0);const zt=Math.ceil(window.devicePixelRatio??1);et.width=Je.width*zt,et.height=Je.height*zt;const Ut=et.getContext("2d");Ut!==null&&(Ut.scale(zt,zt),Ut.textBaseline="middle",Tt===-1?(Ut.font=xe.headerFontFull,Ut.fillStyle=xe.bgHeader,Ut.fillRect(0,0,et.width,et.height),Ud(Ut,0,0,Je.width,Je.height,Pt[ct],!1,xe,!1,void 0,void 0,!1,0,hn,oe,!1)):(Ut.font=xe.baseFontFull,Ut.fillStyle=xe.bgCell,Ut.fillRect(0,0,et.width,et.height),Nd(Ut,h([ct,Tt]),0,Tt,!1,!1,0,0,Je.width,Je.height,!1,xe,xe.bgCell,Ft,hn,1,void 0,!1,0,void 0,void 0,void 0,Ge,Te,()=>{}))),et.style.left="-100%",et.style.position="absolute",et.style.width=`${Je.width}px`,et.style.height=`${Je.height}px`,document.body.append(et),z.dataTransfer.setDragImage(et,Je.width/2,Je.height/2),window.setTimeout(()=>{et.remove()},0)}}else z.preventDefault()},[te,H,Et,ge,xt,xe,Pt,hn,oe,h,Ft,Ge,Te]);xn("dragstart",Mo,re?.current??null,!1,!1);const Pi=f.useRef(),Oa=f.useCallback(z=>{const se=Xe.current;if(ye!==void 0&&z.preventDefault(),se===null||fe===void 0)return;const ke=Et(se,z.clientX,z.clientY),[Re,le]=ke.location,ve=Re-(ue?0:1),[Ye,Ie]=Pi.current??[];(Ye!==ve||Ie!==le)&&(Pi.current=[ve,le],fe([ve,le],z.dataTransfer))},[ue,Et,fe,ye]);xn("dragover",Oa,re?.current??null,!1,!1);const or=f.useCallback(()=>{Pi.current=void 0,de?.()},[de]);xn("dragend",or,re?.current??null,!1,!1);const Cr=f.useCallback(z=>{const se=Xe.current;if(se===null||ye===void 0)return;z.preventDefault();const ke=Et(se,z.clientX,z.clientY),[Re,le]=ke.location,ve=Re-(ue?0:1);ye([ve,le],z.dataTransfer)},[ue,Et,ye]);xn("drop",Cr,re?.current??null,!1,!1);const Un=f.useCallback(()=>{$e?.()},[$e]);xn("dragleave",Un,re?.current??null,!1,!1);const U=f.useRef(R);U.current=R;const At=f.useRef(null),hr=f.useCallback(z=>{Xe.current===null||!Xe.current.contains(document.activeElement)||(z===null&&U.current.current!==void 0?ae?.current?.focus({preventScroll:!0}):z!==null&&z.focus({preventScroll:!0}),At.current=z)},[ae]);f.useImperativeHandle(t,()=>({focus:()=>{const z=At.current;z===null||!document.contains(z)?ae?.current?.focus({preventScroll:!0}):z.focus({preventScroll:!0})},getBounds:(z,se)=>{if(!(ae===void 0||ae.current===null))return xt(ae.current,z??0,se??-1)},damage:_t,getMouseArgsForPosition:(z,se,ke)=>{if(!(ae===void 0||ae.current===null))return Et(ae.current,z,se,ke)}}),[ae,_t,xt,Et]);const Li=f.useRef(),Ro=Sp(()=>{if(n<50||qe?.disableAccessibilityTree===!0)return null;let z=Ls(Pt,ot,n,j,De);const se=ue?0:-1;!ue&&z[0]?.sourceIndex===0&&(z=z.slice(1));const[ke,Re]=R.current?.cell??[],le=R.current?.range,ve=z.map(Ie=>Ie.sourceIndex),Ye=Or(a,Math.min(g,a+i));return ke!==void 0&&Re!==void 0&&!(ve.includes(ke)&&Ye.includes(Re))&&hr(null),f.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":g+1,"aria-multiselectable":"true","aria-colcount":Pt.length+se},f.createElement("thead",{role:"rowgroup"},f.createElement("tr",{role:"row","aria-rowindex":1},z.map(Ie=>f.createElement("th",{role:"columnheader","aria-selected":R.columns.hasIndex(Ie.sourceIndex),"aria-colindex":Ie.sourceIndex+1+se,tabIndex:-1,onFocus:je=>{if(je.target!==At.current)return we?.([Ie.sourceIndex,-1])},key:Ie.sourceIndex},Ie.title)))),f.createElement("tbody",{role:"rowgroup"},Ye.map(Ie=>f.createElement("tr",{role:"row","aria-selected":R.rows.hasIndex(Ie),key:Ie,"aria-rowindex":Ie+2},z.map(je=>{const ht=je.sourceIndex,Qt=er(ht,Ie),ct=ke===ht&&Re===Ie,Tt=le!==void 0&&ht>=le.x&&ht<le.x+le.width&&Ie>=le.y&&Ie<le.y+le.height,et=`glide-cell-${ht}-${Ie}`,Je=[ht,Ie],zt=h(Je,!0);return f.createElement("td",{key:Qt,role:"gridcell","aria-colindex":ht+1+se,"aria-selected":Tt,"aria-readonly":yi(zt)||!bi(zt),id:et,"data-testid":et,onClick:()=>{const Ut=ae?.current;if(Ut!=null)return Q?.({bounds:xt(Ut,ht,Ie),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:Je})},onFocusCapture:Ut=>{if(!(Ut.target===At.current||Li.current?.[0]===ht&&Li.current?.[1]===Ie))return Li.current=Je,we?.(Je)},ref:ct?hr:void 0,tabIndex:-1},O0(zt,Te))})))))},[n,Pt,ot,j,De,g,a,i,R,hr,h,ae,Q,xt,we],200),ri=P===0||!F?0:ot>P?1:Gn(-De/100,0,1),Pa=-a*32+ft,Fi=D?Gn(-Pa/100,0,1):0,K=f.useMemo(()=>{if(!ri&&!Fi)return null;const z={position:"absolute",top:0,left:$n,width:n-$n,height:r,opacity:ri,pointerEvents:"none",transition:it?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},se={position:"absolute",top:On,left:0,width:n,height:r,opacity:Fi,pointerEvents:"none",transition:Me?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return f.createElement(f.Fragment,null,ri>0&&f.createElement("div",{id:"shadow-x",style:z}),Fi>0&&f.createElement("div",{id:"shadow-y",style:se}))},[ri,Fi,$n,n,it,On,r,Me]),In=f.useMemo(()=>({position:"absolute",top:0,left:0}),[]);return f.createElement(f.Fragment,null,f.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:ir,onKeyUp:Oi,onFocus:C,onBlur:E,ref:ko,style:Wn},Ro),f.createElement("canvas",{ref:Bt,style:In}),K)},L0=f.memo(f.forwardRef(P0));function eo(e,t,n,r){return Gn(Math.round(t-(e.growOffset??0)),Math.ceil(n),Math.floor(r))}const F0=e=>{const[t,n]=f.useState(),[r,i]=f.useState(),[o,s]=f.useState(),[a,l]=f.useState(),[u,c]=f.useState(!1),[d,g]=f.useState(),[h,p]=f.useState(),[m,y]=f.useState(),[b,v]=f.useState(!1),[C,E]=f.useState(),{onHeaderMenuClick:I,onHeaderIndicatorClick:R,getCellContent:P,onColumnMoved:x,onColumnResize:S,onColumnResizeStart:F,onColumnResizeEnd:D,gridRef:M,maxColumnWidth:T,minColumnWidth:O,onRowMoved:k,lockColumns:$,onColumnProposeMove:G,onMouseDown:j,onMouseUp:ue,onItemHovered:Q,onDragStart:ee,canvasRef:ce}=e,ae=(S??D??F)!==void 0,{columns:ge,selection:de}=e,re=de.columns,H=f.useCallback(ne=>{const[oe,we]=ne.location;o!==void 0&&a!==oe&&oe>=$?(c(!0),l(oe)):h!==void 0&&we!==void 0?(v(!0),y(Math.max(0,we))):r===void 0&&!u&&!b&&Q?.(ne)},[o,h,a,Q,$,r,u,b]),L=x!==void 0,X=f.useCallback(ne=>{if(ne.button===0){const[oe,we]=ne.location;if(ne.kind==="out-of-bounds"&&ne.isEdge&&ae){const fe=M?.current?.getBounds(ge.length-1,-1);fe!==void 0&&(n(fe.x),i(ge.length-1))}else if(ne.kind==="header"&&oe>=$){const fe=ce?.current;if(ne.isEdge&&ae&&fe){n(ne.bounds.x),i(oe);const $e=fe.getBoundingClientRect().width/fe.offsetWidth,Pe=ne.bounds.width/$e;F?.(ge[oe],Pe,oe,Pe+(ge[oe].growOffset??0))}else ne.kind==="header"&&L&&(g(ne.bounds.x),s(oe))}else ne.kind==="cell"&&$>0&&oe===0&&we!==void 0&&k!==void 0&&(E(ne.bounds.y),p(we))}j?.(ne)},[j,ae,$,k,M,ge,L,F,ce]),te=f.useCallback((ne,oe)=>{u||b||I?.(ne,oe)},[u,b,I]),ie=f.useCallback((ne,oe)=>{u||b||R?.(ne,oe)},[u,b,R]),Se=f.useRef(-1),me=f.useCallback(()=>{Se.current=-1,p(void 0),y(void 0),E(void 0),v(!1),s(void 0),l(void 0),g(void 0),c(!1),i(void 0),n(void 0)},[]),nt=f.useCallback((ne,oe)=>{if(ne.button===0){if(r!==void 0){if(re?.hasIndex(r)===!0)for(const fe of re){if(fe===r)continue;const ye=ge[fe],$e=eo(ye,Se.current,O,T);S?.(ye,$e,fe,$e+(ye.growOffset??0))}const we=eo(ge[r],Se.current,O,T);if(D?.(ge[r],we,r,we+(ge[r].growOffset??0)),re.hasIndex(r))for(const fe of re){if(fe===r)continue;const ye=ge[fe],$e=eo(ye,Se.current,O,T);D?.(ye,$e,fe,$e+(ye.growOffset??0))}}me(),o!==void 0&&a!==void 0&&G?.(o,a)!==!1&&x?.(o,a),h!==void 0&&m!==void 0&&k?.(h,m)}ue?.(ne,oe)},[ue,r,o,a,h,m,re,D,ge,O,T,S,x,k,me,G]),xe=f.useMemo(()=>{if(!(o===void 0||a===void 0)&&o!==a&&G?.(o,a)!==!1)return{src:o,dest:a}},[o,a,G]),gt=f.useCallback(ne=>{const oe=ce?.current;if(o!==void 0&&d!==void 0)Math.abs(ne.clientX-d)>20&&c(!0);else if(h!==void 0&&C!==void 0)Math.abs(ne.clientY-C)>20&&v(!0);else if(r!==void 0&&t!==void 0&&oe){const fe=oe.getBoundingClientRect().width/oe.offsetWidth,ye=(ne.clientX-t)/fe,$e=ge[r],Pe=eo($e,ye,O,T);if(S?.($e,Pe,r,Pe+($e.growOffset??0)),Se.current=ye,re?.first()===r)for(const it of re){if(it===r)continue;const Me=ge[it],qe=eo(Me,Se.current,O,T);S?.(Me,qe,it,qe+(Me.growOffset??0))}}},[o,d,h,C,r,t,ge,O,T,S,re,ce]),Mt=f.useCallback((ne,oe)=>{if(h===void 0||m===void 0)return P(ne,oe);let[we,fe]=ne;return fe===m?fe=h:(fe>m&&(fe-=1),fe>=h&&(fe+=1)),P([we,fe],oe)},[h,m,P]),Ue=f.useCallback(ne=>{ee?.(ne),ne.defaultPrevented()||me()},[me,ee]);return f.createElement(L0,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:Mt,isResizing:r!==void 0,onHeaderMenuClick:te,onHeaderIndicatorClick:ie,isDragging:u,onItemHovered:H,onDragStart:Ue,onMouseDown:X,allowResize:ae,onMouseUp:nt,dragAndDropState:xe,onMouseMoveRaw:gt,ref:M})};function _0(e){const t=f.useRef(null),[n,r]=f.useState({width:e?.[0],height:e?.[1]});return f.useLayoutEffect(()=>{const i=s=>{for(const a of s){const{width:l,height:u}=a&&a.contentRect||{};r(c=>c.width===l&&c.height===u?c:{width:l,height:u})}},o=new window.ResizeObserver(i);return t.current&&o.observe(t.current,void 0),()=>{o.disconnect()}},[t.current]),{ref:t,...n}}const A0=(e,t,n)=>{const r=f.useRef(null),i=f.useRef(null),o=f.useRef(null),s=f.useRef(0),a=f.useRef(t);a.current=t;const l=n.current;f.useEffect(()=>{const u=()=>{if(i.current===!1&&l!==null){const g=[l.scrollLeft,l.scrollTop];if(o.current?.[0]===g[0]&&o.current?.[1]===g[1])if(s.current>10){o.current=null,i.current=null;return}else s.current++;else s.current=0,a.current(g[0],g[1]),o.current=g;r.current=window.setTimeout(u,8.333333333333334)}},c=()=>{i.current=!0,o.current=null,r.current!==null&&(window.clearTimeout(r.current),r.current=null)},d=g=>{g.touches.length===0&&(i.current=!1,s.current=0,r.current=window.setTimeout(u,8.333333333333334))};if(e&&l!==null){const g=l;return g.addEventListener("touchstart",c),g.addEventListener("touchend",d),()=>{g.removeEventListener("touchstart",c),g.removeEventListener("touchend",d),r.current!==null&&window.clearTimeout(r.current)}}},[e,l])},H0=()=>e=>e.isSafari?"scroll":"auto",z0=sn("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[H0()]}}),V0=33554400,$0=5e6;function N0(e){const[t,n]=f.useState(!1),r=typeof window>"u"?null:window,i=f.useRef(0);return xn("touchstart",f.useCallback(()=>{window.clearTimeout(i.current),n(!0)},[]),r,!0,!1),xn("touchend",f.useCallback(o=>{o.touches.length===0&&(i.current=window.setTimeout(()=>n(!1),e))},[e]),r,!0,!1),t}const B0=e=>{const{children:t,clientHeight:n,scrollHeight:r,scrollWidth:i,update:o,draggable:s,className:a,preventDiagonalScrolling:l=!1,paddingBottom:u=0,paddingRight:c=0,rightElement:d,rightElementProps:g,kineticScrollPerfHack:h=!1,scrollRef:p,initialSize:m}=e,y=[],b=g?.sticky??!1,v=g?.fill??!1,C=f.useRef(0),E=f.useRef(0),I=f.useRef(null),R=typeof window>"u"?1:window.devicePixelRatio,P=f.useRef(R);f.useEffect(()=>{if(P.current!==R){C.current=0,E.current=0,P.current=R;const de=I.current;de!==null&&k.current(de.scrollLeft,de.scrollTop)}},[R]);const x=f.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),S=f.useRef(null),F=N0(200),[D,M]=f.useState(!0),T=f.useRef(0);f.useLayoutEffect(()=>{if(!D||F||x.current.lockDirection===void 0)return;const de=I.current;if(de===null)return;const[re,H]=x.current.lockDirection;re!==void 0?de.scrollLeft=re:H!==void 0&&(de.scrollTop=H),x.current.lockDirection=void 0},[F,D]);const O=f.useCallback((de,re)=>{const H=I.current;if(H===null)return;re=re??H.scrollTop,de=de??H.scrollLeft;const L=x.current.scrollTop,X=x.current.scrollLeft,te=de-X,ie=re-L;F&&te!==0&&ie!==0&&(Math.abs(te)>3||Math.abs(ie)>3)&&l&&x.current.lockDirection===void 0&&(x.current.lockDirection=Math.abs(te)<Math.abs(ie)?[X,void 0]:[void 0,L]);const Se=x.current.lockDirection;de=Se?.[0]??de,re=Se?.[1]??re,x.current.scrollLeft=de,x.current.scrollTop=re;const me=H.clientWidth,nt=H.clientHeight,xe=re,gt=E.current-xe,Mt=H.scrollHeight-nt;E.current=xe;let Ue;if(Mt>0&&r>H.scrollHeight+5)if(Math.abs(gt)>2e3||xe===0||xe===Mt){const ne=Math.max(0,Math.min(1,xe/Mt)),oe=r-nt;Ue=ne*oe,C.current=Ue}else C.current-=gt,Ue=C.current;else Ue=xe,C.current=Ue;Ue=Math.max(0,Math.min(Ue,r-nt)),C.current=Ue,Se!==void 0&&(window.clearTimeout(T.current),M(!1),T.current=window.setTimeout(()=>M(!0),200)),o({x:de,y:Ue,width:me-c,height:nt-u,paddingRight:S.current?.clientWidth??0})},[u,c,r,o,l,F]);A0(h&&da.value,O,I);const k=f.useRef(O);k.current=O;const $=f.useRef(),G=f.useRef(!1);f.useLayoutEffect(()=>{G.current?O():G.current=!0},[O,u,c]);const j=f.useCallback(de=>{I.current=de,p!==void 0&&(p.current=de)},[p]);let ue=0,Q=0;const ee=Math.min(r,V0);for(y.push(f.createElement("div",{key:ue++,style:{width:i,height:0}}));Q<ee;){const de=Math.min($0,ee-Q);y.push(f.createElement("div",{key:ue++,style:{width:0,height:de}})),Q+=de}const{ref:ce,width:ae,height:ge}=_0(m);return typeof window<"u"&&($.current?.height!==ge||$.current?.width!==ae)&&(window.setTimeout(()=>k.current(),0),$.current={width:ae,height:ge}),(ae??0)===0||(ge??0)===0?f.createElement("div",{ref:ce}):f.createElement("div",{ref:ce},f.createElement(z0,{isSafari:da.value},f.createElement("div",{className:"dvn-underlay"},t),f.createElement("div",{ref:j,style:$.current,draggable:s,onDragStart:de=>{s||(de.stopPropagation(),de.preventDefault())},className:"dvn-scroller "+(a??""),onScroll:()=>O()},f.createElement("div",{className:"dvn-scroll-inner"+(d===void 0?" dvn-hidden":"")},f.createElement("div",{className:"dvn-stack"},y),d!==void 0&&f.createElement(f.Fragment,null,!v&&f.createElement("div",{className:"dvn-spacer"}),f.createElement("div",{ref:S,style:{height:ge,maxHeight:n-Math.ceil(R%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:c,flexGrow:v?1:void 0,right:b?c??0:void 0,pointerEvents:"auto"}},d))))))},W0=e=>{const{columns:t,rows:n,rowHeight:r,headerHeight:i,groupHeaderHeight:o,enableGroups:s,freezeColumns:a,experimental:l,nonGrowWidth:u,clientSize:c,className:d,onVisibleRegionChanged:g,scrollRef:h,preventDiagonalScrolling:p,rightElement:m,rightElementProps:y,overscrollX:b,overscrollY:v,initialSize:C,smoothScrollX:E=!1,smoothScrollY:I=!1,isDraggable:R}=e,{paddingRight:P,paddingBottom:x}=l??{},[S,F]=c,D=f.useRef(),M=f.useRef(),T=f.useRef(),O=f.useRef(),k=u+Math.max(0,b??0);let $=s?i+o:i;if(typeof r=="number")$+=n*r;else for(let Q=0;Q<n;Q++)$+=r(Q);v!==void 0&&($+=v);const G=f.useRef(),j=f.useCallback(()=>{if(G.current===void 0)return;const Q={...G.current};let ee=0,ce=Q.x<0?-Q.x:0,ae=0,ge=0;Q.x=Q.x<0?0:Q.x;let de=0;for(let ie=0;ie<a;ie++)de+=t[ie].width;for(const ie of t){const Se=ee-de;if(Q.x>=Se+ie.width)ee+=ie.width,ge++,ae++;else if(Q.x>Se)ee+=ie.width,E?ce+=Se-Q.x:ge++,ae++;else if(Q.x+Q.width>Se)ee+=ie.width,ae++;else break}let re=0,H=0,L=0;if(typeof r=="number")I?(H=Math.floor(Q.y/r),re=H*r-Q.y):H=Math.ceil(Q.y/r),L=Math.ceil(Q.height/r)+H,re<0&&L++;else{let ie=0;for(let Se=0;Se<n;Se++){const me=r(Se),nt=ie+(I?0:me/2);if(Q.y>=ie+me)ie+=me,H++,L++;else if(Q.y>nt)ie+=me,I?re+=nt-Q.y:H++,L++;else if(Q.y+Q.height>me/2+ie)ie+=me,L++;else break}}H=Math.max(0,Math.min(H,n-1)),L=Math.max(H,Math.min(L,n));const X={x:ge,y:H,width:ae-ge,height:L-H},te=D.current;(te===void 0||te.y!==X.y||te.x!==X.x||te.height!==X.height||te.width!==X.width||M.current!==ce||T.current!==re||Q.width!==O.current?.[0]||Q.height!==O.current?.[1])&&(g?.({x:ge,y:H,width:ae-ge,height:L-H},Q.width,Q.height,Q.paddingRight??0,ce,re),D.current=X,M.current=ce,T.current=re,O.current=[Q.width,Q.height])},[t,r,n,g,a,E,I]),ue=f.useCallback(Q=>{G.current=Q,j()},[j]);return f.useEffect(()=>{j()},[j]),f.createElement(B0,{scrollRef:h,className:d,kineticScrollPerfHack:l?.kineticScrollPerfHack,preventDiagonalScrolling:p,draggable:R===!0||typeof R=="string",scrollWidth:k+(P??0),scrollHeight:$+(x??0),clientHeight:F,rightElement:m,paddingBottom:x,paddingRight:P,rightElementProps:y,update:ue,initialSize:C},f.createElement(F0,{eventTargetRef:h,width:S,height:F,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},U0=sn("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),q0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),G0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),X0=f.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},f.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),Y0=10,j0=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:o,searchValue:s,searchResults:a,onSearchValueChange:l,getCellsForSelection:u,onSearchResultsChanged:c,showSearch:d=!1,onSearchClose:g}=e,[h]=f.useState(()=>"search-box-"+Math.round(Math.random()*1e3)),[p,m]=f.useState(""),y=s??p,b=f.useCallback(ee=>{m(ee),l?.(ee)},[l]),[v,C]=f.useState(),E=f.useRef(v);E.current=v,f.useEffect(()=>{a!==void 0&&(a.length>0?C(ee=>({rowsSearched:r,results:a.length,selectedIndex:ee?.selectedIndex??-1})):C(void 0))},[r,a]);const I=f.useRef();I.current===void 0&&(I.current=new AbortController);const R=f.useRef(),[P,x]=f.useState([]),S=a??P,F=f.useCallback(()=>{R.current!==void 0&&(window.cancelAnimationFrame(R.current),R.current=void 0),I.current!==void 0&&I.current.abort(),I.current=new AbortController},[]),D=f.useRef(n);D.current=n;const M=f.useCallback(ee=>{const ce=new RegExp(ee.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let ae=D.current,ge=Math.min(10,r),de=0;C(void 0),x([]);const re=[],H=async()=>{if(u===void 0)return;const L=performance.now(),X=r-de;let te=u({x:0,y:ae,width:i.length,height:Math.min(ge,X,r-ae)},I.current.signal);typeof te=="function"&&(te=await te());let ie=!1;for(const[Mt,Ue]of te.entries())for(const[ne,oe]of Ue.entries()){let we;switch(oe.kind){case Z.Text:case Z.Number:we=oe.displayData;break;case Z.Uri:case Z.Markdown:we=oe.data;break;case Z.Boolean:we=typeof oe.data=="boolean"?oe.data.toString():void 0;break;case Z.Image:case Z.Bubble:we=oe.data.join("🐳");break;case Z.Custom:we=oe.copyData;break}we!==void 0&&ce.test(we)&&(re.push([ne,Mt+ae]),ie=!0)}const Se=performance.now();ie&&x([...re]),de+=te.length,zn(de<=r);const me=E.current?.selectedIndex??-1;C({results:re.length,rowsSearched:de,selectedIndex:me}),c?.(re,me),ae+ge>=r?ae=0:ae+=ge;const nt=Se-L,xe=Math.max(nt,1),gt=Y0/xe;ge=Math.ceil(ge*gt),de<r&&re.length<1e3&&(R.current=window.requestAnimationFrame(H))};F(),R.current=window.requestAnimationFrame(H)},[F,i.length,u,c,r]),T=f.useCallback(()=>{g?.(),C(void 0),x([]),c?.([],-1),F(),t?.current?.focus()},[F,t,g,c]),O=f.useCallback(ee=>{b(ee.target.value),a===void 0&&(ee.target.value===""?(C(void 0),x([]),F()):M(ee.target.value))},[M,F,b,a]);f.useEffect(()=>{o.current!==null&&(b(""),C(void 0),P.length>0&&(x([]),c?.([],-1)),d?o.current.focus({preventScroll:!0}):F())},[d,o]);const k=f.useCallback(ee=>{if(ee?.stopPropagation?.(),v===void 0||v.results===0)return;const ce=(v.selectedIndex+1)%v.results;C({...v,selectedIndex:ce}),c?.(S,ce)},[v,c,S]),$=f.useCallback(ee=>{if(ee?.stopPropagation?.(),v===void 0||v.results===0)return;let ce=(v.selectedIndex-1)%v.results;ce<0&&(ce+=v.results),C({...v,selectedIndex:ce}),c?.(S,ce)},[c,S,v]),G=f.useCallback(ee=>{(ee.ctrlKey||ee.metaKey)&&ee.nativeEvent.code==="KeyF"||ee.key==="Escape"?(T(),ee.stopPropagation(),ee.preventDefault()):ee.key==="Enter"&&(ee.shiftKey?$():k())},[T,k,$]);f.useEffect(()=>()=>{F()},[F]);const[j,ue]=f.useState(!1);f.useEffect(()=>{if(d)ue(!0);else{const ee=setTimeout(()=>ue(!1),150);return()=>clearTimeout(ee)}},[d]);const Q=f.useMemo(()=>{if(!d&&!j)return null;let ee;v!==void 0&&(ee=v.results>=1e3?"over 1000":`${v.results} result${v.results!==1?"s":""}`,v.selectedIndex>=0&&(ee=`${v.selectedIndex+1} of ${ee}`));const ce=de=>{de.stopPropagation()},ge={width:`${r>0?Math.floor((v?.rowsSearched??0)/r*100):0}%`};return f.createElement(U0,{className:"gdg-search-bar"+(d?"":" out"),onMouseDown:ce,onMouseMove:ce,onMouseUp:ce,onClick:ce},f.createElement("div",{className:"gdg-search-bar-inner"},f.createElement("input",{id:h,"aria-hidden":!d,"data-testid":"search-input",ref:o,onChange:O,value:y,tabIndex:d?void 0:-1,onKeyDownCapture:G}),f.createElement("button",{type:"button","aria-label":"Previous Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:$,disabled:(v?.results??0)===0},q0),f.createElement("button",{type:"button","aria-label":"Next Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:k,disabled:(v?.results??0)===0},G0),g!==void 0&&f.createElement("button",{type:"button","aria-label":"Close Search","aria-hidden":!d,"data-testid":"search-close-button",tabIndex:d?void 0:-1,onClick:T},X0)),v!==void 0?f.createElement(f.Fragment,null,f.createElement("div",{className:"gdg-search-status"},f.createElement("div",{"data-testid":"search-result-area"},ee)),f.createElement("div",{className:"gdg-search-progress",style:ge})):f.createElement("div",{className:"gdg-search-status"},f.createElement("label",{htmlFor:h},"Type to search")))},[d,j,v,r,h,o,O,y,G,$,k,g,T]);return f.createElement(f.Fragment,null,f.createElement(W0,{prelightCells:S,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),Q)};class K0 extends f.PureComponent{wrapperRef=f.createRef();componentDidMount(){const t=this.props.customEventTarget??document;t.addEventListener("pointerdown",this.clickOutside,!0),t.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){const t=this.props.customEventTarget??document;t.removeEventListener("pointerdown",this.clickOutside,!0),t.removeEventListener("contextmenu",this.clickOutside,!0)}clickOutside=t=>{if(!(this.props.isOutsideClick&&!this.props.isOutsideClick(t))&&this.wrapperRef.current!==null&&!this.wrapperRef.current.contains(t.target)){let n=t.target;for(;n!==null;){if(n.classList.contains("click-outside-ignore"))return;n=n.parentElement}this.props.onClickOutside()}};render(){const{onClickOutside:t,isOutsideClick:n,customEventTarget:r,...i}=this.props;return f.createElement("div",{...i,ref:this.wrapperRef},this.props.children)}}const Z0=()=>e=>Math.max(16,e.targetHeight-10),J0=sn("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[Z0(),"px"]}}),Q0=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:o}=e,[s,a]=$t.useState(n);return $t.createElement(K0,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:r},$t.createElement(J0,{targetHeight:t.height,"data-testid":"group-rename-input",value:s,onBlur:r,onFocus:l=>l.target.setSelectionRange(0,s.length),onChange:l=>a(l.target.value),onKeyDown:l=>{l.key==="Enter"?o(s):l.key==="Escape"&&r()},autoFocus:!0}))};function ev(e,t){return e===void 0?!1:e.length>1&&e.startsWith("_")?Number.parseInt(e.slice(1))===t.keyCode:e.length===1&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}function at(e,t,n){const r=Xd(e,t);return r&&(n.didMatch=!0),r}function Xd(e,t){if(e.length===0)return!1;if(e.includes("|")){const l=e.split("|");for(const u of l)if(Xd(u,t))return!0;return!1}let n=!1,r=!1,i=!1,o=!1;const s=e.split("+"),a=s.pop();if(!ev(a,t))return!1;if(s[0]==="any")return!0;for(const l of s)switch(l){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":fa.value?o=!0:n=!0;break}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function tv(e,t,n,r,i,o,s){const a=$t.useCallback((c,d,g,h)=>{(o==="cell"||o==="multi-cell")&&c!==void 0&&(c={...c,range:{x:c.cell[0],y:c.cell[1],width:1,height:1}}),!s&&c!==void 0&&c.range.width>1&&(c={...c,range:{...c.range,width:1,x:c.cell[0]}});const p=n==="mixed"&&(g||h==="drag"),m=r==="mixed"&&p,y=i==="mixed"&&p;let b={current:c===void 0?void 0:{...c,rangeStack:h==="drag"?e.current?.rangeStack??[]:[]},columns:m?e.columns:Qe.empty(),rows:y?e.rows:Qe.empty()};g&&(o==="multi-rect"||o==="multi-cell")&&b.current!==void 0&&e.current!==void 0&&(b={...b,current:{...b.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(b,d)},[r,e,n,o,s,i,t]),l=$t.useCallback((c,d,g)=>{c=c??e.rows,d!==void 0&&(c=c.add(d));let h;if(i==="exclusive"&&c.length>0)h={current:void 0,columns:Qe.empty(),rows:c};else{const p=g&&n==="mixed",m=g&&r==="mixed";h={current:p?e.current:void 0,columns:m?e.columns:Qe.empty(),rows:c}}t(h,!1)},[r,e,n,i,t]),u=$t.useCallback((c,d,g)=>{c=c??e.columns,d!==void 0&&(c=c.add(d));let h;if(r==="exclusive"&&c.length>0)h={current:void 0,rows:Qe.empty(),columns:c};else{const p=g&&n==="mixed",m=g&&i==="mixed";h={current:p?e.current:void 0,rows:m?e.rows:Qe.empty(),columns:c}}t(h,!1)},[r,e,n,i,t]);return[a,l,u]}function nv(e,t,n,r,i){const o=f.useCallback(u=>{if(e===!0){const c=[];for(let d=u.y;d<u.y+u.height;d++){const g=[];for(let h=u.x;h<u.x+u.width;h++)h<0||d>=i?g.push({kind:Z.Loading,allowOverlay:!1}):g.push(t([h,d]));c.push(g)}return c}return e?.(u,r.signal)??[]},[r.signal,t,e,i]),s=e!==void 0?o:void 0,a=f.useCallback(u=>{if(s===void 0)return[];const c={...u,x:u.x-n};if(c.x<0){c.x=0,c.width--;const d=s(c,r.signal);return typeof d=="function"?async()=>(await d()).map(g=>[{kind:Z.Loading,allowOverlay:!1},...g]):d.map(g=>[{kind:Z.Loading,allowOverlay:!1},...g])}return s(c,r.signal)},[r.signal,s,n]);return[e!==void 0?a:void 0,s]}function rv(e){if(e.copyData!==void 0)return{formatted:e.copyData,rawValue:e.copyData,format:"string",doNotEscape:!0};switch(e.kind){case Z.Boolean:return{formatted:e.data===!0?"TRUE":e.data===!1?"FALSE":e.data===Gs?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case Z.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case Z.Image:case Z.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case Z.Drilldown:return{formatted:e.data.map(t=>t.text),rawValue:e.data.map(t=>t.text),format:"string-array"};case Z.Text:return{formatted:e.displayData??e.data,rawValue:e.data,format:"string"};case Z.Uri:return{formatted:e.displayData??e.data,rawValue:e.data,format:"url"};case Z.Markdown:case Z.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case Z.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case Z.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case Z.Protected:return{formatted:"************",rawValue:"",format:"string"};default:co()}}function iv(e,t){return e.map((r,i)=>{const o=t[i];return r.map(s=>s.span!==void 0&&s.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:rv(s))})}function $u(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e=`"${e.replace(/"/g,'""')}"`),e}function ov(e){const t=[];for(const n of e){const r=[];for(const i of n)i.format==="url"?r.push(i.rawValue?.toString()??""):i.format==="string-array"?r.push(i.formatted.map(o=>$u(o,!0)).join(",")):r.push(i.doNotEscape===!0?i.formatted:$u(i.formatted,!1));t.push(r.join("	"))}return t.join(`
`)}function fs(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,t=>"<span> </span>".repeat(t.length))}function Nu(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function av(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function sv(e){const t=[];t.push('<style type="text/css"><!--br {mso-data-placement:same-cell;}--></style>',"<table><tbody>");for(const n of e){t.push("<tr>");for(const r of n){const i=`gdg-format="${r.format}"`;r.format==="url"?t.push(`<td ${i}><a href="${r.rawValue}">${fs(r.formatted)}</a></td>`):r.format==="string-array"?t.push(`<td ${i}><ol>${r.formatted.map((o,s)=>`<li gdg-raw-value=${Nu(r.rawValue[s])}>`+fs(o)+"</li>").join("")}</ol></td>`):t.push(`<td gdg-raw-value=${Nu(r.rawValue?.toString()??"")} ${i}>${fs(r.formatted)}</td>`)}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}function lv(e,t){const n=iv(e,t),r=ov(n),i=sv(n);return{textPlain:r,textHtml:i}}function Bu(e){const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(n===null)return;const r=[n],i=[];let o;for(;r.length>0;){const s=r.pop();if(s===void 0)break;if(s instanceof HTMLTableElement||s.nodeName==="TBODY")r.push(...[...s.children].reverse());else if(s instanceof HTMLTableRowElement)o!==void 0&&i.push(o),o=[],r.push(...[...s.children].reverse());else if(s instanceof HTMLTableCellElement){const a=s.cloneNode(!0),u=a.children.length===1&&a.children[0].nodeName==="P"?a.children[0]:null,c=u?.children.length===1&&u.children[0].nodeName==="FONT",d=a.querySelectorAll("br");for(const p of d)p.replaceWith(`
`);const g=a.getAttribute("gdg-raw-value"),h=a.getAttribute("gdg-format")??"string";if(a.querySelector("a")!==null)o?.push({rawValue:a.querySelector("a")?.getAttribute("href")??"",formatted:a.textContent??"",format:h});else if(a.querySelector("ol")!==null){const p=a.querySelectorAll("li");o?.push({rawValue:[...p].map(m=>m.getAttribute("gdg-raw-value")??""),formatted:[...p].map(m=>m.textContent??""),format:"string-array"})}else if(g!==null)o?.push({rawValue:av(g),formatted:a.textContent??"",format:h});else{let p=a.textContent??"";c&&(p=p.replace(/\n(?!\n)/g,"")),o?.push({rawValue:p??"",formatted:p??"",format:h})}}}return o!==void 0&&i.push(o),i}function uv(e,t,n,r,i){const o=e;if(r==="allowPartial"||e.current===void 0||t===void 0)return e;let s=!1;do{if(e?.current===void 0)break;const a=e.current?.range,l=[];if(a.width>2){const d=t({x:a.x,y:a.y,width:1,height:a.height},i.signal);if(typeof d=="function")return o;l.push(...d);const g=t({x:a.x+a.width-1,y:a.y,width:1,height:a.height},i.signal);if(typeof g=="function")return o;l.push(...g)}else{const d=t({x:a.x,y:a.y,width:a.width,height:a.height},i.signal);if(typeof d=="function")return o;l.push(...d)}let u=a.x-n,c=a.x+a.width-1-n;for(const d of l)for(const g of d)g.span!==void 0&&(u=Math.min(g.span[0],u),c=Math.max(g.span[1],c));u===a.x-n&&c===a.x+a.width-1-n?s=!0:e={current:{cell:e.current.cell??[0,0],range:{x:u+n,y:a.y,width:c-u+1,height:a.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!s);return e}function Wu(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function cv(e){let t;(function(a){a[a.None=0]="None",a[a.inString=1]="inString",a[a.inStringPostQuote=2]="inStringPostQuote"})(t||(t={}));const n=[];let r=[],i=0,o=t.None;e=e.replace(/\r\n/g,`
`);let s=0;for(const a of e){switch(o){case t.None:a==="	"||a===`
`?(r.push(e.slice(i,s)),i=s+1,a===`
`&&(n.push(r),r=[])):a==='"'&&(o=t.inString);break;case t.inString:a==='"'&&(o=t.inStringPostQuote);break;case t.inStringPostQuote:a==='"'?o=t.inString:((a==="	"||a===`
`)&&(r.push(Wu(e.slice(i,s))),i=s+1,a===`
`&&(n.push(r),r=[])),o=t.None);break}s++}return i<e.length&&r.push(Wu(e.slice(i,e.length))),n.push(r),n.map(a=>a.map(l=>({rawValue:l,formatted:l,format:"string"})))}function Uu(e,t,n){const r=lv(e,t),i=a=>{window.navigator.clipboard?.writeText(a)},o=(a,l)=>window.navigator.clipboard?.write===void 0?!1:(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([a],{type:"text/plain"}),"text/html":new Blob([l],{type:"text/html"})})]),!0),s=(a,l)=>{try{if(n===void 0||n.clipboardData===null)throw new Error("No clipboard data");n?.clipboardData?.setData("text/plain",a),n?.clipboardData?.setData("text/html",l)}catch{o(a,l)||i(a)}};window.navigator.clipboard?.write!==void 0||n?.clipboardData!==void 0?s(r.textPlain,r.textHtml):i(r.textPlain),n?.preventDefault()}function Yd(e){return e!==!0}function qu(e){return typeof e=="string"?e:`${e}px`}const dv=()=>e=>e.innerWidth,fv=()=>e=>e.innerHeight,hv=sn("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[dv()],"wmyidgi-1":[fv()]}}),gv=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return f.createElement(hv,{innerHeight:qu(n),innerWidth:qu(t),...i},r)},pv=2,mv=1300;function vv(e,t,n){const r=$t.useRef(0),[i,o]=e??[0,0];$t.useEffect(()=>{if(i===0&&o===0){r.current=0;return}let s=!1,a=0;const l=u=>{if(!s){if(a===0)a=u;else{const c=u-a;r.current=Math.min(1,r.current+c/mv);const d=r.current**1.618*c*pv;t.current?.scrollBy(i*d,o*d),a=u,n?.()}window.requestAnimationFrame(l)}};return window.requestAnimationFrame(l),()=>{s=!0}},[t,i,o,n])}function bv({rowHeight:e,headerHeight:t,groupHeaderHeight:n,theme:r,overscrollX:i,overscrollY:o,scaleToRem:s,remSize:a}){const[l,u,c,d,g,h]=$t.useMemo(()=>{if(!s||a===16)return[e,t,n,r,i,o];const p=a/16,m=e,y=Pd();return[typeof m=="number"?m*p:b=>Math.ceil(m(b)*p),Math.ceil(t*p),Math.ceil(n*p),{...r,headerIconSize:(r?.headerIconSize??y.headerIconSize)*p,cellHorizontalPadding:(r?.cellHorizontalPadding??y.cellHorizontalPadding)*p,cellVerticalPadding:(r?.cellVerticalPadding??y.cellVerticalPadding)*p},Math.ceil((i??0)*p),Math.ceil((o??0)*p)]},[n,t,i,o,a,e,s,r]);return{rowHeight:l,headerHeight:u,groupHeaderHeight:c,theme:d,overscrollX:g,overscrollY:h}}const Ir={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function st(e,t){return e===!0?t:e===!1?"":e}function Gu(e){const t=fa.value;return{activateCell:st(e.activateCell," |Enter|shift+Enter"),clear:st(e.clear,"any+Escape"),closeOverlay:st(e.closeOverlay,"any+Escape"),acceptOverlayDown:st(e.acceptOverlayDown,"Enter"),acceptOverlayUp:st(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:st(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:st(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:st(e.delete,t?"Backspace|Delete":"Delete"),downFill:st(e.downFill,"primary+_68"),scrollToSelectedCell:st(e.scrollToSelectedCell,"primary+Enter"),goDownCell:st(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:st(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:st(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:st(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:st(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:st(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:st(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:st(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:st(e.goToFirstCell,"primary+Home"),goToFirstColumn:st(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:st(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:st(e.goToLastCell,"primary+End"),goToLastColumn:st(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:st(e.goToLastRow,"primary+ArrowDown"),goToNextPage:st(e.goToNextPage,"PageDown"),goToPreviousPage:st(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:st(e.rightFill,"primary+_82"),search:st(e.search,"primary+f"),selectAll:st(e.selectAll,"primary+a"),selectColumn:st(e.selectColumn,"ctrl+ "),selectGrowDown:st(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:st(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:st(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:st(e.selectGrowUp,"shift+ArrowUp"),selectRow:st(e.selectRow,"shift+ "),selectToFirstCell:st(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:st(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:st(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:st(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:st(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:st(e.selectToLastRow,"primary+shift+ArrowDown")}}function wv(e){const t=Ip(e);return $t.useMemo(()=>{if(t===void 0)return Gu(Ir);const n={...t,goToNextPage:t?.goToNextPage??t?.pageDown??Ir.goToNextPage,goToPreviousPage:t?.goToPreviousPage??t?.pageUp??Ir.goToPreviousPage,goToFirstCell:t?.goToFirstCell??t?.first??Ir.goToFirstCell,goToLastCell:t?.goToLastCell??t?.last??Ir.goToLastCell,selectToFirstCell:t?.selectToFirstCell??t?.first??Ir.selectToFirstCell,selectToLastCell:t?.selectToLastCell??t?.last??Ir.selectToLastCell};return Gu({...Ir,...n})},[t])}function yv(e){function t(r,i,o){if(typeof r=="number")return{headerIndex:r,isCollapsed:!1,depth:i,path:o};const s={headerIndex:r.headerIndex,isCollapsed:r.isCollapsed,depth:i,path:o};return r.subGroups!==void 0&&(s.subGroups=r.subGroups.map((a,l)=>t(a,i+1,[...o,l])).sort((a,l)=>a.headerIndex-l.headerIndex)),s}return e.map((r,i)=>t(r,0,[i])).sort((r,i)=>r.headerIndex-i.headerIndex)}function al(e,t){const n=[];function r(a,l,u=!1){let c=l!==null?l-a.headerIndex:t-a.headerIndex;if(a.subGroups!==void 0&&(c=a.subGroups[0].headerIndex-a.headerIndex),c--,n.push({rowIndex:-1,headerIndex:a.headerIndex,contentIndex:-1,skip:u,isCollapsed:a.isCollapsed,depth:a.depth,path:a.path,rows:c}),a.subGroups)for(let d=0;d<a.subGroups.length;d++){const g=d<a.subGroups.length-1?a.subGroups[d+1].headerIndex:l;r(a.subGroups[d],g,u||a.isCollapsed)}}const i=yv(e.groups);for(let a=0;a<i.length;a++){const l=a<i.length-1?i[a+1].headerIndex:null;r(i[a],l)}let o=0,s=0;for(const a of n)a.contentIndex=s,s+=a.rows,a.rowIndex=o,o+=a.isCollapsed?1:a.rows+1;return n.filter(a=>a.skip===!1).map(a=>{const{skip:l,...u}=a;return u})}function Hs(e,t){if(t===void 0||al.length===0)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const r of t){if(n===0)return{path:[...r.path,-1],originalIndex:r.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:r.rows};if(r.isCollapsed)n--;else{if(n<=r.rows)return{path:[...r.path,n-1],originalIndex:r.headerIndex+n,isGroupHeader:!1,groupIndex:n-1,contentIndex:r.contentIndex+n-1,groupRows:r.rows};n=n-r.rows-1}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function Cv(e,t,n,r){const i=$t.useMemo(()=>e===void 0?void 0:al(e,t),[e,t]),o=$t.useMemo(()=>i?.reduce((c,d)=>(c[d.rowIndex]=d,c),{}),[i]),s=$t.useMemo(()=>i===void 0?t:i.reduce((c,d)=>c+(d.isCollapsed?1:d.rows+1),0),[i,t]),a=$t.useMemo(()=>e===void 0||typeof n=="number"&&e.height===n?n:c=>o?.[c]?e.height:typeof n=="number"?n:n(c),[o,e,n]),l=$t.useCallback(c=>{if(i===void 0)return c;let d=c;for(const g of i){if(d===0)return;if(d--,!g.isCollapsed){if(d<g.rows)return g.contentIndex+d;d-=g.rows}}return c},[i]),u=Er(r??e?.themeOverride,$t.useCallback(c=>{if(e===void 0)return r?.(c,c,c);if(r===void 0&&e?.themeOverride===void 0)return;const{isGroupHeader:d,contentIndex:g,groupIndex:h}=Hs(c,i);return d?e.themeOverride:r?.(c,h,g)},[i,r,e]));return e===void 0?{rowHeight:a,rows:t,rowNumberMapper:l,getRowThemeOverride:u}:{rowHeight:a,rows:s,rowNumberMapper:l,getRowThemeOverride:u}}function Sv(e,t){const n=$t.useMemo(()=>e===void 0?void 0:al(e,t),[e,t]);return{getRowGroupingForPath:Kd,updateRowGroupingByPath:jd,mapper:$t.useCallback(r=>{if(typeof r=="number")return Hs(r,n);const i=Hs(r[1],n);return{...i,originalIndex:[r[0],i.originalIndex]}},[n])}}function jd(e,t,n){const[r,...i]=t;return i[0]===-1?e.map((o,s)=>s===r?{...o,...n}:o):e.map((o,s)=>s===r?{...o,subGroups:jd(o.subGroups??[],i,n)}:o)}function Kd(e,t){const[n,...r]=t;return r[0]===-1?e[n]:Kd(e[n].subGroups??[],r)}function xv(e,t){const[n]=f.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(r){const i=n.value;i!==r&&(n.value=r,n.callback(r,i))}}}));return n.callback=t,n.facade}function kv(e,t,n,r,i){const[o,s]=f.useMemo(()=>[t!==void 0&&typeof n=="number"?Math.floor(t/n):0,t!==void 0&&typeof n=="number"?-(t%n):0],[t,n]),a=f.useMemo(()=>({x:r.current.x,y:o,width:r.current.width??1,height:r.current.height??1,ty:s}),[r,s,o]),[l,u,c]=Rp(a),d=f.useRef(i);d.current=i;const g=xv(null,m=>{m!==null&&t!==void 0?m.scrollTop=t:m!==null&&e!==void 0&&(m.scrollLeft=e)}),h=(l.height??1)>1;f.useLayoutEffect(()=>{if(t!==void 0&&g.current!==null&&h){if(g.current.scrollTop===t)return;g.current.scrollTop=t,g.current.scrollTop!==t&&c(),d.current()}},[t,h,c,g]);const p=(l.width??1)>1;return f.useLayoutEffect(()=>{if(e!==void 0&&g.current!==null&&p){if(g.current.scrollLeft===e)return;g.current.scrollLeft=e,g.current.scrollLeft!==e&&c(),d.current()}},[e,p,c,g]),{visibleRegion:l,setVisibleRegion:u,scrollRef:g}}const Mv=f.lazy(async()=>await Us(()=>import("./data-grid-overlay-editor.DRTHOydk.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12]),import.meta.url));let Rv=0;function Iv(e){return Tm(Lu(Lu(e).filter(t=>t.span!==void 0).map(t=>Or((t.span?.[0]??0)+1,(t.span?.[1]??0)+1))))}function Go(e,t){return e===void 0||t===0||e.columns.length===0&&e.current===void 0?e:{current:e.current===void 0?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map(n=>({...n,x:n.x+t}))},rows:e.rows,columns:e.columns.offset(t)}}const Xo={kind:Z.Loading,allowOverlay:!1},Yo={columns:Qe.empty(),rows:Qe.empty(),current:void 0},Ev=(e,t)=>{const[n,r]=f.useState(Yo),[i,o]=f.useState(),s=f.useRef(null),a=f.useRef(null),[l,u]=f.useState(),c=f.useRef(),d=typeof window>"u"?null:window,{imageEditorOverride:g,getRowThemeOverride:h,markdownDivCreateNode:p,width:m,height:y,columns:b,rows:v,getCellContent:C,onCellClicked:E,onCellActivated:I,onFillPattern:R,onFinishedEditing:P,coercePasteValue:x,drawHeader:S,drawCell:F,editorBloom:D,onHeaderClicked:M,onColumnProposeMove:T,rangeSelectionColumnSpanning:O=!0,spanRangeBehavior:k="default",onGroupHeaderClicked:$,onCellContextMenu:G,className:j,onHeaderContextMenu:ue,getCellsForSelection:Q,onGroupHeaderContextMenu:ee,onGroupHeaderRenamed:ce,onCellEdited:ae,onCellsEdited:ge,onSearchResultsChanged:de,searchResults:re,onSearchValueChange:H,searchValue:L,onKeyDown:X,onKeyUp:te,keybindings:ie,editOnType:Se=!0,onRowAppended:me,onColumnAppended:nt,onColumnMoved:xe,validateCell:gt,highlightRegions:Mt,rangeSelect:Ue="rect",columnSelect:ne="multi",rowSelect:oe="multi",rangeSelectionBlending:we="exclusive",columnSelectionBlending:fe="exclusive",rowSelectionBlending:ye="exclusive",onDelete:$e,onDragStart:Pe,onMouseMove:it,onPaste:Me,copyHeaders:qe=!1,freezeColumns:Te=0,cellActivationBehavior:Ee="second-click",rowSelectionMode:De="auto",onHeaderMenuClick:ft,onHeaderIndicatorClick:ot,getGroupDetails:Xe,rowGrouping:wt,onSearchClose:yt,onItemHovered:Ft,onSelectionCleared:It,showSearch:dn,onVisibleRegionChanged:Zt,gridSelection:fn,onGridSelectionChange:Ot,minColumnWidth:Ct=50,maxColumnWidth:Tn=500,maxColumnAutoWidth:pt,provideEditor:en,trailingRowOptions:Bt,freezeTrailingRows:Le=0,allowedFillDirections:St="orthogonal",scrollOffsetX:mt,scrollOffsetY:Dn,verticalBorder:Gt,onDragOverCell:hn,onDrop:On,onColumnResize:Xt,onColumnResizeEnd:tn,onColumnResizeStart:Be,customRenderers:Pt,fillHandle:$n,experimental:xt,fixedShadowX:Et,fixedShadowY:kn,headerIcons:cr,imageWindowLoader:Nn,initialSize:N,isDraggable:We,onDragLeave:Ge,onRowMoved:Rt,overscrollX:gn,overscrollY:ln,preventDiagonalScrolling:Mn,rightElement:wn,rightElementProps:Wt,trapFocus:yn=!1,smoothScrollX:Pn,smoothScrollY:Ce,scaleToRem:_t=!1,rowHeight:Jt=34,headerHeight:Ln=36,groupHeaderHeight:un=Ln,theme:Yt,isOutsideClick:jt,renderers:Cn,resizeIndicator:Bn,scrollToActiveCell:Ii=!0,drawFocusRing:Ar=!0,portalElementRef:Il}=e,Ei=Ar==="no-editor"?i===void 0:Ar,Wn=typeof e.rowMarkers=="string"?void 0:e.rowMarkers,Rn=Wn?.kind??e.rowMarkers??"none",Ti=Wn?.width??e.rowMarkerWidth,Hr=Wn?.startIndex??e.rowMarkerStartIndex??1,dr=Wn?.theme??e.rowMarkerTheme,ei=Wn?.headerTheme,br=Wn?.headerAlwaysVisible,wr=oe!=="multi"||Wn?.headerDisabled===!0,ti=Wn?.checkboxStyle??"square",fr=Math.max(Ct,20),ni=Math.max(Tn,fr),Ta=Math.max(pt??ni,fr),So=f.useMemo(()=>typeof window>"u"?{fontSize:"16px"}:window.getComputedStyle(document.documentElement),[]),{rows:_e,rowNumberMapper:Di,rowHeight:xo,getRowThemeOverride:yr}=Cv(wt,v,Jt,h),Da=f.useMemo(()=>Number.parseFloat(So.fontSize),[So]),{rowHeight:ir,headerHeight:Oi,groupHeaderHeight:ko,theme:Mo,overscrollX:Pi,overscrollY:Oa}=bv({groupHeaderHeight:un,headerHeight:Ln,overscrollX:gn,overscrollY:ln,remSize:Da,rowHeight:xo,scaleToRem:_t,theme:Yt}),or=wv(ie),Cr=Ti??(v>1e4?48:v>1e3?44:v>100?36:32),Un=Rn!=="none",U=Un?1:0,At=Bt!==void 0,hr=Bt?.sticky===!0,[Li,Ro]=f.useState(!1),ri=dn??Li,Pa=f.useCallback(()=>{yt!==void 0?yt():Ro(!1)},[yt]),K=f.useMemo(()=>fn===void 0?void 0:Go(fn,U),[fn,U])??n,In=f.useRef();In.current===void 0&&(In.current=new AbortController),f.useEffect(()=>()=>In?.current.abort(),[]);const[z,se]=nv(Q,C,U,In.current,_e),ke=f.useCallback((w,A,_)=>{if(gt===void 0)return!0;const W=[w[0]-U,w[1]];return gt?.(W,A,_)},[U,gt]),Re=f.useRef(fn),le=f.useCallback((w,A)=>{A&&(w=uv(w,z,U,k,In.current)),Ot!==void 0?(Re.current=Go(w,-U),Ot(Re.current)):r(w)},[Ot,z,U,k]),ve=Er(Xt,f.useCallback((w,A,_,W)=>{Xt?.(b[_-U],A,_-U,W)},[Xt,U,b])),Ye=Er(tn,f.useCallback((w,A,_,W)=>{tn?.(b[_-U],A,_-U,W)},[tn,U,b])),Ie=Er(Be,f.useCallback((w,A,_,W)=>{Be?.(b[_-U],A,_-U,W)},[Be,U,b])),je=Er(S,f.useCallback((w,A)=>S?.({...w,columnIndex:w.columnIndex-U},A)??!1,[S,U])),ht=Er(F,f.useCallback((w,A)=>F?.({...w,col:w.col-U},A)??!1,[F,U])),Qt=f.useCallback(w=>{if($e!==void 0){const A=$e(Go(w,-U));return typeof A=="boolean"?A:Go(A,U)}return!0},[$e,U]),[ct,Tt,et]=tv(K,le,we,fe,ye,Ue,O),Je=f.useMemo(()=>pr(Pd(),Mo),[Mo]),[zt,Ut]=f.useState([0,0,0]),zr=f.useMemo(()=>{if(Cn===void 0)return{};const w={};for(const A of Cn)w[A.kind]=A;return w},[Cn]),nn=f.useCallback(w=>w.kind!==Z.Custom?zr[w.kind]:Pt?.find(A=>A.isMatch(w)),[Pt,zr]);let{sizedColumns:cn,nonGrowWidth:Sr}=ym(b,_e,se,zt[0]-(U===0?0:Cr)-zt[2],fr,Ta,Je,nn,In.current);Rn!=="none"&&(Sr+=Cr);const Lt=f.useMemo(()=>cn.some(w=>w.group!==void 0),[cn]),En=Lt?Oi+ko:Oi,Fn=K.rows.length,ii=Rn==="none"?void 0:Fn===0?!1:Fn===_e?!0:void 0,rt=f.useMemo(()=>Rn==="none"?cn:[{title:"",width:Cr,icon:void 0,hasMenu:!1,style:"normal",themeOverride:dr,rowMarker:ti,rowMarkerChecked:ii,headerRowMarkerTheme:ei,headerRowMarkerAlwaysVisible:br,headerRowMarkerDisabled:wr},...cn],[Rn,cn,Cr,dr,ti,ii,ei,br,wr]),an=f.useRef({height:1,width:1,x:0,y:0}),Yn=f.useRef(!1),{setVisibleRegion:_i,visibleRegion:_n,scrollRef:Vt}=kv(mt,Dn,ir,an,()=>Yn.current=!0);an.current=_n;const La=_n.x+U,Io=_n.y,pn=f.useRef(null),Sn=f.useCallback(w=>{w===!0?pn.current?.focus():window.requestAnimationFrame(()=>{pn.current?.focus()})},[]),mn=At?_e+1:_e,qn=f.useCallback(w=>{const A=U===0?w:w.map(W=>({...W,location:[W.location[0]-U,W.location[1]]})),_=ge?.(A);if(_!==!0)for(const W of A)ae?.(W.location,W.value);return _},[ae,ge,U]),[Vr,Fa]=f.useState(),Eo=K.current!==void 0&&K.current.range.width*K.current.range.height>1?K.current.range:void 0,El=Ei?K.current?.cell:void 0,To=El?.[0],Do=El?.[1],eh=f.useMemo(()=>{if((Mt===void 0||Mt.length===0)&&(Eo??To??Do??Vr)===void 0)return;const w=[];if(Mt!==void 0)for(const A of Mt){const _=rt.length-A.range.x-U;_>0&&w.push({color:A.color,range:{...A.range,x:A.range.x+U,width:Math.min(_,A.range.width)},style:A.style})}return Vr!==void 0&&w.push({color:jr(Je.accentColor,0),range:Vr,style:"dashed"}),Eo!==void 0&&w.push({color:jr(Je.accentColor,.5),range:Eo,style:"solid-outline"}),To!==void 0&&Do!==void 0&&w.push({color:Je.accentColor,range:{x:To,y:Do,width:1,height:1},style:"solid-outline"}),w.length>0?w:void 0},[Vr,Eo,To,Do,Mt,rt.length,Je.accentColor,U]),Tl=f.useRef(rt);Tl.current=rt;const An=f.useCallback(([w,A],_=!1)=>{const W=At&&A===mn-1;if(w===0&&Un){if(W)return Xo;const Y=Di(A);return Y===void 0?Xo:{kind:Xn.Marker,allowOverlay:!1,checkboxStyle:ti,checked:K?.rows.hasIndex(A)===!0,markerKind:Rn==="clickable-number"?"number":Rn,row:Hr+Y,drawHandle:Rt!==void 0,cursor:Rn==="clickable-number"?"pointer":void 0}}else if(W){const V=w===U?Bt?.hint??"":"",B=Tl.current[w];if(B?.trailingRowOptions?.disabled===!0)return Xo;{const J=B?.trailingRowOptions?.hint??V,he=B?.trailingRowOptions?.addIcon??Bt?.addIcon;return{kind:Xn.NewRow,hint:J,allowOverlay:!1,icon:he}}}else{const Y=w-U;if(_||xt?.strict===!0){const B=an.current,J=B.x>Y||Y>B.x+B.width||B.y>A||A>B.y+B.height||A>=za.current,he=Y===B.extras?.selected?.[0]&&A===B.extras?.selected[1];let pe=!1;if(B.extras?.freezeRegions!==void 0){for(const kt of B.extras.freezeRegions)if(Yr(kt,Y,A)){pe=!0;break}}if(J&&!he&&!pe)return Xo}let V=C([Y,A]);return U!==0&&V.span!==void 0&&(V={...V,span:[V.span[0]+U,V.span[1]+U]}),V}},[At,mn,Un,Di,ti,K?.rows,Rn,Hr,Rt,U,Bt?.hint,Bt?.addIcon,xt?.strict,C]),_a=f.useCallback(w=>{let A=Xe?.(w)??{name:w};return ce!==void 0&&w!==""&&(A={icon:A.icon,name:A.name,overrideTheme:A.overrideTheme,actions:[...A.actions??[],{title:"Rename",icon:"renameIcon",onClick:_=>$a({group:A.name,bounds:_.bounds})}]}),A},[Xe,ce]),Oo=f.useCallback(w=>{const[A,_]=w.cell,W=rt[A],q=W?.group!==void 0?_a(W.group)?.overrideTheme:void 0,Y=W?.themeOverride,V=yr?.(_);o({...w,theme:pr(Je,q,Y,V,w.content.themeOverride)})},[yr,rt,_a,Je]),oi=f.useCallback((w,A,_)=>{if(K.current===void 0)return;const[W,q]=K.current.cell,Y=An([W,q]);if(Y.kind!==Z.Boolean&&Y.allowOverlay){let V=Y;if(_!==void 0)switch(V.kind){case Z.Number:{const B=tp(()=>_==="-"?-0:Number.parseFloat(_),0);V={...V,data:Number.isNaN(B)?0:B};break}case Z.Text:case Z.Markdown:case Z.Uri:V={...V,data:_};break}Oo({target:w,content:V,initialValue:_,cell:[W,q],highlight:_===void 0,forceEditMode:_!==void 0,activation:A})}else Y.kind===Z.Boolean&&A.inputType==="keyboard"&&Y.readonly!==!0&&(qn([{location:K.current.cell,value:{...Y,data:Yd(Y.data)}}]),pn.current?.damage([{cell:K.current.cell}]))},[An,K,qn,Oo]),Dl=f.useCallback((w,A)=>{const _=pn.current?.getBounds(w,A);if(_===void 0||Vt.current===null)return;const W=An([w,A]);W.allowOverlay&&Oo({target:_,content:W,initialValue:void 0,highlight:!0,cell:[w,A],forceEditMode:!0,activation:{inputType:"keyboard",key:"Enter"}})},[An,Vt,Oo]),qt=f.useCallback((w,A,_="both",W=0,q=0,Y=void 0)=>{if(Vt.current!==null){const V=pn.current,B=a.current,J=typeof w!="number"?w.unit==="cell"?w.amount:void 0:w,he=typeof A!="number"?A.unit==="cell"?A.amount:void 0:A,pe=typeof w!="number"&&w.unit==="px"?w.amount:void 0,kt=typeof A!="number"&&A.unit==="px"?A.amount:void 0;if(V!==null&&B!==null){let tt={x:0,y:0,width:0,height:0},Ke=0,Ze=0;if((J!==void 0||he!==void 0)&&(tt=V.getBounds((J??0)+U,he??0)??tt,tt.width===0||tt.height===0))return;const ut=B.getBoundingClientRect(),vt=ut.width/B.offsetWidth;if(pe!==void 0&&(tt={...tt,x:pe-ut.left-Vt.current.scrollLeft,width:1}),kt!==void 0&&(tt={...tt,y:kt+ut.top-Vt.current.scrollTop,height:1}),tt!==void 0){const bt={x:tt.x-W,y:tt.y-q,width:tt.width+2*W,height:tt.height+2*q};let di=0;for(let Xa=0;Xa<Te;Xa++)di+=cn[Xa].width;let Bi=0;const Wi=Le+(hr?1:0);Wi>0&&(Bi=Qr(mn,Wi,ir));let sr=di*vt+ut.left+U*Cr*vt,fi=ut.right,Wr=ut.top+En*vt,hi=ut.bottom-Bi*vt;const zo=tt.width+W*2;switch(Y?.hAlign){case"start":fi=sr+zo;break;case"end":sr=fi-zo;break;case"center":sr=Math.floor((sr+fi)/2)-zo/2,fi=sr+zo;break}const Vo=tt.height+q*2;switch(Y?.vAlign){case"start":hi=Wr+Vo;break;case"end":Wr=hi-Vo;break;case"center":Wr=Math.floor((Wr+hi)/2)-Vo/2,hi=Wr+Vo;break}sr>bt.x?Ke=bt.x-sr:fi<bt.x+bt.width&&(Ke=bt.x+bt.width-fi),Wr>bt.y?Ze=bt.y-Wr:hi<bt.y+bt.height&&(Ze=bt.y+bt.height-hi),_==="vertical"||typeof w=="number"&&w<Te?Ke=0:(_==="horizontal"||typeof A=="number"&&A>=mn-Wi)&&(Ze=0),(Ke!==0||Ze!==0)&&(vt!==1&&(Ke/=vt,Ze/=vt),Vt.current.scrollTo({left:Ke+Vt.current.scrollLeft,top:Ze+Vt.current.scrollTop,behavior:Y?.behavior??"auto"}))}}}},[U,Le,Cr,Vt,En,Te,cn,mn,hr,ir]),Aa=f.useRef(Dl),Ha=f.useRef(C);Aa.current=Dl,Ha.current=C;const za=f.useRef(_e);za.current=_e;const Ol=f.useRef(rt.length);Ol.current=rt.length;const xr=f.useCallback(async(w,A=!0,_)=>{if(rt[w]?.trailingRowOptions?.disabled===!0)return;const q=me?.();let Y,V=!0;q!==void 0&&(Y=await q,Y==="top"&&(V=!1),typeof Y=="number"&&(V=!1));let B=0;const J=()=>{if(za.current<=_e){B<500&&window.setTimeout(J,B),B=50+B*2;return}const he=typeof Y=="number"?Y:V?_e:0;Ho.current(w-U,he,"both",0,0,_?{behavior:_}:void 0),ct({cell:[w,he],range:{x:w,y:he,width:1,height:1}},!1,!1,"edit");const pe=Ha.current([w-U,he]);pe.allowOverlay&&bi(pe)&&pe.readonly!==!0&&A&&window.setTimeout(()=>{Aa.current(w,he)},0)};J()},[rt,me,U,_e,ct]),Po=f.useCallback(async(w,A=!0)=>{const _=nt?.();let W,q=!0;_!==void 0&&(W=await _,W==="left"&&(q=!1),typeof W=="number"&&(q=!1));let Y=0;const V=()=>{if(Ol.current<=rt.length){Y<500&&window.setTimeout(V,Y),Y=50+Y*2;return}const B=typeof W=="number"?W:q?rt.length:0;qt(B-U,w),ct({cell:[B,w],range:{x:B,y:w,width:1,height:1}},!1,!1,"edit");const J=Ha.current([B-U,w]);J.allowOverlay&&bi(J)&&J.readonly!==!0&&A&&window.setTimeout(()=>{Aa.current(B,w)},0)};V()},[rt,nt,U,qt,ct]),ai=f.useCallback(w=>{const A=cn[w]?.trailingRowOptions?.targetColumn??Bt?.targetColumn;if(typeof A=="number")return A+(Un?1:0);if(typeof A=="object"){const _=b.indexOf(A);if(_>=0)return _+(Un?1:0)}},[cn,b,Un,Bt?.targetColumn]),kr=f.useRef(),si=f.useRef(),Ai=f.useCallback((w,A)=>{const[_,W]=A;return pr(Je,rt[_]?.themeOverride,yr?.(W),w.themeOverride)},[yr,rt,Je]),{mapper:$r}=Sv(wt,v),jn=wt?.navigationBehavior,Hi=f.useCallback(w=>{const A=fa.value?w.metaKey:w.ctrlKey,_=A&&oe==="multi",W=A&&ne==="multi",[q,Y]=w.location,V=K.columns,B=K.rows,[J,he]=K.current?.cell??[];if(w.kind==="cell"){if(si.current=void 0,Nr.current=[q,Y],q===0&&Un){if(At===!0&&Y===_e||Rn==="number"||oe==="none")return;const pe=An(w.location);if(pe.kind!==Xn.Marker)return;if(Rt!==void 0){const Ke=nn(pe);zn(Ke?.kind===Xn.Marker);const Ze=Ke?.onClick?.({...w,cell:pe,posX:w.localEventX,posY:w.localEventY,bounds:w.bounds,theme:Ai(pe,w.location),preventDefault:()=>{}});if(Ze===void 0||Ze.checked===pe.checked)return}o(void 0),Sn();const kt=B.hasIndex(Y),tt=kr.current;if(oe==="multi"&&(w.shiftKey||w.isLongTouch===!0)&&tt!==void 0&&B.hasIndex(tt)){const Ke=[Math.min(tt,Y),Math.max(tt,Y)+1];_||De==="multi"?Tt(void 0,Ke,!0):Tt(Qe.fromSingleSelection(Ke),void 0,_)}else oe==="multi"&&(_||w.isTouch||De==="multi")?kt?Tt(B.remove(Y),void 0,!0):(Tt(void 0,Y,!0),kr.current=Y):kt&&B.length===1?Tt(Qe.empty(),void 0,A):(Tt(Qe.fromSingleSelection(Y),void 0,A),kr.current=Y)}else if(q>=U&&At&&Y===_e){const pe=ai(q);xr(pe??q)}else if(J!==q||he!==Y){const pe=An(w.location),kt=nn(pe);if(kt?.onSelect!==void 0){let Ze=!1;if(kt.onSelect({...w,cell:pe,posX:w.localEventX,posY:w.localEventY,bounds:w.bounds,preventDefault:()=>Ze=!0,theme:Ai(pe,w.location)}),Ze)return}if(jn==="block"&&$r(Y).isGroupHeader)return;const tt=hr&&Y===_e,Ke=hr&&K!==void 0&&K.current?.cell[1]===_e;if((w.shiftKey||w.isLongTouch===!0)&&J!==void 0&&he!==void 0&&K.current!==void 0&&!Ke){if(tt)return;const Ze=Math.min(q,J),ut=Math.max(q,J),vt=Math.min(Y,he),bt=Math.max(Y,he);ct({...K.current,range:{x:Ze,y:vt,width:ut-Ze+1,height:bt-vt+1}},!0,A,"click"),kr.current=void 0,Sn()}else ct({cell:[q,Y],range:{x:q,y:Y,width:1,height:1}},!0,A,"click"),kr.current=void 0,o(void 0),Sn()}}else if(w.kind==="header")if(Nr.current=[q,Y],o(void 0),Un&&q===0)kr.current=void 0,si.current=void 0,!wr&&oe==="multi"&&(B.length!==_e?Tt(Qe.fromSingleSelection([0,_e]),void 0,A):Tt(Qe.empty(),void 0,A),Sn());else{const pe=si.current;if(ne==="multi"&&(w.shiftKey||w.isLongTouch===!0)&&pe!==void 0&&V.hasIndex(pe)){const kt=[Math.min(pe,q),Math.max(pe,q)+1];W?et(void 0,kt,A):et(Qe.fromSingleSelection(kt),void 0,A)}else W?(V.hasIndex(q)?et(V.remove(q),void 0,A):et(void 0,q,A),si.current=q):ne!=="none"&&(V.hasIndex(q)?et(V.remove(q),void 0,A):et(Qe.fromSingleSelection(q),void 0,A),si.current=q);kr.current=void 0,Sn()}else w.kind===Qn?Nr.current=[q,Y]:w.kind===ha&&!w.isMaybeScrollbar&&(le(Yo,!1),o(void 0),Sn(),It?.(),kr.current=void 0,si.current=void 0)},[oe,ne,K,Un,U,At,_e,Rn,An,Rt,Sn,De,nn,Ai,Tt,ai,xr,jn,$r,hr,ct,wr,et,le,It]),zi=f.useRef(!1),Nr=f.useRef(),Pl=f.useRef(_n),ar=f.useRef(),th=f.useCallback(w=>{if(li.current=!1,Pl.current=an.current,w.button!==0&&w.button!==1){ar.current=void 0;return}const A=performance.now();ar.current={button:w.button,time:A,location:w.location},w?.kind==="header"&&(zi.current=!0);const _=w.kind==="cell"&&w.isFillHandle;!_&&w.kind!=="cell"&&w.isEdge||(u({previousSelection:K,fillHandle:_}),Nr.current=void 0,!w.isTouch&&w.button===0&&!_?Hi(w):!w.isTouch&&w.button===1&&(Nr.current=w.location))},[K,Hi]),[Va,$a]=f.useState(),Ll=f.useCallback(w=>{if(w.kind!==Qn||ne!=="multi")return;const A=fa.value?w.metaKey:w.ctrlKey,[_]=w.location,W=K.columns;if(_<U)return;const q=rt[_];let Y=_,V=_;for(let B=_-1;B>=U&&ho(q.group,rt[B].group);B--)Y--;for(let B=_+1;B<rt.length&&ho(q.group,rt[B].group);B++)V++;if(Sn(),A)if(W.hasAll([Y,V+1])){let B=W;for(let J=Y;J<=V;J++)B=B.remove(J);et(B,void 0,A)}else et(void 0,[Y,V+1],A);else et(Qe.fromSingleSelection([Y,V+1]),void 0,A)},[ne,Sn,K.columns,rt,U,et]),li=f.useRef(!1),Lo=f.useCallback(async w=>{if(z!==void 0&&ve!==void 0){const A=an.current.y,_=an.current.height;let W=z({x:w,y:A,width:1,height:Math.min(_,_e-A)},In.current.signal);typeof W!="object"&&(W=await W());const q=cn[w-U],V=document.createElement("canvas").getContext("2d",{alpha:!1});if(V!==null){V.font=Je.baseFontFull;const B=_d(V,Je,q,0,W,fr,ni,!1,nn);ve?.(q,B.width,w,B.width)}}},[cn,z,ni,Je,fr,ve,U,_e,nn]),[nh,Na]=f.useState(),ui=f.useCallback(async(w,A)=>{const _=w.current?.range;if(_===void 0||z===void 0||A.current===void 0)return;const W=A.current.range;if(R!==void 0){let B=!1;if(R({fillDestination:{...W,x:W.x-U},patternSource:{..._,x:_.x-U},preventDefault:()=>B=!0}),B)return}let q=z(_,In.current.signal);typeof q!="object"&&(q=await q());const Y=q,V=[];for(let B=0;B<W.width;B++)for(let J=0;J<W.height;J++){const he=[W.x+B,W.y+J];if(xd(he,_))continue;const pe=Y[J%_.height][B%_.width];yi(pe)||!bi(pe)||V.push({location:he,value:{...pe}})}qn(V),pn.current?.damage(V.map(B=>({cell:B.location})))},[z,qn,R,U]),Fl=f.useCallback(()=>{if(K.current===void 0||K.current.range.width<=1)return;const w={...K,current:{...K.current,range:{...K.current.range,width:1}}};ui(w,K)},[ui,K]),_l=f.useCallback(()=>{if(K.current===void 0||K.current.range.height<=1)return;const w={...K,current:{...K.current,range:{...K.current.range,height:1}}};ui(w,K)},[ui,K]),rh=f.useCallback((w,A)=>{const _=l;if(u(void 0),Fa(void 0),Na(void 0),zi.current=!1,A)return;if(_?.fillHandle===!0&&K.current!==void 0&&_.previousSelection?.current!==void 0){if(Vr===void 0)return;const pe={...K,current:{...K.current,range:$d(_.previousSelection.current.range,Vr)}};ui(_.previousSelection,pe),le(pe,!0);return}const[W,q]=w.location,[Y,V]=Nr.current??[],B=()=>{li.current=!0},J=pe=>{const kt=pe.isTouch||Y===W&&V===q;if(kt&&E?.([W-U,q],{...pe,preventDefault:B}),pe.button===1)return!li.current;if(!li.current){const tt=An(w.location),Ke=nn(tt);if(Ke!==void 0&&Ke.onClick!==void 0&&kt){const ut=Ke.onClick({...pe,cell:tt,posX:pe.localEventX,posY:pe.localEventY,bounds:pe.bounds,theme:Ai(tt,w.location),preventDefault:B});ut!==void 0&&!yi(ut)&&vi(ut)&&(qn([{location:pe.location,value:ut}]),pn.current?.damage([{cell:pe.location}]))}if(li.current||K.current===void 0)return!1;let Ze=!1;switch(tt.activationBehaviorOverride??Ee){case"double-click":case"second-click":{if(_?.previousSelection?.current?.cell===void 0)break;const[ut,vt]=K.current.cell,[bt,di]=_.previousSelection.current.cell;Ze=W===ut&&W===bt&&q===vt&&q===di&&(pe.isDoubleClick===!0||Ee==="second-click");break}case"single-click":{Ze=!0;break}}if(Ze){const vt={inputType:"pointer",pointerActivation:pe.isDoubleClick===!0?"double-click":tt.activationBehaviorOverride??Ee,pointerType:pe.isTouch?"touch":"mouse"};return I?.([W-U,q],vt),oi(pe.bounds,vt),!0}}return!1},he=w.location[0]-U;if(w.isTouch){const pe=an.current,kt=Pl.current;if(pe.x!==kt.x||pe.y!==kt.y)return;if(w.isLongTouch===!0){if(w.kind==="cell"&&so(K.current?.cell,w.location)){G?.([he,w.location[1]],{...w,preventDefault:B});return}else if(w.kind==="header"&&K.columns.hasIndex(W)){ue?.(he,{...w,preventDefault:B});return}else if(w.kind===Qn){if(he<0)return;ee?.(he,{...w,preventDefault:B});return}}w.kind==="cell"?J(w)||Hi(w):w.kind===Qn?$?.(he,{...w,preventDefault:B}):(w.kind===Tr&&M?.(he,{...w,preventDefault:B}),Hi(w));return}if(w.kind==="header"){if(he<0)return;w.isEdge?w.isDoubleClick===!0&&Lo(W):w.button===0&&W===Y&&q===V&&M?.(he,{...w,preventDefault:B})}if(w.kind===Qn){if(he<0)return;w.button===0&&W===Y&&q===V&&($?.(he,{...w,preventDefault:B}),li.current||Ll(w))}w.kind==="cell"&&(w.button===0||w.button===1)&&J(w),Nr.current=void 0},[l,K,U,Vr,ui,le,E,An,nn,Ee,Ai,qn,I,oi,G,ue,ee,Hi,$,M,Lo,Ll]),ih=f.useCallback(w=>{const A={...w,location:[w.location[0]-U,w.location[1]]};it?.(A),l!==void 0&&w.buttons===0&&(u(void 0),Fa(void 0),Na(void 0),zi.current=!1),Na(_=>zi.current?[w.scrollEdge[0],0]:w.scrollEdge[0]===_?.[0]&&w.scrollEdge[1]===_[1]?_:l===void 0||(ar.current?.location[0]??0)<U?void 0:w.scrollEdge)},[l,it,U]),oh=f.useCallback((w,A)=>{ft?.(w-U,A)},[ft,U]),ah=f.useCallback((w,A)=>{ot?.(w-U,A)},[ot,U]),ci=K?.current?.cell,sh=f.useCallback((w,A,_,W,q,Y)=>{Yn.current=!1;let V=ci;V!==void 0&&(V=[V[0]-U,V[1]]);const B=Te===0?void 0:{x:0,y:w.y,width:Te,height:w.height},J=[];B!==void 0&&J.push(B),Le>0&&(J.push({x:w.x-U,y:_e-Le,width:w.width,height:Le}),Te>0&&J.push({x:0,y:_e-Le,width:Te,height:Le}));const he={x:w.x-U,y:w.y,width:w.width,height:At&&w.y+w.height>=_e?w.height-1:w.height,tx:q,ty:Y,extras:{selected:V,freezeRegion:B,freezeRegions:J}};an.current=he,_i(he),Ut([A,_,W]),Zt?.(he,he.tx,he.ty,he.extras)},[ci,U,At,_e,Te,Le,_i,Zt]),lh=Er(T,f.useCallback((w,A)=>T?.(w-U,A-U)!==!1,[T,U])),uh=Er(xe,f.useCallback((w,A)=>{xe?.(w-U,A-U),ne!=="none"&&et(Qe.fromSingleSelection(A),void 0,!0)},[ne,xe,U,et])),Ba=f.useRef(!1),ch=f.useCallback(w=>{if(w.location[0]===0&&U>0){w.preventDefault();return}Pe?.({...w,location:[w.location[0]-U,w.location[1]]}),w.defaultPrevented()||(Ba.current=!0),u(void 0)},[Pe,U]),dh=f.useCallback(()=>{Ba.current=!1},[]),Al=wt?.selectionBehavior,Fo=f.useCallback(w=>{if(Al!=="block-spanning")return;const{isGroupHeader:A,path:_,groupRows:W}=$r(w);if(A)return[w,w];const q=_[_.length-1],Y=w-q,V=w+W-q-1;return[Y,V]},[$r,Al]),Wa=f.useRef(),Ua=f.useCallback(w=>{if(!Gd(w,Wa.current)&&(Wa.current=w,!(ar?.current?.button!==void 0&&ar.current.button>=1))){if(w.buttons!==0&&l!==void 0&&ar.current?.location[0]===0&&w.location[0]===0&&U===1&&oe==="multi"&&l.previousSelection&&!l.previousSelection.rows.hasIndex(ar.current.location[1])&&K.rows.hasIndex(ar.current.location[1])){const A=Math.min(ar.current.location[1],w.location[1]),_=Math.max(ar.current.location[1],w.location[1])+1;Tt(Qe.fromSingleSelection([A,_]),void 0,!1)}if(w.buttons!==0&&l!==void 0&&K.current!==void 0&&!Ba.current&&!zi.current&&(Ue==="rect"||Ue==="multi-rect")){const[A,_]=K.current.cell;let[W,q]=w.location;if(q<0&&(q=an.current.y),l.fillHandle===!0&&l.previousSelection?.current!==void 0){const Y=l.previousSelection.current.range;q=Math.min(q,At?_e-1:_e);const V=l0(Y,W,q,St);Fa(V)}else{if(At&&_===_e)return;if(At&&q===_e)if(w.kind===ha)q--;else return;W=Math.max(W,U);const B=Fo(_);q=B===void 0?q:Gn(q,B[0],B[1]);const J=W-A,he=q-_,pe={x:J>=0?A:W,y:he>=0?_:q,width:Math.abs(J)+1,height:Math.abs(he)+1};ct({...K.current,range:pe},!0,!1,"drag")}}Ft?.({...w,location:[w.location[0]-U,w.location[1]]})}},[l,U,oe,K,Ue,Ft,Tt,At,_e,St,Fo,ct]),fh=f.useCallback(()=>{const w=Wa.current;if(w===void 0)return;const[A,_]=w.scrollEdge;let[W,q]=w.location;const Y=an.current;A===-1?W=Y.extras?.freezeRegion?.x??Y.x:A===1&&(W=Y.x+Y.width),_===-1?q=Math.max(0,Y.y):_===1&&(q=Math.min(_e-1,Y.y+Y.height)),W=Gn(W,0,rt.length-1),q=Gn(q,0,_e-1),Ua({...w,location:[W,q]})},[rt.length,Ua,_e]);vv(nh,Vt,fh);const Kn=f.useCallback(w=>{if(K.current===void 0)return;const[A,_]=w,[W,q]=K.current.cell,Y=K.current.range;let V=Y.x,B=Y.x+Y.width,J=Y.y,he=Y.y+Y.height;const[pe,kt]=Fo(q)??[0,_e-1],tt=kt+1;if(_!==0)switch(_){case 2:{he=tt,J=q,qt(0,he,"vertical");break}case-2:{J=pe,he=q+1,qt(0,J,"vertical");break}case 1:{J<q?(J++,qt(0,J,"vertical")):(he=Math.min(tt,he+1),qt(0,he,"vertical"));break}case-1:{he>q+1?(he--,qt(0,he,"vertical")):(J=Math.max(pe,J-1),qt(0,J,"vertical"));break}default:co()}if(A!==0)if(A===2)B=rt.length,V=W,qt(B-1-U,0,"horizontal");else if(A===-2)V=U,B=W+1,qt(V-U,0,"horizontal");else{let Ke=[];if(z!==void 0){const Ze=z({x:V,y:J,width:B-V-U,height:he-J},In.current.signal);typeof Ze=="object"&&(Ke=Iv(Ze))}if(A===1){let Ze=!1;if(V<W){if(Ke.length>0){const ut=Or(V+1,W+1).find(vt=>!Ke.includes(vt-U));ut!==void 0&&(V=ut,Ze=!0)}else V++,Ze=!0;Ze&&qt(V,0,"horizontal")}Ze||(B=Math.min(rt.length,B+1),qt(B-1-U,0,"horizontal"))}else if(A===-1){let Ze=!1;if(B>W+1){if(Ke.length>0){const ut=Or(B-1,W,-1).find(vt=>!Ke.includes(vt-U));ut!==void 0&&(B=ut,Ze=!0)}else B--,Ze=!0;Ze&&qt(B-U,0,"horizontal")}Ze||(V=Math.max(U,V-1),qt(V-U,0,"horizontal"))}else co()}ct({cell:K.current.cell,range:{x:V,y:J,width:B-V,height:he-J}},!0,!1,"keyboard-select")},[z,Fo,K,rt.length,U,_e,qt,ct]),qa=f.useRef(Ii);qa.current=Ii;const Mr=f.useCallback((w,A,_,W)=>{const q=mn-(_?0:1);w=Gn(w,U,cn.length-1+U),A=Gn(A,0,q);const Y=ci?.[0],V=ci?.[1];if(w===Y&&A===V)return!1;if(W&&K.current!==void 0){const B=[...K.current.rangeStack];(K.current.range.width>1||K.current.range.height>1)&&B.push(K.current.range),le({...K,current:{cell:[w,A],range:{x:w,y:A,width:1,height:1},rangeStack:B}},!0)}else ct({cell:[w,A],range:{x:w,y:A,width:1,height:1}},!0,!1,"keyboard-nav");return c.current!==void 0&&c.current[0]===w&&c.current[1]===A&&(c.current=void 0),qa.current&&qt(w-U,A),!0},[mn,U,cn.length,ci,K,qt,le,ct]),hh=f.useCallback((w,A)=>{i?.cell!==void 0&&w!==void 0&&vi(w)&&(qn([{location:i.cell,value:w}]),window.requestAnimationFrame(()=>{pn.current?.damage([{cell:i.cell}])})),Sn(!0),o(void 0);const[_,W]=A;if(K.current!==void 0&&(_!==0||W!==0)){const q=K.current.cell[1]===mn-1&&w!==void 0,Y=K.current.cell[0]===rt.length-1&&w!==void 0;let V=!0;if(q&&W===1&&me!==void 0){V=!1;const B=K.current.cell[0]+_,J=ai(B);xr(J??B,!1)}if(Y&&_===1&&nt!==void 0){V=!1;const B=K.current.cell[1]+W;Po(B,!1)}V&&Mr(Gn(K.current.cell[0]+_,0,rt.length-1),Gn(K.current.cell[1]+W,0,mn-1),q,!1)}P?.(w,A)},[i?.cell,Sn,K,P,qn,mn,Mr,rt.length,xr,Po,me,nt,ai]),gh=f.useMemo(()=>`gdg-overlay-${Rv++}`,[]),Br=f.useCallback(w=>{Sn();const A=[];for(let _=w.x;_<w.x+w.width;_++)for(let W=w.y;W<w.y+w.height;W++){const q=C([_-U,W]);if(!q.allowOverlay&&q.kind!==Z.Boolean)continue;let Y;if(q.kind===Z.Custom){const V=nn(q),B=V?.provideEditor?.({...q,location:[_-U,W]});V?.onDelete!==void 0?Y=V.onDelete(q):np(B)&&(Y=B?.deletedValue?.(q))}else(vi(q)&&q.allowOverlay||q.kind===Z.Boolean)&&(Y=nn(q)?.onDelete?.(q));Y!==void 0&&!yi(Y)&&vi(Y)&&A.push({location:[_,W],value:Y})}qn(A),pn.current?.damage(A.map(_=>({cell:_.location})))},[Sn,C,nn,qn,U]),Vi=i!==void 0,Hl=f.useCallback(w=>{const A=()=>{w.stopPropagation(),w.preventDefault()},_={didMatch:!1},{bounds:W}=w,q=K.columns,Y=K.rows,V=or;if(!Vi&&at(V.clear,w,_))le(Yo,!1),It?.();else if(!Vi&&at(V.selectAll,w,_))le({columns:Qe.empty(),rows:Qe.empty(),current:{cell:K.current?.cell??[U,0],range:{x:U,y:0,width:b.length,height:_e},rangeStack:[]}},!1);else if(at(V.search,w,_))s?.current?.focus({preventScroll:!0}),Ro(!0);else if(at(V.delete,w,_)){const ut=Qt?.(K)??!0;if(ut!==!1){const vt=ut===!0?K:ut;if(vt.current!==void 0){Br(vt.current.range);for(const bt of vt.current.rangeStack)Br(bt)}for(const bt of vt.rows)Br({x:U,y:bt,width:b.length,height:1});for(const bt of vt.columns)Br({x:bt,y:0,width:1,height:_e})}}if(_.didMatch)return A(),!0;if(K.current===void 0)return!1;let[B,J]=K.current.cell;const[,he]=K.current.cell;let pe=!1,kt=!1;if(at(V.scrollToSelectedCell,w,_))Ho.current(B-U,J);else if(ne!=="none"&&at(V.selectColumn,w,_))q.hasIndex(B)?et(q.remove(B),void 0,!0):ne==="single"?et(Qe.fromSingleSelection(B),void 0,!0):et(void 0,B,!0);else if(oe!=="none"&&at(V.selectRow,w,_))Y.hasIndex(J)?Tt(Y.remove(J),void 0,!0):oe==="single"?Tt(Qe.fromSingleSelection(J),void 0,!0):Tt(void 0,J,!0);else if(!Vi&&W!==void 0&&at(V.activateCell,w,_))if(J===_e&&At)window.setTimeout(()=>{const ut=ai(B);xr(ut??B)},0);else{const ut={inputType:"keyboard",key:w.key};I?.([B-U,J],ut),oi(W,ut)}else K.current.range.height>1&&at(V.downFill,w,_)?_l():K.current.range.width>1&&at(V.rightFill,w,_)?Fl():at(V.goToNextPage,w,_)?J+=Math.max(1,an.current.height-4):at(V.goToPreviousPage,w,_)?J-=Math.max(1,an.current.height-4):at(V.goToFirstCell,w,_)?(o(void 0),J=0,B=0):at(V.goToLastCell,w,_)?(o(void 0),J=Number.MAX_SAFE_INTEGER,B=Number.MAX_SAFE_INTEGER):at(V.selectToFirstCell,w,_)?(o(void 0),Kn([-2,-2])):at(V.selectToLastCell,w,_)?(o(void 0),Kn([2,2])):Vi?(at(V.closeOverlay,w,_)&&o(void 0),at(V.acceptOverlayDown,w,_)&&(o(void 0),J++),at(V.acceptOverlayUp,w,_)&&(o(void 0),J--),at(V.acceptOverlayLeft,w,_)&&(o(void 0),B--),at(V.acceptOverlayRight,w,_)&&(o(void 0),B++)):(at(V.goDownCell,w,_)?J+=1:at(V.goUpCell,w,_)?J-=1:at(V.goRightCell,w,_)?B+=1:at(V.goLeftCell,w,_)?B-=1:at(V.goDownCellRetainSelection,w,_)?(J+=1,pe=!0):at(V.goUpCellRetainSelection,w,_)?(J-=1,pe=!0):at(V.goRightCellRetainSelection,w,_)?(B+=1,pe=!0):at(V.goLeftCellRetainSelection,w,_)?(B-=1,pe=!0):at(V.goToLastRow,w,_)?J=_e-1:at(V.goToFirstRow,w,_)?J=Number.MIN_SAFE_INTEGER:at(V.goToLastColumn,w,_)?B=Number.MAX_SAFE_INTEGER:at(V.goToFirstColumn,w,_)?B=Number.MIN_SAFE_INTEGER:(Ue==="rect"||Ue==="multi-rect")&&(at(V.selectGrowDown,w,_)?Kn([0,1]):at(V.selectGrowUp,w,_)?Kn([0,-1]):at(V.selectGrowRight,w,_)?Kn([1,0]):at(V.selectGrowLeft,w,_)?Kn([-1,0]):at(V.selectToLastRow,w,_)?Kn([0,2]):at(V.selectToFirstRow,w,_)?Kn([0,-2]):at(V.selectToLastColumn,w,_)?Kn([2,0]):at(V.selectToFirstColumn,w,_)&&Kn([-2,0])),kt=_.didMatch);if(jn!==void 0&&jn!=="normal"&&J!==he){const ut=jn==="skip-up"||jn==="skip"||jn==="block",vt=jn==="skip-down"||jn==="skip"||jn==="block",bt=J<he;if(bt&&ut){for(;J>=0&&$r(J).isGroupHeader;)J--;J<0&&(J=he)}else if(!bt&&vt){for(;J<_e&&$r(J).isGroupHeader;)J++;J>=_e&&(J=he)}}const Ke=Mr(B,J,!1,pe),Ze=_.didMatch;return Ze&&(Ke||!kt||yn)&&A(),Ze},[jn,Vi,K,or,ne,oe,Ue,U,$r,_e,Mr,le,It,b.length,Qt,yn,Br,et,Tt,At,ai,xr,I,oi,_l,Fl,Kn]),$i=f.useCallback(w=>{let A=!1;if(X!==void 0&&X({...w,...w.location&&{location:[w.location[0]-U,w.location[1]]},cancel:()=>{A=!0}}),A||Hl(w)||K.current===void 0)return;const[_,W]=K.current.cell,q=an.current;if(Se&&!w.metaKey&&!w.ctrlKey&&K.current!==void 0&&w.key.length===1&&/[\p{L}\p{M}\p{N}\p{S}\p{P}]/u.test(w.key)&&w.bounds!==void 0&&bi(C([_-U,Math.max(0,Math.min(W,_e-1))]))){if((!At||W!==_e)&&(q.y>W||W>q.y+q.height||q.x>_||_>q.x+q.width))return;const Y={inputType:"keyboard",key:w.key};I?.([_-U,W],Y),oi(w.bounds,Y,w.key),w.stopPropagation(),w.preventDefault()}},[Se,X,Hl,K,C,U,_e,At,I,oi]),ph=f.useCallback((w,A)=>{const _=w.location[0]-U;if(w.kind==="header"&&ue?.(_,{...w,preventDefault:A}),w.kind===Qn){if(_<0)return;ee?.(_,{...w,preventDefault:A})}if(w.kind==="cell"){const[W,q]=w.location;G?.([_,q],{...w,preventDefault:A}),im(K,w.location)||Mr(W,q,!1,!1)}},[K,G,ee,ue,U,Mr]),Ga=f.useCallback(async w=>{if(!or.paste)return;function A(V,B,J,he){const pe=typeof J=="object"?J?.join(`
`)??"":J?.toString()??"";if(!yi(V)&&bi(V)&&V.readonly!==!0){const kt=x?.(pe,V);if(kt!==void 0&&vi(kt))return{location:B,value:kt};const tt=nn(V);if(tt===void 0)return;if(tt.kind===Z.Custom){zn(V.kind===Z.Custom);const Ke=tt.onPaste?.(pe,V.data);return Ke===void 0?void 0:{location:B,value:{...V,data:Ke}}}else{const Ke=tt.onPaste?.(pe,V,{formatted:he,formattedString:typeof he=="string"?he:he?.join(`
`),rawValue:J});return Ke===void 0?void 0:(zn(Ke.kind===V.kind),{location:B,value:Ke})}}}const _=K.columns,W=K.rows,q=Vt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0;let Y;if(K.current!==void 0?Y=[K.current.range.x,K.current.range.y]:_.length===1?Y=[_.first()??0,0]:W.length===1&&(Y=[U,W.first()??0]),q&&Y!==void 0){let V,B;const J="text/plain",he="text/html";if(navigator.clipboard.read!==void 0){const Ke=await navigator.clipboard.read();for(const Ze of Ke){if(Ze.types.includes(he)){const vt=await(await Ze.getType(he)).text(),bt=Bu(vt);if(bt!==void 0){V=bt;break}}Ze.types.includes(J)&&(B=await(await Ze.getType(J)).text())}}else if(navigator.clipboard.readText!==void 0)B=await navigator.clipboard.readText();else if(w!==void 0&&w?.clipboardData!==null){if(w.clipboardData.types.includes(he)){const Ke=w.clipboardData.getData(he);V=Bu(Ke)}V===void 0&&w.clipboardData.types.includes(J)&&(B=w.clipboardData.getData(J))}else return;const[pe,kt]=Y,tt=[];do{if(Me===void 0){const Ke=An(Y),Ze=B??V?.map(vt=>vt.map(bt=>bt.rawValue).join("	")).join("	")??"",ut=A(Ke,Y,Ze,void 0);ut!==void 0&&tt.push(ut);break}if(V===void 0){if(B===void 0)return;V=cv(B)}if(Me===!1||typeof Me=="function"&&Me?.([Y[0]-U,Y[1]],V.map(Ke=>Ke.map(Ze=>Ze.rawValue?.toString()??"")))!==!0)return;for(const[Ke,Ze]of V.entries()){if(Ke+kt>=_e)break;for(const[ut,vt]of Ze.entries()){const bt=[ut+pe,Ke+kt],[di,Bi]=bt;if(di>=rt.length||Bi>=mn)continue;const Wi=An(bt),sr=A(Wi,bt,vt.rawValue,vt.formatted);sr!==void 0&&tt.push(sr)}}}while(!1);qn(tt),pn.current?.damage(tt.map(Ke=>({cell:Ke.location})))}},[x,nn,An,K,or.paste,Vt,rt.length,qn,mn,Me,U,_e]);xn("paste",Ga,d,!1,!0);const Ni=f.useCallback(async(w,A)=>{if(!or.copy)return;const _=A===!0||Vt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0,W=K.columns,q=K.rows,Y=(V,B)=>{if(!qe)Uu(V,B,w);else{const J=B.map(he=>({kind:Z.Text,data:b[he].title,displayData:b[he].title,allowOverlay:!1}));Uu([J,...V],B,w)}};if(_&&z!==void 0){if(K.current!==void 0){let V=z(K.current.range,In.current.signal);typeof V!="object"&&(V=await V()),Y(V,Or(K.current.range.x-U,K.current.range.x+K.current.range.width-U))}else if(q!==void 0&&q.length>0){const B=[...q].map(J=>{const he=z({x:U,y:J,width:b.length,height:1},In.current.signal);return typeof he=="object"?he[0]:he().then(pe=>pe[0])});if(B.some(J=>J instanceof Promise)){const J=await Promise.all(B);Y(J,Or(b.length))}else Y(B,Or(b.length))}else if(W.length>0){const V=[],B=[];for(const J of W){let he=z({x:J,y:0,width:1,height:_e},In.current.signal);typeof he!="object"&&(he=await he()),V.push(he),B.push(J-U)}if(V.length===1)Y(V[0],B);else{const J=V.reduce((he,pe)=>he.map((kt,tt)=>[...kt,...pe[tt]]));Y(J,B)}}}},[b,z,K,or.copy,U,Vt,_e,qe]);xn("copy",Ni,d,!1,!1);const mh=f.useCallback(async w=>{if(!(!or.cut||!(Vt.current?.contains(document.activeElement)===!0||a.current?.contains(document.activeElement)===!0))&&(await Ni(w),K.current!==void 0)){let _={current:{cell:K.current.cell,range:K.current.range,rangeStack:[]},rows:Qe.empty(),columns:Qe.empty()};const W=Qt?.(_);if(W===!1||(_=W===!0?_:W,_.current===void 0))return;Br(_.current.range)}},[Br,K,or.cut,Ni,Vt,Qt]);xn("cut",mh,d,!1,!1);const vh=f.useCallback((w,A)=>{if(de!==void 0){U!==0&&(w=w.map(q=>[q[0]-U,q[1]])),de(w,A);return}if(w.length===0||A===-1)return;const[_,W]=w[A];c.current!==void 0&&c.current[0]===_&&c.current[1]===W||(c.current=[_,W],Mr(_,W,!1,!1))},[de,U,Mr]),[_o,Ao]=fn?.current?.cell??[],Ho=f.useRef(qt);Ho.current=qt,f.useLayoutEffect(()=>{qa.current&&!Yn.current&&_o!==void 0&&Ao!==void 0&&(_o!==Re.current?.current?.cell[0]||Ao!==Re.current?.current?.cell[1])&&Ho.current(_o,Ao),Yn.current=!1},[_o,Ao]);const zl=K.current!==void 0&&(K.current.cell[0]>=rt.length||K.current.cell[1]>=mn);f.useLayoutEffect(()=>{zl&&le(Yo,!1)},[zl,le]);const bh=f.useMemo(()=>At===!0&&Bt?.tint===!0?Qe.fromSingleSelection(mn-1):Qe.empty(),[mn,At,Bt?.tint]),wh=f.useCallback(w=>typeof Gt=="boolean"?Gt:Gt?.(w-U)??!0,[U,Gt]),yh=f.useMemo(()=>{if(Va===void 0||a.current===null)return null;const{bounds:w,group:A}=Va,_=a.current.getBoundingClientRect();return f.createElement(Q0,{bounds:w,group:A,canvasBounds:_,onClose:()=>$a(void 0),onFinish:W=>{$a(void 0),ce?.(A,W)}})},[ce,Va]),Ch=Math.min(rt.length,Te+(Un?1:0));f.useImperativeHandle(t,()=>({appendRow:(w,A)=>xr(w+U,A),appendColumn:(w,A)=>Po(w,A),updateCells:w=>(U!==0&&(w=w.map(A=>({cell:[A.cell[0]+U,A.cell[1]]}))),pn.current?.damage(w)),getBounds:(w,A)=>{if(!(a?.current===null||Vt?.current===null)){if(w===void 0&&A===void 0){const _=a.current.getBoundingClientRect(),W=_.width/Vt.current.clientWidth;return{x:_.x-Vt.current.scrollLeft*W,y:_.y-Vt.current.scrollTop*W,width:Vt.current.scrollWidth*W,height:Vt.current.scrollHeight*W}}return pn.current?.getBounds((w??0)+U,A)}},focus:()=>pn.current?.focus(),emit:async w=>{switch(w){case"delete":$i({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":$i({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":$i({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await Ni(void 0,!0);break;case"paste":await Ga();break}},scrollTo:qt,remeasureColumns:w=>{for(const A of w)Lo(A+U)},getMouseArgsForPosition:(w,A,_)=>{if(pn?.current===null)return;const W=pn.current.getMouseArgsForPosition(w,A,_);if(W!==void 0)return{...W,location:[W.location[0]-U,W.location[1]]}}}),[xr,Po,Lo,Vt,Ni,$i,Ga,U,qt]);const[Vl,$l]=ci??[],Sh=f.useCallback(w=>{const[A,_]=w;if(_===-1){ne!=="none"&&(et(Qe.fromSingleSelection(A),void 0,!1),Sn());return}Vl===A&&$l===_||(ct({cell:w,range:{x:A,y:_,width:1,height:1}},!0,!1,"keyboard-nav"),qt(A,_))},[ne,Sn,qt,Vl,$l,ct,et]),[xh,kh]=f.useState(!1),Nl=f.useRef(cd(w=>{kh(w)},5)),Mh=f.useCallback(()=>{Nl.current(!0),K.current===void 0&&K.columns.length===0&&K.rows.length===0&&l===void 0&&ct({cell:[U,Io],range:{x:U,y:Io,width:1,height:1}},!0,!1,"keyboard-select")},[Io,K,l,U,ct]),Rh=f.useCallback(()=>{Nl.current(!1)},[]),[Ih,Eh]=f.useMemo(()=>{let w;const A=xt?.scrollbarWidthOverride??Ts(),_=_e+(At?1:0);if(typeof ir=="number")w=En+_*ir;else{let q=0;const Y=Math.min(_,10);for(let V=0;V<Y;V++)q+=ir(V);q=Math.floor(q/Y),w=En+_*q}w+=A;const W=rt.reduce((q,Y)=>Y.width+q,0)+A;return[`${Math.min(1e5,W)}px`,`${Math.min(1e5,w)}px`]},[rt,xt?.scrollbarWidthOverride,ir,_e,At,En]),Th=f.useMemo(()=>bm(Je),[Je]);return f.createElement(Ld.Provider,{value:Je},f.createElement(gv,{style:Th,className:j,inWidth:m??Ih,inHeight:y??Eh},f.createElement(j0,{fillHandle:$n,drawFocusRing:Ei,experimental:xt,fixedShadowX:Et,fixedShadowY:kn,getRowThemeOverride:yr,headerIcons:cr,imageWindowLoader:Nn,initialSize:N,isDraggable:We,onDragLeave:Ge,onRowMoved:Rt,overscrollX:Pi,overscrollY:Oa,preventDiagonalScrolling:Mn,rightElement:wn,rightElementProps:Wt,smoothScrollX:Pn,smoothScrollY:Ce,className:j,enableGroups:Lt,onCanvasFocused:Mh,onCanvasBlur:Rh,canvasRef:a,onContextMenu:ph,theme:Je,cellXOffset:La,cellYOffset:Io,accessibilityHeight:_n.height,onDragEnd:dh,columns:rt,nonGrowWidth:Sr,drawHeader:je,onColumnProposeMove:lh,drawCell:ht,disabledRows:bh,freezeColumns:Ch,lockColumns:U,firstColAccessible:U===0,getCellContent:An,minColumnWidth:fr,maxColumnWidth:ni,searchInputRef:s,showSearch:ri,onSearchClose:Pa,highlightRegions:eh,getCellsForSelection:z,getGroupDetails:_a,headerHeight:Oi,isFocused:xh,groupHeaderHeight:Lt?ko:0,freezeTrailingRows:Le+(At&&Bt?.sticky===!0?1:0),hasAppendRow:At,onColumnResize:ve,onColumnResizeEnd:Ye,onColumnResizeStart:Ie,onCellFocused:Sh,onColumnMoved:uh,onDragStart:ch,onHeaderMenuClick:oh,onHeaderIndicatorClick:ah,onItemHovered:Ua,isFilling:l?.fillHandle===!0,onMouseMove:ih,onKeyDown:$i,onKeyUp:te,onMouseDown:th,onMouseUp:rh,onDragOverCell:hn,onDrop:On,onSearchResultsChanged:vh,onVisibleRegionChanged:sh,clientSize:zt,rowHeight:ir,searchResults:re,searchValue:L,onSearchValueChange:H,rows:mn,scrollRef:Vt,selection:K,translateX:_n.tx,translateY:_n.ty,verticalBorder:wh,gridRef:pn,getCellRenderer:nn,resizeIndicator:Bn}),yh,i!==void 0&&f.createElement(f.Suspense,{fallback:null},f.createElement(Mv,{...i,validateCell:ke,bloom:D,id:gh,getCellRenderer:nn,className:xt?.isSubGrid===!0?"click-outside-ignore":void 0,provideEditor:en,imageEditorOverride:g,portalElementRef:Il,onFinishEditing:hh,markdownDivCreateNode:p,isOutsideClick:jt,customEventTarget:xt?.eventTarget}))))},Tv=f.forwardRef(Ev);function Xu(e){const{cell:t,posX:n,posY:r,bounds:i,theme:o}=e,{width:s,height:a,x:l,y:u}=i,c=t.maxSize??o.checkboxMaxSize,d=Math.floor(i.y+a/2),g=hd(c,a,o.cellVerticalPadding),h=fd(t.contentAlign??"center",l,s,o.cellHorizontalPadding,g),p=dd(h,d,g),m=gd(l+n,u+r,p);return Xs(t)&&m}const Dv={getAccessibilityString:e=>e.data?.toString()??"false",kind:Z.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>Ov(e,e.cell.data,Xs(e.cell),e.cell.maxSize??e.theme.checkboxMaxSize,e.cell.hoverEffectIntensity??.35),onDelete:e=>({...e,data:!1}),onSelect:e=>{Xu(e)&&e.preventDefault()},onClick:e=>{if(Xu(e))return{...e.cell,data:Yd(e.cell.data)}},onPaste:(e,t)=>{let n=sa;return e.toLowerCase()==="true"?n=!0:e.toLowerCase()==="false"?n=!1:e.toLowerCase()==="indeterminate"&&(n=Gs),n===t.data?void 0:{...t,data:n}}};function Ov(e,t,n,r,i){if(!n&&t===sa)return;const{ctx:o,hoverAmount:s,theme:a,rect:l,highlighted:u,hoverX:c,hoverY:d,cell:{contentAlign:g}}=e,{x:h,y:p,width:m,height:y}=l;let b=!1;if(i>0){let v=n?1-i+i*s:.4;if(t===sa&&(v*=s),v===0)return;v<1&&(b=!0,o.globalAlpha=v)}il(o,a,t,h,p,m,y,u,c,d,r,g),b&&(o.globalAlpha=1)}const Pv=sn("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),Lv=e=>{const{bubbles:t}=e;return f.createElement(Pv,null,t.map((n,r)=>f.createElement("div",{key:r,className:"boe-bubble"},n)),f.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},Fv={getAccessibilityString:e=>pd(e.data),kind:Z.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>{const r=t.data.reduce((i,o)=>e.measureText(o).width+i+n.bubblePadding*2+n.bubbleMargin,0);return t.data.length===0?n.cellHorizontalPadding*2:r+2*n.cellHorizontalPadding-n.bubbleMargin},draw:e=>_v(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return f.createElement(Lv,{bubbles:t.data})},onPaste:()=>{}};function _v(e,t){const{rect:n,theme:r,ctx:i,highlighted:o}=e,{x:s,y:a,width:l,height:u}=n;let c=s+r.cellHorizontalPadding;const d=[];for(const g of t){if(c>s+l)break;const h=Fr(g,i,r.baseFontFull).width;d.push({x:c,width:h}),c+=h+r.bubblePadding*2+r.bubbleMargin}i.beginPath();for(const g of d)tr(i,g.x,a+(u-r.bubbleHeight)/2,g.width+r.bubblePadding*2,r.bubbleHeight,r.roundingRadius??r.bubbleHeight/2);i.fillStyle=o?r.bgBubbleSelected:r.bgBubble,i.fill();for(const[g,h]of d.entries())i.beginPath(),i.fillStyle=r.textBubble,i.fillText(t[g],h.x+r.bubblePadding,a+u/2+rr(i,r))}const Av=sn("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),Hv=e=>{const{drilldowns:t}=e;return f.createElement(Av,null,t.map((n,r)=>f.createElement("div",{key:r,className:"doe-bubble"},n.img!==void 0&&f.createElement("img",{src:n.img}),f.createElement("div",null,n.text))))},zv={getAccessibilityString:e=>pd(e.data.map(t=>t.text)),kind:Z.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i.text).width+r+n.bubblePadding*2+n.bubbleMargin+(i.img!==void 0?18:0),0)+2*n.cellHorizontalPadding-4,draw:e=>$v(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return f.createElement(Hv,{drilldowns:t.data})},onPaste:()=>{}},hs={};function Vv(e,t,n,r){const i=Math.ceil(window.devicePixelRatio),o=5,s=n-o*2,a=4,l=n*i,u=r+o,c=r*3,d=(c+o*2)*i,g=`${e},${t},${i},${n}`;if(hs[g]!==void 0)return{el:hs[g],height:l,width:d,middleWidth:a*i,sideWidth:u*i,padding:o*i,dpr:i};const h=document.createElement("canvas"),p=h.getContext("2d");return p===null?null:(h.width=d,h.height=l,p.scale(i,i),hs[g]=h,p.beginPath(),tr(p,o,o,c,s,r),p.shadowColor="rgba(24, 25, 34, 0.4)",p.shadowBlur=1,p.fillStyle=e,p.fill(),p.shadowColor="rgba(24, 25, 34, 0.3)",p.shadowOffsetY=1,p.shadowBlur=5,p.fillStyle=e,p.fill(),p.shadowOffsetY=0,p.shadowBlur=0,p.shadowBlur=0,p.beginPath(),tr(p,o+.5,o+.5,c,s,r),p.strokeStyle=t,p.lineWidth=1,p.stroke(),{el:h,height:l,width:d,sideWidth:u*i,middleWidth:r*i,padding:o*i,dpr:i})}function $v(e,t){const{rect:n,theme:r,ctx:i,imageLoader:o,col:s,row:a}=e,{x:l,width:u}=n,c=r.baseFontFull,d=el(i,c),g=Math.min(n.height,Math.max(16,Math.ceil(d*r.lineHeight)*2)),h=Math.floor(n.y+(n.height-g)/2),p=g-10,m=r.bubblePadding,y=r.bubbleMargin;let b=l+r.cellHorizontalPadding;const v=r.roundingRadius??6,C=Vv(r.bgCell,r.drilldownBorder,g,v),E=[];for(const I of t){if(b>l+u)break;const P=Fr(I.text,i,c).width;let x=0;I.img!==void 0&&o.loadOrGetImage(I.img,s,a)!==void 0&&(x=p-8+4);const S=P+x+m*2;E.push({x:b,width:S}),b+=S+y}if(C!==null){const{el:I,height:R,middleWidth:P,sideWidth:x,width:S,dpr:F,padding:D}=C,M=x/F,T=D/F;for(const O of E){const k=Math.floor(O.x),$=Math.floor(O.width),G=$-(M-T)*2;i.imageSmoothingEnabled=!1,i.drawImage(I,0,0,x,R,k-T,h,M,g),G>0&&i.drawImage(I,x,0,P,R,k+(M-T),h,G,g),i.drawImage(I,S-x,0,x,R,k+$-(M-T),h,M,g),i.imageSmoothingEnabled=!0}}i.beginPath();for(const[I,R]of E.entries()){const P=t[I];let x=R.x+m;if(P.img!==void 0){const S=o.loadOrGetImage(P.img,s,a);if(S!==void 0){const F=p-8;let D=0,M=0,T=S.width,O=S.height;T>O?(D+=(T-O)/2,T=O):O>T&&(M+=(O-T)/2,O=T),i.beginPath(),tr(i,x,h+g/2-F/2,F,F,r.roundingRadius??3),i.save(),i.clip(),i.drawImage(S,D,M,T,O,x,h+g/2-F/2,F,F),i.restore(),x+=F+4}}i.beginPath(),i.fillStyle=r.textBubble,i.fillText(P.text,x,h+g/2+rr(i,r))}}const Nv={getAccessibilityString:e=>e.data.join(", "),kind:Z.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>Bv(e,e.cell.displayData??e.cell.data,e.cell.rounding??e.theme.roundingRadius??4,e.cell.contentAlign),measure:(e,t)=>t.data.length*50,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=r??Ep;return f.createElement(i,{urls:t.data,canWrite:t.readonly!==!0,onCancel:n,onChange:o=>{n({...t,data:[o]})}})},onPaste:(e,t)=>{e=e.trim();const r=e.split(",").map(i=>{try{return new URL(i),i}catch{return}}).filter(i=>i!==void 0);if(!(r.length===t.data.length&&r.every((i,o)=>i===t.data[o])))return{...t,data:r}}},gs=4;function Bv(e,t,n,r){const{rect:i,col:o,row:s,theme:a,ctx:l,imageLoader:u}=e,{x:c,y:d,height:g,width:h}=i,p=g-a.cellVerticalPadding*2,m=[];let y=0;for(let v=0;v<t.length;v++){const C=t[v];if(C.length===0)continue;const E=u.loadOrGetImage(C,o,s);if(E!==void 0){m[v]=E;const I=E.width*(p/E.height);y+=I+gs}}if(y===0)return;y-=gs;let b=c+a.cellHorizontalPadding;r==="right"?b=Math.floor(c+h-a.cellHorizontalPadding-y):r==="center"&&(b=Math.floor(c+h/2-y/2));for(const v of m){if(v===void 0)continue;const C=v.width*(p/v.height);n>0&&(l.beginPath(),tr(l,b,d+a.cellVerticalPadding,C,p,n),l.save(),l.clip()),l.drawImage(v,b,d+a.cellVerticalPadding,C,p),n>0&&l.restore(),b+=C+gs}}function Wv(e,t){let n=e*49632+t*325176;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}const Uv={getAccessibilityString:()=>"",kind:Z.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{const{cell:t,col:n,row:r,ctx:i,rect:o,theme:s}=e;if(t.skeletonWidth===void 0||t.skeletonWidth===0)return;let a=t.skeletonWidth;t.skeletonWidthVariability!==void 0&&t.skeletonWidthVariability>0&&(a+=Math.round(Wv(n,r)*t.skeletonWidthVariability));const l=s.cellHorizontalPadding;a+l*2>=o.width&&(a=o.width-l*2-1);const u=t.skeletonHeight??Math.min(18,o.height-2*s.cellVerticalPadding);tr(i,o.x+l,o.y+(o.height-u)/2,a,u,s.roundingRadius??3),i.fillStyle=jr(s.textDark,.1),i.fill()},onPaste:()=>{}},qv=()=>e=>e.targetWidth,Yu=sn("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[qv(),"px"]}}),Gv=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:o,onFinish:s,validatedSelection:a}=e,l=t.data,u=t.readonly===!0,[c,d]=f.useState(l===""||r),g=f.useCallback(()=>{d(p=>!p)},[]),h=l?"gdg-ml-6":"";return c?f.createElement(Yu,{targetWidth:o.width-20},f.createElement(Jr,{autoFocus:!0,highlight:!1,validatedSelection:a,value:l,onKeyDown:p=>{p.key==="Enter"&&p.stopPropagation()},onChange:n}),f.createElement("div",{className:`gdg-edit-icon gdg-checkmark-hover ${h}`,onClick:()=>s(t)},f.createElement(Cp,null))):f.createElement(Yu,{targetWidth:o.width},f.createElement(Xp,{contents:l,createNode:i}),!u&&f.createElement(f.Fragment,null,f.createElement("div",{className:"spacer"}),f.createElement("div",{className:`gdg-edit-icon gdg-edit-hover ${h}`,onClick:g},f.createElement(js,null))),f.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},Xv={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:yo,measure:(e,t,n)=>{const r=t.data.split(`
`)[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>lr(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:o,forceEditMode:s,validatedSelection:a}=e;return f.createElement(Gv,{onFinish:i,targetRect:r,value:n,validatedSelection:a,onChange:l=>t({...n,data:l.target.value}),forceEditMode:s,createNode:o})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},Yv={getAccessibilityString:e=>e.row.toString(),kind:Xn.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:jv,measure:()=>44,draw:e=>Zv(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:i}=e,{width:o,height:s}=t,a=n.drawHandle?7+(o-7)/2:o/2,l=s/2;if(Math.abs(r-a)<=10&&Math.abs(i-l)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function jv(e,t){const{ctx:n,theme:r}=e,i=r.markerFontFull,o=t??{};return o?.font!==i&&(n.font=i,o.font=i),o.deprep=Kv,n.textAlign="center",o}function Kv(e){const{ctx:t}=e;t.textAlign="start"}function Zv(e,t,n,r,i,o){const{ctx:s,rect:a,hoverAmount:l,theme:u}=e,{x:c,y:d,width:g,height:h}=a,p=n?1:r==="checkbox-visible"?.6+.4*l:l;if(r!=="number"&&p>0){s.globalAlpha=p;const m=7*(n?l:1);if(il(s,u,n,i?c+m:c,d,i?g-m:g,h,!0,void 0,void 0,u.checkboxMaxSize,"center",o),i){s.globalAlpha=l,s.beginPath();for(const y of[3,6])for(const b of[-5,-1,3])s.rect(c+y,d+h/2+b,2,2);s.fillStyle=u.textLight,s.fill(),s.beginPath()}s.globalAlpha=1}if(r==="number"||r==="both"&&!n){const m=t.toString(),y=u.markerFontFull,b=c+g/2;r==="both"&&l!==0&&(s.globalAlpha=1-l),s.fillStyle=u.textLight,s.font=y,s.fillText(m,b,d+h/2+rr(s,y)),l!==0&&(s.globalAlpha=1)}}const Jv={getAccessibilityString:()=>"",kind:Xn.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>Qv(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function Qv(e,t,n){const{ctx:r,rect:i,hoverAmount:o,theme:s,spriteManager:a}=e,{x:l,y:u,width:c,height:d}=i;r.beginPath(),r.globalAlpha=o,r.rect(l+1,u+1,c,d-2),r.fillStyle=s.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const g=t!=="";let h=0;if(n!==void 0){const m=d-8,y=l+8/2,b=u+8/2;a.drawSprite(n,"normal",r,y,b,m,s,g?1:o),h=m}else{h=24;const p=12,m=g?p:o*p,y=g?0:(1-o)*p*.5,b=s.cellHorizontalPadding+4;m>0&&(r.moveTo(l+b+y,u+d/2),r.lineTo(l+b+y+m,u+d/2),r.moveTo(l+b+y+m*.5,u+d/2-m*.5),r.lineTo(l+b+y+m*.5,u+d/2+m*.5),r.lineWidth=2,r.strokeStyle=s.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=s.textMedium,r.fillText(t,h+l+s.cellHorizontalPadding+.5,u+d/2+rr(r,s)),r.beginPath()}function Zd(e,t,n,r,i,o,s){e.textBaseline="alphabetic";const a=eb(e,i,r,t,n?.fullSize??!1);e.beginPath(),tr(e,a.x,a.y,a.width,a.height,t.roundingRadius??4),e.globalAlpha=o,e.fillStyle=n?.bgColor??jr(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",s?.("text")}function eb(e,t,n,r,i){const o=r.cellHorizontalPadding,s=r.cellVerticalPadding;if(i)return{x:t.x+o/2,y:t.y+s/2+1,width:t.width-o,height:t.height-s-1};const a=Fr(n,e,r.baseFontFull,"alphabetic"),l=t.height-s,u=Math.min(l,a.actualBoundingBoxAscent*2.5);return{x:t.x+o/2,y:t.y+(t.height-u)/2+1,width:a.width+o*3,height:u-1}}const tb=f.lazy(async()=>await Us(()=>import("./number-overlay-editor.DaRFzZEO.js"),__vite__mapDeps([13,1,2,3,4,5,6,7,8,9,10,11,12]),import.meta.url)),nb={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Number,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,useLabel:!0,drawPrep:yo,draw:e=>{const{hoverAmount:t,cell:n,ctx:r,theme:i,rect:o,overrideCursor:s}=e,{hoverEffect:a,displayData:l,hoverEffectTheme:u}=n;a===!0&&t>0&&Zd(r,i,u,l,o,t,s),lr(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return f.createElement(f.Suspense,{fallback:null},f.createElement(tb,{highlight:t,disabled:r.readonly===!0,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:o=>n({...r,data:Number.isNaN(o.floatValue??0)?0:o.floatValue})}))},onPaste:(e,t,n)=>{const r=typeof n.rawValue=="number"?n.rawValue:Number.parseFloat(typeof n.rawValue=="string"?n.rawValue:e);if(!(Number.isNaN(r)||t.data===r))return{...t,data:r,displayData:n.formattedString??t.displayData}}},rb={getAccessibilityString:()=>"",measure:()=>108,kind:Z.Protected,needsHover:!1,needsHoverPosition:!1,draw:ib,onPaste:()=>{}};function ib(e){const{ctx:t,theme:n,rect:r}=e,{x:i,y:o,height:s}=r;t.beginPath();const a=2.5;let l=i+n.cellHorizontalPadding+a;const u=o+s/2,c=Math.cos(gu(30))*a,d=Math.sin(gu(30))*a;for(let g=0;g<12;g++)t.moveTo(l,u-a),t.lineTo(l,u+a),t.moveTo(l+c,u-d),t.lineTo(l-c,u+d),t.moveTo(l-c,u-d),t.lineTo(l+c,u+d),l+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()}const ob={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>yo(e,t,e.theme.textLight),draw:e=>lr(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+n.cellHorizontalPadding*2,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return $t.createElement(Jr,{highlight:t,autoFocus:r.readonly!==!0,disabled:r.readonly!==!1,value:r.data,validatedSelection:i,onChange:o=>n({...r,data:o.target.value})})},onPaste:()=>{}},ab={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Text,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,drawPrep:yo,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:r,ctx:i,rect:o,theme:s,overrideCursor:a}=e,{displayData:l,contentAlign:u,hoverEffect:c,allowWrapping:d,hoverEffectTheme:g}=t;c===!0&&n>0&&Zd(i,s,g,l,o,n,a),lr(e,l,u,d,r)},measure:(e,t,n)=>{const r=t.displayData.split(`
`,t.allowWrapping===!0?void 0:1);let i=0;for(const o of r)i=Math.max(i,e.measureText(o).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:e.allowWrapping===!0,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:o}=t;return f.createElement(Jr,{style:e.allowWrapping===!0?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:i.readonly!==!0,disabled:i.readonly===!0,altNewline:!0,value:i.data,validatedSelection:o,onChange:s=>r({...i,data:s.target.value})})}}),onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},sb=sn("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),lb=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:o,preview:s}=e,[a,l]=f.useState(!i&&(t===""||r)),u=f.useCallback(()=>{l(!0)},[]);return a?f.createElement(Jr,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):f.createElement(sb,null,f.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},s),!i&&f.createElement("div",{className:"gdg-edit-icon",onClick:u},f.createElement(js,null)),f.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function Jd(e,t,n,r){let i=n.cellHorizontalPadding;const o=t.height/2-e.actualBoundingBoxAscent/2,s=e.width,a=e.actualBoundingBoxAscent;return r==="right"?i=t.width-s-n.cellHorizontalPadding:r==="center"&&(i=t.width/2-s/2),{x:i,y:o,width:s,height:a}}function ju(e){const{cell:t,bounds:n,posX:r,posY:i,theme:o}=e,s=t.displayData??t.data;if(t.hoverEffect!==!0||t.onClickUri===void 0)return!1;const a=Id(s,o.baseFontFull);if(a===void 0)return!1;const l=Jd(a,n,o,t.contentAlign);return Yr({x:l.x-4,y:l.y-4,width:l.width+8,height:l.height+8},r,i)}const ub={getAccessibilityString:e=>e.data?.toString()??"",kind:Z.Uri,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!0,useLabel:!0,drawPrep:yo,draw:e=>{const{cell:t,theme:n,overrideCursor:r,hoverX:i,hoverY:o,rect:s,ctx:a}=e,l=t.displayData??t.data,u=t.hoverEffect===!0;if(r!==void 0&&u&&i!==void 0&&o!==void 0){const c=Fr(l,a,n.baseFontFull),d=Jd(c,s,n,t.contentAlign),{x:g,y:h,width:p,height:m}=d;if(i>=g-4&&i<=g-4+p+8&&o>=h-4&&o<=h-4+m+8){const y=rr(a,n.baseFontFull);r("pointer");const b=5,v=h-y;a.beginPath(),a.moveTo(s.x+g,Math.floor(s.y+v+m+b)+.5),a.lineTo(s.x+g+p,Math.floor(s.y+v+m+b)+.5),a.strokeStyle=n.linkColor,a.stroke(),a.save(),a.fillStyle=e.cellFillColor,lr({...e,rect:{...s,x:s.x-1}},l,t.contentAlign),lr({...e,rect:{...s,x:s.x-2}},l,t.contentAlign),lr({...e,rect:{...s,x:s.x+1}},l,t.contentAlign),lr({...e,rect:{...s,x:s.x+2}},l,t.contentAlign),a.restore()}}a.fillStyle=u?n.linkColor:n.textDark,lr(e,l,t.contentAlign)},onSelect:e=>{ju(e)&&e.preventDefault()},onClick:e=>{const{cell:t}=e;ju(e)&&t.onClickUri?.(e)},measure:(e,t,n)=>e.measureText(t.displayData??t.data).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{const{onChange:n,value:r,forceEditMode:i,validatedSelection:o}=t;return f.createElement(lb,{forceEditMode:r.readonly!==!0&&(i||e.hoverEffect===!0&&e.onClickUri!==void 0),uri:r.data,preview:r.displayData??r.data,validatedSelection:o,readonly:r.readonly===!0,onChange:s=>n({...r,data:s.target.value})})},onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},cb=[Yv,Jv,Dv,Fv,zv,Nv,Uv,Xv,nb,rb,ob,ab,ub];var ps,Ku;function db(){if(Ku)return ps;Ku=1;var e=ud(),t=$c(),n="Expected a function";function r(i,o,s){var a=!0,l=!0;if(typeof i!="function")throw new TypeError(n);return t(s)&&(a="leading"in s?!!s.leading:a,l="trailing"in s?!!s.trailing:l),e(i,o,{leading:a,maxWait:o,trailing:l})}return ps=r,ps}var fb=db();const hb=vr(fb),ms=[];class gb extends Dd{imageLoaded=()=>{};loadedLocations=[];cache={};setCallback(t){this.imageLoaded=t}sendLoaded=hb(()=>{this.imageLoaded(new uo(this.loadedLocations)),this.loadedLocations=[]},20);clearOutOfWindow=()=>{const t=Object.keys(this.cache);for(const n of t){const r=this.cache[n];let i=!1;for(let o=0;o<r.cells.length;o++){const s=r.cells[o];if(this.isInWindow(s)){i=!0;break}}i?r.cells=r.cells.filter(this.isInWindow):(r.cancel(),delete this.cache[n])}};loadImage(t,n,r,i){let o=!1;const s=ms.pop()??new Image;let a=!1;const l={img:void 0,cells:[er(n,r)],url:t,cancel:()=>{a||(a=!0,ms.length<12?ms.unshift(s):o||(s.src=""))}},u=new Promise(c=>s.addEventListener("load",()=>c(null)));requestAnimationFrame(async()=>{try{s.src=t,await u,await s.decode();const c=this.cache[i];if(c!==void 0&&!a){c.img=s;for(const d of c.cells)this.loadedLocations.push(rl(d));o=!0,this.sendLoaded()}}catch{l.cancel()}}),this.cache[i]=l}loadOrGetImage(t,n,r){const i=t,o=this.cache[i];if(o!==void 0){const s=er(n,r);return o.cells.includes(s)||o.cells.push(s),o.img}else this.loadImage(t,n,r,i)}}const pb=(e,t)=>{const n=f.useMemo(()=>({...o0,...e.headerIcons}),[e.headerIcons]),r=f.useMemo(()=>e.renderers??cb,[e.renderers]),i=f.useMemo(()=>e.imageWindowLoader??new gb,[e.imageWindowLoader]);return f.createElement(Tv,{...e,renderers:r,headerIcons:n,ref:t,imageWindowLoader:i})},mb=f.forwardRef(pb);function Zu(e,t){const n=f.useRef(null),r=f.useRef(),i=f.useCallback(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]);return f.useEffect(()=>i,[i]),{debouncedCallback:f.useCallback((...s)=>{r.current=s,i(),n.current=setTimeout(()=>{r.current&&(e(...r.current),r.current=void 0)},t)},[e,t,i]),cancel:i}}var vb=Nh();const bb=vr(vb);var ra={exports:{}};/*! Moment Duration Format v2.2.2
 *  https://github.com/jsmreese/moment-duration-format
 *  Date: 2018-02-16
 *
 *  Duration format plugin function for the Moment.js library
 *  http://momentjs.com/
 *
 *  Copyright 2018 John Madhavan-Reese
 *  Released under the MIT license
 */var wb=ra.exports,Ju;function yb(){return Ju||(Ju=1,function(e,t){(function(n,r){try{e.exports=r(Bh)}catch{e.exports=r}n&&(n.momentDurationFormatSetup=n.moment?r(n.moment):r)})(wb,function(n){var r=!1,i=!1,o=!1,s=!1,a="escape years months weeks days hours minutes seconds milliseconds general".split(" "),l=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function u(H,L){return L.length>H.length?!1:H.indexOf(L)!==-1}function c(H){for(var L="";H;)L+="0",H-=1;return L}function d(H){for(var L=H.split("").reverse(),X=0,te=!0;te&&X<L.length;)X?L[X]==="9"?L[X]="0":(L[X]=(parseInt(L[X],10)+1).toString(),te=!1):(parseInt(L[X],10)<5&&(te=!1),L[X]="0"),X+=1;return te&&L.push("1"),L.reverse().join("")}function g(H,L){var X=P(k(L).sort(),function(ie){return ie+":"+L[ie]}).join(","),te=H+"+"+X;return g.cache[te]||(g.cache[te]=Intl.NumberFormat(H,L)),g.cache[te]}g.cache={};function h(H,L,X){var te=L.useToLocaleString,ie=L.useGrouping,Se=ie&&L.grouping.slice(),me=L.maximumSignificantDigits,nt=L.minimumIntegerDigits||1,xe=L.fractionDigits||0,gt=L.groupingSeparator,Mt=L.decimalSeparator;if(te&&X){var Ue={minimumIntegerDigits:nt,useGrouping:ie};if(xe&&(Ue.maximumFractionDigits=xe,Ue.minimumFractionDigits=xe),me&&H>0&&(Ue.maximumSignificantDigits=me),o){if(!s){var ne=O({},L);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return g(X,Ue).format(H)}else{if(!i){var ne=O({},L);ne.useGrouping=!1,ne.decimalSeparator=".",H=parseFloat(h(H,ne),10)}return H.toLocaleString(X,Ue)}}var oe;me?oe=H.toPrecision(me+1):oe=H.toFixed(xe+1);var we,fe,ye,$e=oe.split("e");ye=$e[1]||"",$e=$e[0].split("."),fe=$e[1]||"",we=$e[0]||"";var Pe=we.length,it=fe.length,Me=Pe+it,qe=we+fe;(me&&Me===me+1||!me&&it===xe+1)&&(qe=d(qe),qe.length===Me+1&&(Pe=Pe+1),it&&(qe=qe.slice(0,-1)),we=qe.slice(0,Pe),fe=qe.slice(Pe)),me&&(fe=fe.replace(/0*$/,""));var Te=parseInt(ye,10);Te>0?fe.length<=Te?(fe=fe+c(Te-fe.length),we=we+fe,fe=""):(we=we+fe.slice(0,Te),fe=fe.slice(Te)):Te<0&&(fe=c(Math.abs(Te)-we.length)+we+fe,we="0"),me||(fe=fe.slice(0,xe),fe.length<xe&&(fe=fe+c(xe-fe.length)),we.length<nt&&(we=c(nt-we.length)+we));var Ee="";if(ie){$e=we;for(var De;$e.length;)Se.length&&(De=Se.shift()),Ee&&(Ee=gt+Ee),Ee=$e.slice(-De)+Ee,$e=$e.slice(0,-De)}else Ee=we;return fe&&(Ee=Ee+Mt+fe),Ee}function p(H,L){return H.label.length>L.label.length?-1:H.label.length<L.label.length?1:0}function m(H,L){var X=[];return R(k(L),function(te){if(te.slice(0,15)==="_durationLabels"){var ie=te.slice(15).toLowerCase();R(k(L[te]),function(Se){Se.slice(0,1)===H&&X.push({type:ie,key:Se,label:L[te][Se]})})}}),X}function y(H,L,X){return L===1&&X===null?H:H+H}var b={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:y};function v(H){return Object.prototype.toString.call(H)==="[object Array]"}function C(H){return Object.prototype.toString.call(H)==="[object Object]"}function E(H,L){for(var X=H.length;X-=1;)if(L(H[X]))return H[X]}function I(H,L){var X=0,te=H&&H.length||0,ie;for(typeof L!="function"&&(ie=L,L=function(Se){return Se===ie});X<te;){if(L(H[X]))return H[X];X+=1}}function R(H,L){var X=0,te=H.length;if(!(!H||!te))for(;X<te;){if(L(H[X],X)===!1)return;X+=1}}function P(H,L){var X=0,te=H.length,ie=[];if(!H||!te)return ie;for(;X<te;)ie[X]=L(H[X],X),X+=1;return ie}function x(H,L){return P(H,function(X){return X[L]})}function S(H){var L=[];return R(H,function(X){X&&L.push(X)}),L}function F(H){var L=[];return R(H,function(X){I(L,X)||L.push(X)}),L}function D(H,L){var X=[];return R(H,function(te){R(L,function(ie){te===ie&&X.push(te)})}),F(X)}function M(H,L){var X=[];return R(H,function(te,ie){if(!L(te))return X=H.slice(ie),!1}),X}function T(H,L){var X=H.slice().reverse();return M(X,L).reverse()}function O(H,L){for(var X in L)L.hasOwnProperty(X)&&(H[X]=L[X]);return H}function k(H){var L=[];for(var X in H)H.hasOwnProperty(X)&&L.push(X);return L}function $(H,L){var X=0,te=H.length;if(!H||!te)return!1;for(;X<te;){if(L(H[X],X)===!0)return!0;X+=1}return!1}function G(H){var L=[];return R(H,function(X){L=L.concat(X)}),L}function j(){var H=0;try{H.toLocaleString("i")}catch(L){return L.name==="RangeError"}return!1}function ue(H){return H(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})==="3.6"}function Q(H){var L=!0;return L=L&&H(1,"en",{minimumIntegerDigits:1})==="1",L=L&&H(1,"en",{minimumIntegerDigits:2})==="01",L=L&&H(1,"en",{minimumIntegerDigits:3})==="001",!(!L||(L=L&&H(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0})==="100",L=L&&H(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1})==="100.0",L=L&&H(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2})==="99.99",L=L&&H(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3})==="99.990",!L)||(L=L&&H(99.99,"en",{maximumSignificantDigits:1})==="100",L=L&&H(99.99,"en",{maximumSignificantDigits:2})==="100",L=L&&H(99.99,"en",{maximumSignificantDigits:3})==="100",L=L&&H(99.99,"en",{maximumSignificantDigits:4})==="99.99",L=L&&H(99.99,"en",{maximumSignificantDigits:5})==="99.99",!L)||(L=L&&H(1e3,"en",{useGrouping:!0})==="1,000",L=L&&H(1e3,"en",{useGrouping:!1})==="1000",!L))}function ee(){var H=[].slice.call(arguments),L={},X;if(R(H,function(me,nt){if(!nt){if(!v(me))throw"Expected array as the first argument to durationsFormat.";X=me}if(typeof me=="string"||typeof me=="function"){L.template=me;return}if(typeof me=="number"){L.precision=me;return}C(me)&&O(L,me)}),!X||!X.length)return[];L.returnMomentTypes=!0;var te=P(X,function(me){return me.format(L)}),ie=D(a,F(x(G(te),"type"))),Se=L.largest;return Se&&(ie=ie.slice(0,Se)),L.returnMomentTypes=!1,L.outputTypes=ie,P(X,function(me){return me.format(L)})}function ce(){var H=[].slice.call(arguments),L=O({},this.format.defaults),X=this.asMilliseconds(),te=this.asMonths();typeof this.isValid=="function"&&this.isValid()===!1&&(X=0,te=0);var ie=X<0,Se=n.duration(Math.abs(X),"milliseconds"),me=n.duration(Math.abs(te),"months");R(H,function(N){if(typeof N=="string"||typeof N=="function"){L.template=N;return}if(typeof N=="number"){L.precision=N;return}C(N)&&O(L,N)});var nt={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},xe={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};L.types=a;var gt=function(N){return I(a,function(We){return xe[We].test(N)})},Mt=new RegExp(P(a,function(N){return xe[N].source}).join("|"),"g");L.duration=this;var Ue=typeof L.template=="function"?L.template.apply(L):L.template,ne=L.outputTypes,oe=L.returnMomentTypes,we=L.largest,fe=[];ne||(v(L.stopTrim)&&(L.stopTrim=L.stopTrim.join("")),L.stopTrim&&R(L.stopTrim.match(Mt),function(N){var We=gt(N);We==="escape"||We==="general"||fe.push(We)}));var ye=n.localeData();ye||(ye={}),R(k(b),function(N){if(typeof b[N]=="function"){ye[N]||(ye[N]=b[N]);return}ye["_"+N]||(ye["_"+N]=b[N])}),R(k(ye._durationTimeTemplates),function(N){Ue=Ue.replace("_"+N+"_",ye._durationTimeTemplates[N])});var $e=L.userLocale||n.locale(),Pe=L.useLeftUnits,it=L.usePlural,Me=L.precision,qe=L.forceLength,Te=L.useGrouping,Ee=L.trunc,De=L.useSignificantDigits&&Me>0,ft=De?L.precision:0,ot=ft,Xe=L.minValue,wt=!1,yt=L.maxValue,Ft=!1,It=L.useToLocaleString,dn=L.groupingSeparator,Zt=L.decimalSeparator,fn=L.grouping;It=It&&(r||o);var Ot=L.trim;v(Ot)&&(Ot=Ot.join(" ")),Ot===null&&(we||yt||De)&&(Ot="all"),(Ot===null||Ot===!0||Ot==="left"||Ot==="right")&&(Ot="large"),Ot===!1&&(Ot="");var Ct=function(N){return N.test(Ot)},Tn=/large/,pt=/small/,en=/both/,Bt=/mid/,Le=/^all|[^sm]all/,St=/final/,mt=we>0||$([Tn,en,Le],Ct),Dn=$([pt,en,Le],Ct),Gt=$([Bt,Le],Ct),hn=$([St,Le],Ct),On=P(Ue.match(Mt),function(N,We){var Ge=gt(N);return N.slice(0,1)==="*"&&(N=N.slice(1),Ge!=="escape"&&Ge!=="general"&&fe.push(Ge)),{index:We,length:N.length,text:"",token:Ge==="escape"?N.replace(xe.escape,"$1"):N,type:Ge==="escape"||Ge==="general"?null:Ge}}),Xt={index:0,length:0,token:"",text:"",type:null},tn=[];Pe&&On.reverse(),R(On,function(N){if(N.type){(Xt.type||Xt.text)&&tn.push(Xt),Xt=N;return}Pe?Xt.text=N.token+Xt.text:Xt.text+=N.token}),(Xt.type||Xt.text)&&tn.push(Xt),Pe&&tn.reverse();var Be=D(a,F(S(x(tn,"type"))));if(!Be.length)return x(tn,"text").join("");Be=P(Be,function(N,We){var Ge=We+1===Be.length,Rt=!We,gn;N==="years"||N==="months"?gn=me.as(N):gn=Se.as(N);var ln=Math.floor(gn),Mn=gn-ln,wn=I(tn,function(Wt){return N===Wt.type});return Rt&&yt&&gn>yt&&(Ft=!0),Ge&&Xe&&Math.abs(L.duration.as(N))<Xe&&(wt=!0),Rt&&qe===null&&wn.length>1&&(qe=!0),Se.subtract(ln,N),me.subtract(ln,N),{rawValue:gn,wholeValue:ln,decimalValue:Ge?Mn:0,isSmallest:Ge,isLargest:Rt,type:N,tokenLength:wn.length}});var Pt=Ee?Math.floor:Math.round,$n=function(N,We){var Ge=Math.pow(10,We);return Pt(N*Ge)/Ge},xt=!1,Et=!1,kn=function(N,We){var Ge={useGrouping:Te,groupingSeparator:dn,decimalSeparator:Zt,grouping:fn,useToLocaleString:It};return De&&(ft<=0?(N.rawValue=0,N.wholeValue=0,N.decimalValue=0):(Ge.maximumSignificantDigits=ft,N.significantDigits=ft)),Ft&&!Et&&(N.isLargest?(N.wholeValue=yt,N.decimalValue=0):(N.wholeValue=0,N.decimalValue=0)),wt&&!Et&&(N.isSmallest?(N.wholeValue=Xe,N.decimalValue=0):(N.wholeValue=0,N.decimalValue=0)),N.isSmallest||N.significantDigits&&N.significantDigits-N.wholeValue.toString().length<=0?Me<0?N.value=$n(N.wholeValue,Me):Me===0?N.value=Pt(N.wholeValue+N.decimalValue):De?(Ee?N.value=$n(N.rawValue,ft-N.wholeValue.toString().length):N.value=N.rawValue,N.wholeValue&&(ft-=N.wholeValue.toString().length)):(Ge.fractionDigits=Me,Ee?N.value=N.wholeValue+$n(N.decimalValue,Me):N.value=N.wholeValue+N.decimalValue):De&&N.wholeValue?(N.value=Math.round($n(N.wholeValue,N.significantDigits-N.wholeValue.toString().length)),ft-=N.wholeValue.toString().length):N.value=N.wholeValue,N.tokenLength>1&&(qe||xt)&&(Ge.minimumIntegerDigits=N.tokenLength,Et&&Ge.maximumSignificantDigits<N.tokenLength&&delete Ge.maximumSignificantDigits),!xt&&(N.value>0||Ot===""||I(fe,N.type)||I(ne,N.type))&&(xt=!0),N.formattedValue=h(N.value,Ge,$e),Ge.useGrouping=!1,Ge.decimalSeparator=".",N.formattedValueEn=h(N.value,Ge,"en"),N.tokenLength===2&&N.type==="milliseconds"&&(N.formattedValueMS=h(N.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),N};if(Be=P(Be,kn),Be=S(Be),Be.length>1){var cr=function(N){return I(Be,function(We){return We.type===N})},Nn=function(N){var We=cr(N.type);We&&R(N.targets,function(Ge){var Rt=cr(Ge.type);Rt&&parseInt(We.formattedValueEn,10)===Ge.value&&(We.rawValue=0,We.wholeValue=0,We.decimalValue=0,Rt.rawValue+=1,Rt.wholeValue+=1,Rt.decimalValue=0,Rt.formattedValueEn=Rt.wholeValue.toString(),Et=!0)})};R(l,Nn)}return Et&&(xt=!1,ft=ot,Be=P(Be,kn),Be=S(Be)),ne&&!(Ft&&!L.trim)?(Be=P(Be,function(N){return I(ne,function(We){return N.type===We})?N:null}),Be=S(Be)):(mt&&(Be=M(Be,function(N){return!N.isSmallest&&!N.wholeValue&&!I(fe,N.type)})),we&&Be.length&&(Be=Be.slice(0,we)),Dn&&Be.length>1&&(Be=T(Be,function(N){return!N.wholeValue&&!I(fe,N.type)&&!N.isLargest})),Gt&&(Be=P(Be,function(N,We){return We>0&&We<Be.length-1&&!N.wholeValue?null:N}),Be=S(Be)),hn&&Be.length===1&&!Be[0].wholeValue&&!(!Ee&&Be[0].isSmallest&&Be[0].rawValue<Xe)&&(Be=[])),oe?Be:(R(tn,function(N){var We=nt[N.type],Ge=I(Be,function(Wt){return Wt.type===N.type});if(!(!We||!Ge)){var Rt=Ge.formattedValueEn.split(".");Rt[0]=parseInt(Rt[0],10),Rt[1]?Rt[1]=parseFloat("0."+Rt[1],10):Rt[1]=null;var gn=ye.durationPluralKey(We,Rt[0],Rt[1]),ln=m(We,ye),Mn=!1,wn={};R(ye._durationLabelTypes,function(Wt){var yn=I(ln,function(Pn){return Pn.type===Wt.type&&Pn.key===gn});yn&&(wn[yn.type]=yn.label,u(N.text,Wt.string)&&(N.text=N.text.replace(Wt.string,yn.label),Mn=!0))}),it&&!Mn&&(ln.sort(p),R(ln,function(Wt){if(wn[Wt.type]===Wt.label)return u(N.text,Wt.label)?!1:void 0;if(u(N.text,Wt.label))return N.text=N.text.replace(Wt.label,wn[Wt.type]),!1}))}}),tn=P(tn,function(N){if(!N.type)return N.text;var We=I(Be,function(Rt){return Rt.type===N.type});if(!We)return"";var Ge="";return Pe&&(Ge+=N.text),(ie&&Ft||!ie&&wt)&&(Ge+="< ",Ft=!1,wt=!1),(ie&&wt||!ie&&Ft)&&(Ge+="> ",Ft=!1,wt=!1),ie&&(We.value>0||Ot===""||I(fe,We.type)||I(ne,We.type))&&(Ge+="-",ie=!1),N.type==="milliseconds"&&We.formattedValueMS?Ge+=We.formattedValueMS:Ge+=We.formattedValue,Pe||(Ge+=N.text),Ge}),tn.join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function ae(){var H=this.duration,L=function(Se){return H._data[Se]},X=I(this.types,L),te=E(this.types,L);switch(X){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(X===te)return"d __";case"weeks":return X===te?"w __":(this.trim===null&&(this.trim="both"),"w __, d __, h __");case"months":if(X===te)return"M __";case"years":return X===te?"y __":(this.trim===null&&(this.trim="both"),"y __, M __, d __");default:return this.trim===null&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function ge(H){if(!H)throw"Moment Duration Format init cannot find moment instance.";H.duration.format=ee,H.duration.fn.format=ce,H.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:ae,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},H.updateLocale("en",b)}var de=function(H,L,X){return H.toLocaleString(L,X)};r=j()&&Q(de),i=r&&ue(de);var re=function(H,L,X){if(typeof window<"u"&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(L,X).format(H)};return o=Q(re),s=o&&ue(re),ge(n),ge})}(ra)),ra.exports}yb();const Cb=["true","t","yes","y","on","1"],Sb=["false","f","no","n","off","0"];function Dt(e,t=""){return{kind:Z.Text,readonly:!0,allowOverlay:!0,data:e,displayData:e,errorDetails:t,isError:!0,style:"faded"}}function ki(e){return Object.hasOwn(e,"isError")&&e.isError}function xb(e){return Object.hasOwn(e,"tooltip")&&e.tooltip!==""}function sl(e){return Object.hasOwn(e,"isMissingValue")&&e.isMissingValue}function zs(e=!1){return e?{kind:Z.Loading,allowOverlay:!1,isMissingValue:!0,copyData:""}:{kind:Z.Loading,allowOverlay:!1,copyData:""}}function kb(e,t){const n=t?"faded":"normal";return{kind:Z.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Vs(e){return{id:e.id,title:e.title,hasMenu:!1,menuIcon:"dots",themeOverride:e.themeOverride,icon:e.icon,group:e.group,...e.isStretched&&!e.isPinned&&{grow:1},...e.width&&{width:e.width}}}function Co(e,t){return ze(e)?t||{}:ze(t)?e||{}:Wc(e,t)}function Qd(e){if(ze(e))return[];if(typeof e=="number"||typeof e=="boolean")return[e];if(typeof e=="string"){if(e==="")return[];if(e.trim().startsWith("[")&&e.trim().endsWith("]"))try{return JSON.parse(e)}catch{return[e]}else return e.split(",")}try{const t=JSON.parse(JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r));return Array.isArray(t)?t.map(n=>["string","number","boolean","null"].includes(typeof n)?n:dt(n)):[dt(t)]}catch{return[dt(e)]}}function Mb(e){return typeof e=="string"||e instanceof String?!0:(e instanceof Wh&&(e=Array.from(e)),Array.isArray(e)&&e.every(t=>typeof t=="string"||t instanceof String))}function Rb(e){return e&&e.startsWith("{")&&e.endsWith("}")}function dt(e){try{try{return bb(e)}catch{return JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r)}}catch{return`[${typeof e}]`}}function ef(e){if(ze(e))return null;if(typeof e=="boolean")return e;const t=dt(e).toLowerCase().trim();if(t==="")return null;if(Cb.includes(t))return!0;if(Sb.includes(t))return!1}function po(e){if(ze(e))return null;if(Array.isArray(e))return NaN;if(typeof e=="string"){if(e.trim().length===0)return null;try{const t=no.unformat(e.trim());if(lt(t))return t}catch{}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function Ib(e){return ze(e)?"":dt(e.map(t=>typeof t=="string"&&t.includes(",")?t.replace(/,/g," "):t))}function ga(e){if(ze(e))return"";if(typeof e=="string")return e;try{return JSON.stringify(e,(t,n)=>typeof n=="bigint"?Number(n):n)}catch{return dt(e)}}function Eb(e){if(e===0||Math.abs(e)>=1e-4)return 4;const n=e.toExponential().split("e");return Math.abs(parseInt(n[1],10))}function qr(e,t={}){const n=navigator.languages;try{return new Intl.NumberFormat(n,t).format(e)}catch(r){if(r instanceof RangeError)return new Intl.NumberFormat(void 0,t).format(e);throw r}}function pa(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":ze(t)||t===""?lt(n)?(n===0&&(e=Math.round(e)),no(e).format({thousandSeparated:!1,mantissa:n,trimMantissa:!1})):no(e).format({thousandSeparated:!1,mantissa:Eb(e),trimMantissa:!0}):t==="plain"?no(e).format({thousandSeparated:!1,mantissa:20,trimMantissa:!0}):t==="localized"?qr(e,{minimumFractionDigits:n??void 0,maximumFractionDigits:n??void 0}):t==="percent"?qr(e,{style:"percent",minimumFractionDigits:lt(n)?Math.max(n-2,0):0,maximumFractionDigits:lt(n)?Math.max(n-2,0):2}):t==="dollar"?qr(e,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="euro"?qr(e,{style:"currency",currency:"EUR",currencyDisplay:"narrowSymbol",minimumFractionDigits:n??2,maximumFractionDigits:n??2}):t==="yen"?qr(e,{style:"currency",currency:"JPY",currencyDisplay:"narrowSymbol",minimumFractionDigits:n??0,maximumFractionDigits:n??0}):["compact","scientific","engineering"].includes(t)?qr(e,{notation:t}):t==="accounting"?no(e).format({thousandSeparated:!0,negative:"parenthesis",mantissa:n??2,trimMantissa:!1}):t==="bytes"?qr(e,{notation:"compact",style:"unit",unit:"byte",unitDisplay:"narrow",maximumFractionDigits:1}).replace("BB","GB"):Vg.sprintf(t,e)}function Qu(e,t,n="datetime"){if(t==="localized"){const r=navigator.languages,i=n==="time"?void 0:"medium",o=n==="date"?void 0:"medium";try{return new Intl.DateTimeFormat(r,{dateStyle:i,timeStyle:o}).format(e.toDate())}catch(s){if(s instanceof RangeError)return new Intl.DateTimeFormat(void 0,{dateStyle:i,timeStyle:o}).format(e.toDate());throw s}}else{if(t==="distance")return e.fromNow();if(t==="calendar")return e.calendar();if(t==="iso8601")return n==="date"?e.format("YYYY-MM-DD"):n==="time"?e.format("HH:mm:ss.SSS[Z]"):e.toISOString()}return e.format(t)}function jo(e){if(ze(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if(typeof e=="string"&&e.trim().length===0)return null;try{const t=Number(e);if(!isNaN(t)){let n=t;t>=10**18?n=t/1e3**3:t>=10**15?n=t/1e3**2:t>=10**12&&(n=t/1e3);const r=Gr.unix(n).utc();if(r.isValid())return r.toDate()}if(typeof e=="string"){const n=Gr.utc(e);if(n.isValid())return n.toDate();const r=Gr.utc(e,[Gr.HTML5_FMT.TIME_MS,Gr.HTML5_FMT.TIME_SECONDS,Gr.HTML5_FMT.TIME]);if(r.isValid())return r.toDate()}}catch{return}}function tf(e){if(e%1===0)return 0;let t=e.toString();return t.indexOf("e")!==-1&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),t.indexOf(".")===-1?0:t.split(".")[1].length}function Tb(e,t){if(!Number.isFinite(e))return e;if(t<=0)return Math.trunc(e);const n=10**t,r=e*n,i=Number.EPSILON*Math.abs(r)*10;return Math.trunc(r+Math.sign(r)*i)/n}const Db=new RegExp(/(\r\n|\n|\r)/gm);function mo(e){return e.indexOf(`
`)!==-1?e.replace(Db," "):e}function Ob(e,t){if(ze(t))return"";try{const n=t.match(e);return n&&n[1]!==void 0?decodeURIComponent(n[1].replace(/\+/g,"%20")):t}catch{return t}}const Pb=nr("div",{target:"e1ugbj0f0"})(({theme:e})=>({overflowY:"auto",padding:e.spacing.sm,".react-json-view .copy-icon svg":{fontSize:"0.9em !important",marginRight:`${e.spacing.threeXS} !important`,verticalAlign:"middle !important"}})),nf=({jsonValue:e,theme:t})=>{let n;if(e)try{n=typeof e=="string"?ja.parse(e):ja.parse(ja.stringify(e))}catch{n=void 0}return ze(n)?He(Jr,{highlight:!0,autoFocus:!1,disabled:!0,value:ga(e)??"",onChange:()=>{}}):He(Pb,{"data-testid":"stJsonColumnViewer",children:He(Uh,{src:n,collapsed:2,theme:qh(t.bgCell)>.5?"rjv-default":"monokai",displayDataTypes:!1,displayObjectSize:!1,name:!1,enableClipboard:!0,style:{fontFamily:t.fontFamily,fontSize:t.baseFontStyle,backgroundColor:t.bgCell,whiteSpace:"pre-wrap"}})})},Lb=e=>{const t=e.theme,n=e.value.data;return He(nf,{jsonValue:n.value||n.displayValue,theme:t})},Fb=e=>{const t=e.theme,n=e.value;return He(nf,{jsonValue:n.data,theme:t})},_b={kind:Z.Custom,isMatch:e=>e.data.kind==="json-cell",draw:(e,t)=>{const{value:n,displayValue:r}=t.data;return Qs(e,r??ga(n)??"",t.contentAlign),!0},measure:(e,t,n)=>{const{value:r,displayValue:i}=t.data,o=i??ga(r)??"";return(o?e.measureText(o).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Lb})},rf="line_chart",Ab="area_chart",of="bar_chart";function ll(e,t,n){const r=Co({y_min:null,y_max:null},t.columnTypeOptions),i={kind:Z.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[r.y_min??0,r.y_max??1]}};return{...t,kind:e,typeIcon:e===rf?":material/show_chart:":e===of?":material/bar_chart:":":material/area_chart:",sortMode:"default",isEditable:!1,getCell(o){if(ze(o))return zs();const s=Qd(o),a=[];let l=[];if(s.length===0)return zs();let u=Number.MIN_SAFE_INTEGER,c=Number.MAX_SAFE_INTEGER;for(let h=0;h<s.length;h++){const p=po(s[h]);if(Number.isNaN(p)||ze(p))return Dt(dt(s),`The value cannot be interpreted as a numeric array. ${dt(p)} is not a number.`);p>u&&(u=p),p<c&&(c=p),a.push(p)}let d,g;if(s.length===1){let h;u<=0?h=u===0?1:0:h=u,d=r.y_max??h,g=r.y_min??(u>=0?0:u)}else d=r.y_max??u,g=r.y_min??c;return ze(g)||ze(d)||Number.isNaN(g)||Number.isNaN(d)||g>=d?Dt("Invalid min/max y-axis configuration",`The y_min (${g}) and y_max (${d}) configuration options must be valid numbers.`):(a.length>0&&(u>d||c<g)?l=a.map(h=>u-c===0?u>(d||1)?d:g:(d-g)*((h-c)/(u-c))+g):l=a,{...i,copyData:a.join(","),data:{...i.data,values:l,displayValues:a.map(h=>pa(h)),yAxis:[g,d]},isMissingValue:ze(o)})},getCellValue(o){return o.kind===Z.Loading||o.data?.values===void 0?null:o.data?.values}}}function af(e){return ll(rf,e,"line")}af.isEditableType=!1;function sf(e){return ll(of,e,"bar")}sf.isEditableType=!1;function lf(e){return ll(Ab,e,"area")}lf.isEditableType=!1;function ul(e,t){const n={kind:Z.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:"normal"};return{...e,kind:"checkbox",typeIcon:":material/check_box:",sortMode:"default",themeOverride:{roundingRadius:Math.round(Math.min(rn(t.radii.md),rn(t.radii.maxCheckbox)))},getCell(r){let i=null;return i=ef(r),i===void 0?Dt(dt(r),"The value cannot be interpreted as boolean."):{...n,data:i,isMissingValue:ze(i)}},getCellValue(r){return r.data===void 0?null:r.data}}}ul.isEditableType=!0;function ec(e,t){return t.startsWith("+")||t.startsWith("-")?e=e.utcOffset(t,!1):e=e.tz(t),e}function cl(e,t,n,r,i,o,s){const a=Co({format:n,step:r,timezone:s},t.columnTypeOptions);let l;if(lt(a.timezone))try{l=ec(Bl(),a.timezone)?.utcOffset()||void 0}catch{}let u;lt(a.min_value)&&(u=jo(a.min_value)||void 0);let c;lt(a.max_value)&&(c=jo(a.max_value)||void 0);const d={kind:Z.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isPinned?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:a.step?.toString()||"1",format:i,min:u,max:c}},g=h=>{const p=jo(h);return p===null?!t.isRequired:!(p===void 0||lt(u)&&o(p)<o(u)||lt(c)&&o(p)>o(c))};return{...t,kind:e,typeIcon:e==="date"?":material/calendar_month:":e==="time"?":material/access_time:":":material/calendar_today:",sortMode:"default",validateInput:g,getCell(h,p){if(p===!0){const C=g(h);if(C===!1)return Dt(dt(h),"Invalid input.");C instanceof Date&&(h=C)}const m=jo(h);let y="",b="",v=l;if(m===void 0)return Dt(dt(h),"The value cannot be interpreted as a datetime object.");if(m!==null){let C=Bl.utc(m);if(!C.isValid())return Dt(dt(m),`Invalid moment date. This should never happen. Please report this bug. 
Error: ${C.toString()}`);if(a.timezone){try{C=ec(C,a.timezone)}catch(E){return Dt(C.toISOString(),`Failed to adjust to the provided timezone: ${a.timezone}. 
Error: ${E}`)}v=C.utcOffset()}try{b=Qu(C,a.format||n,e)}catch(E){return Dt(C.toISOString(),`Failed to format the date for rendering with: ${a.format}. 
Error: ${E}`)}y=Qu(C,n,e)}return{...d,copyData:y,isMissingValue:ze(m),data:{...d.data,date:m,displayDate:b,timezoneOffset:v}}},getCellValue(h){return ze(h?.data?.date)?null:o(h.data.date)}}}function dl(e){let t="YYYY-MM-DD HH:mm:ss";e.columnTypeOptions?.step>=60?t="YYYY-MM-DD HH:mm":e.columnTypeOptions?.step<1&&(t="YYYY-MM-DD HH:mm:ss.SSS");const n=Gh(e.arrowType),r=lt(n)||lt(e?.columnTypeOptions?.timezone);return cl("datetime",e,r?t+"Z":t,1,"datetime-local",i=>r?i.toISOString():i.toISOString().replace("Z",""),n)}dl.isEditableType=!0;function fl(e){let t="HH:mm:ss";return e.columnTypeOptions?.step>=60?t="HH:mm":e.columnTypeOptions?.step<1&&(t="HH:mm:ss.SSS"),cl("time",e,t,1,"time",n=>n.toISOString().split("T")[1].replace("Z",""))}fl.isEditableType=!0;function hl(e){return cl("date",e,"YYYY-MM-DD",1,"date",t=>t.toISOString().split("T")[0])}hl.isEditableType=!0;function uf(e){const t={kind:Z.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:"normal"};return{...e,kind:"image",typeIcon:":material/image:",sortMode:"default",isEditable:!1,getCell(n){const r=lt(n)?[dt(n)]:[];return{...t,data:r,isMissingValue:!lt(n),displayData:r}},getCellValue(n){return n.data===void 0||n.data.length===0?null:n.data[0]}}}uf.isEditableType=!1;function cf(e){const t={kind:Z.Custom,allowOverlay:!0,contentAlign:e.contentAlignment,readonly:!0,style:e.isPinned?"faded":"normal",copyData:"",data:{kind:"json-cell",value:""}};return{...e,kind:"json",typeIcon:":material/code_blocks:",sortMode:"default",isEditable:!1,getCell(n){try{const r=lt(n)?mo(ga(n)):"";return{...t,copyData:r,isMissingValue:ze(n),data:{...t.data,value:n,displayValue:r}}}catch(r){return Dt(dt(n),`The value cannot be interpreted as a JSON string. Error: ${r}`)}},getCellValue(n){return n.data?.value??null}}}cf.isEditableType=!1;function df(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(l){n=`Invalid validate regex: ${t.validate}.
Error: ${l}`}let r=!1,i=t.display_text,o;if(!ze(i)){if(i.startsWith(":material/")&&Xh(i))i=Yh(i).icon,r=!0;else if(i.includes("(")&&i.includes(")"))try{o=new RegExp(i,"us")}catch{o=void 0}}const s={kind:Z.Uri,readonly:!e.isEditable,allowOverlay:!r,contentAlign:e.contentAlignment??(r?"center":void 0),style:"normal",hoverEffect:!0,data:"",displayData:"",copyData:"",...r&&{themeOverride:{fontFamily:jh.iconFont,linkColor:void 0}}},a=l=>{if(ze(l))return!e.isRequired;const u=dt(l);return!(t.max_chars&&u.length>t.max_chars||n instanceof RegExp&&n.test(u)===!1)};return{...e,kind:"link",typeIcon:":material/link:",sortMode:"default",validateInput:a,getCell(l,u){if(ze(l))return{...s,data:null,isMissingValue:!0,onClickUri:()=>{},themeOverride:void 0};const c=l;if(typeof n=="string")return Dt(dt(c),n);if(u&&a(c)===!1)return Dt(dt(c),"Invalid input.");let d="";return c&&(o!==void 0?d=Ob(o,c):d=i||c),{...s,data:c,displayData:d,isMissingValue:ze(c),onClickUri:g=>{window.open(c.startsWith("www.")?`https://${c}`:c,"_blank","noopener,noreferrer"),g.preventDefault()},copyData:c}},getCellValue(l){return ze(l.data)?null:l.data}}}df.isEditableType=!0;function gl(e){const t={kind:Z.Custom,readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal",copyData:"",data:{kind:"multi-select-cell",values:[],options:void 0,allowCreation:!0,allowDuplicates:!0}};return{...e,kind:"list",sortMode:"default",typeIcon:":material/list:",getCell(n){if(ze(n))return{...t,data:{...t.data,values:null},isMissingValue:!0,copyData:""};const r=Qd(n);return{...t,data:{...t.data,values:r},copyData:Ib(r),...e.isEditable&&!Mb(n)&&{readonly:!0,isError:!0,errorDetails:"Editing of arrays with non-string values is not supported. Please disable editing or convert all values to strings."}}},getCellValue(n){return ze(n.data?.values)?null:n.data.values}}}gl.isEditableType=!0;function pl(e){const t=Co({step:Uc(e.arrowType)?1:void 0,min_value:Kh(e.arrowType)?0:void 0},e.columnTypeOptions),n=!t.format&&(Zh(e.arrowType)||Jh(e.arrowType)),r=ze(t.min_value)||t.min_value<0,i=lt(t.step)&&!Number.isNaN(t.step)?tf(t.step):void 0,o={kind:Z.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||n?"left":"right",style:e.isPinned?"faded":"normal",allowNegative:r,fixedDecimals:i,thousandSeparator:""},s=a=>{let l=po(a);if(ze(l))return!e.isRequired;if(Number.isNaN(l))return!1;let u=!1;return lt(t.max_value)&&l>t.max_value&&(l=t.max_value,u=!0),lt(t.min_value)&&l<t.min_value?!1:u?l:!0};return{...e,kind:"number",sortMode:"smart",typeIcon:":material/tag:",validateInput:s,getCell(a,l){if(l===!0){const d=s(a);if(d===!1)return Dt(dt(a),"Invalid input.");typeof d=="number"&&(a=d)}let u=po(a),c="";if(lt(u)){if(Number.isNaN(u))return Dt(dt(a),"The value cannot be interpreted as a number.");if(lt(i)&&(u=Tb(u,i)),Number.isInteger(u)&&!Number.isSafeInteger(u))return Dt(dt(a),"The value is larger than the maximum supported integer values in number columns (2^53).");try{n?c=Es(u,e.arrowType):c=pa(u,t.format,i)}catch(d){return Dt(dt(u),lt(t.format)?`Failed to format the number based on the provided format configuration: (${t.format}). Error: ${d}`:`Failed to format the number. Error: ${d}`)}}return{...o,data:u,displayData:c,isMissingValue:ze(u),copyData:ze(u)?"":dt(u)}},getCellValue(a){return a.data===void 0?null:a.data}}}pl.isEditableType=!0;function vo(e){const t={kind:Z.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!0,style:e.isPinned?"faded":"normal"};return{...e,kind:"object",sortMode:"default",typeIcon:":material/data_object:",isEditable:!1,getCell(n){try{const r=lt(n)?dt(n):null,i=lt(r)?mo(r):"";return{...t,data:r,displayData:i,isMissingValue:ze(n)}}catch(r){return Dt(dt(n),`The value cannot be interpreted as a string. Error: ${r}`)}},getCellValue(n){return n.data===void 0?null:n.data}}}vo.isEditableType=!1;function ff(e){const t=Uc(e.arrowType),n=Co({min_value:0,max_value:t?100:1,format:t?"%3d%%":"percent",step:t?1:void 0},e.columnTypeOptions),r=ze(n.step)||Number.isNaN(n.step)?void 0:tf(n.step);let i;try{i=pa(n.max_value,n.format,r)}catch{i=dt(n.max_value)}const o={kind:Z.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,readonly:!0,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step??.01,value:n.min_value,label:String(n.min_value),measureLabel:i}};return{...e,kind:"progress",sortMode:"smart",typeIcon:":material/commit:",isEditable:!1,getCell(s){if(ze(s))return zs();if(ze(n.min_value)||ze(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return Dt("Invalid min/max parameters",`The min_value (${n.min_value}) and max_value (${n.max_value}) parameters must be valid numbers.`);if(lt(n.step)&&Number.isNaN(n.step))return Dt("Invalid step parameter",`The step parameter (${n.step}) must be a valid number.`);const a=po(s);if(Number.isNaN(a)||ze(a))return Dt(dt(s),"The value cannot be interpreted as a number.");if(Number.isInteger(a)&&!Number.isSafeInteger(a))return Dt(dt(s),"The value is larger than the maximum supported integer values in number columns (2^53).");let l="";try{l=pa(a,n.format,r)}catch(c){return Dt(dt(a),lt(n.format)?`Failed to format the number based on the provided format configuration: (${n.format}). Error: ${c}`:`Failed to format the number. Error: ${c}`)}const u=Math.min(n.max_value,Math.max(n.min_value,a));return{...o,isMissingValue:ze(s),copyData:String(a),data:{...o.data,value:u,label:l,measureLabel:l.length>i.length?l:i}}},getCellValue(s){return s.kind===Z.Loading||s.data?.value===void 0?null:s.data?.value}}}ff.isEditableType=!1;const Hb=e=>ze(e)?[]:e.filter(t=>lt(t)&&t!=="").map(t=>{if(typeof t=="object"&&"value"in t){const r=dt(t.value).trim();return{value:r,label:t.label??r}}const n=dt(t).trim();return{value:n,label:n}});function ml(e){let t="string";const n=Co({options:qc(e.arrowType)?[!0,!1]:e.arrowType.categoricalOptions??[]},e.columnTypeOptions),r=l=>typeof l=="object"&&l!==null&&"value"in l,i=l=>r(l)?typeof l.value:typeof l,o=new Set(n.options.map(i));o.size===1&&(o.has("number")||o.has("bigint")?t="number":o.has("boolean")&&(t="boolean"));const s=Hb(n.options),a={kind:Z.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isPinned?"faded":"normal",data:{kind:"dropdown-cell",allowedValues:[...e.isRequired!==!0?[null]:[],...s],value:""}};return{...e,kind:"selectbox",sortMode:"default",typeIcon:":material/arrow_drop_down_circle:",getCell(l,u){let c=null;return lt(l)&&l!==""&&(c=dt(l)),u&&!a.data.allowedValues.some(g=>g===null?c===null:typeof g=="string"?g===c:typeof g=="object"&&"value"in g?g.value===c:!1)?Dt(dt(c),"The value is not part of the allowed options."):{...a,isMissingValue:c===null,copyData:c||"",data:{...a.data,value:c}}},getCellValue(l){return ze(l.data?.value)||l.data?.value===""?null:t==="number"?po(l.data?.value)??null:t==="boolean"?ef(l.data?.value)??null:l.data?.value}}}ml.isEditableType=!0;function vl(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(o){n=`Invalid validate regex: ${t.validate}.
Error: ${o}`}const r={kind:Z.Text,data:"",displayData:"",allowOverlay:!0,contentAlign:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!e.isEditable,style:e.isPinned?"faded":"normal"},i=o=>{if(ze(o))return!e.isRequired;let s=dt(o),a=!1;return t.max_chars&&s.length>t.max_chars&&(s=s.slice(0,t.max_chars),a=!0),n instanceof RegExp&&n.test(s)===!1?!1:a?s:!0};return{...e,kind:"text",sortMode:"default",typeIcon:":material/notes:",validateInput:i,getCell(o,s){if(typeof n=="string")return Dt(dt(o),n);if(s){const a=i(o);if(a===!1)return Dt(dt(o),"Invalid input.");typeof a=="string"&&(o=a)}try{const a=lt(o)?dt(o):null,l=lt(a)?mo(a):"";return{...r,isMissingValue:ze(a),data:a,displayData:l}}catch(a){return Dt("Incompatible value",`The value cannot be interpreted as string. Error: ${a}`)}},getCellValue(o){return o.data===void 0?null:o.data}}}vl.isEditableType=!0;const tc=nr("img",{target:"e3b94od0"})({maxWidth:"100%",maxHeight:"37.5rem",objectFit:"scale-down"}),zb=({urls:e})=>{const t=e&&e.length>0?e[0]:"";return t.startsWith("http")?He("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:He(tc,{src:t})}):He(tc,{src:t})},nc=new Map(Object.entries({object:vo,text:vl,checkbox:ul,selectbox:ml,list:gl,number:pl,link:df,datetime:dl,date:hl,time:fl,line_chart:af,bar_chart:sf,area_chart:lf,image:uf,progress:ff,json:cf})),Vb=[_b];var $b=Gc();const Nb=vr($b);var vs,rc;function Bb(){if(rc)return vs;rc=1;var e=Qh(),t=eg(),n=tg(),r=Gc(),i=ng(),o=rg(),s=ig(),a=og(),l="[object Map]",u="[object Set]",c=Object.prototype,d=c.hasOwnProperty;function g(h){if(h==null)return!0;if(i(h)&&(r(h)||typeof h=="string"||typeof h.splice=="function"||o(h)||a(h)||n(h)))return!h.length;var p=t(h);if(p==l||p==u)return!h.size;if(s(h))return!e(h).length;for(var m in h)if(d.call(h,m))return!1;return!0}return vs=g,vs}var Wb=Bb();const Ub=vr(Wb);function bs(e,t,n){if(!n.includes(t))return;const r=new RegExp(`${e}[,\\s].*{(?:[^}]*[\\s;]{1})?${t}:\\s*([^;}]+)[;]?.*}`,"gm");n=n.replace(/{/g," {");const i=r.exec(n);if(i)return i[1].trim()}function qb(e,t,n){const r={};if(!n.includes(t))return e;const i=bs(t,"color",n);i&&(r.textDark=i,(e.kind===Z.Bubble||e.kind===Z.Custom&&e.data?.kind==="multi-select-cell")&&(r.textBubble=i),e.kind===Z.Uri&&(r.linkColor=i));const o=bs(t,"background-color",n);o&&(r.bgCell=o),o==="yellow"&&i===void 0&&(r.textDark="#31333F");const s=bs(t,"font-weight",n);return s&&(r.baseFontStyle=`${s} ${yg.sm}`),r?{...e,themeOverride:r}:e}function Gb(e){return dg(e)||fg(e)?vl:hg(e)?dl:Xc(e)?fl:gg(e)?hl:pg(e)||mg(e)?vo:qc(e)?ul:vg(e)?pl:bg(e)?ml:wg(e)?gl:vo}function hf(e){const t=e.length>0?e[e.length-1]:"",n=e.length>1?e.slice(0,-1).filter(r=>r!=="").join(" / "):void 0;return{title:t,group:n}}function bl(e){return{group:void 0,isEditable:!1,isIndex:!1,isPinned:!1,isHidden:!1,isStretched:!1,...e}}function Xb(e,t){const n=e.columnNames.map(a=>a[t]),{title:r,group:i}=hf(n),o=e.columnTypes[t];let s=!0;return cg(o)&&(s=!1),bl({id:`_index-${t}`,indexNumber:t,name:r,title:r,group:i,isEditable:s,arrowType:o,isIndex:!0,isPinned:!0})}function Yb(e,t){const n=e.columnNames.map(s=>s[t]),{title:r,group:i}=hf(n),o=e.columnTypes[t];return bl({id:`_column-${r}-${t}`,indexNumber:t,name:r,isEditable:!0,title:r,arrowType:o,group:i})}function gf(){return bl({id:"_empty-index",indexNumber:0,title:"",name:"",isEditable:!1,isIndex:!0,isPinned:!0,arrowType:{type:Cg.INDEX,arrowField:new ag("",new sg,!0),pandasType:void 0}})}function ic(e){const t=[],{dimensions:n}=e,r=n.numIndexColumns,i=n.numDataColumns;if(r===0&&i===0)return t.push(gf()),t;for(let o=0;o<r;o++)t.push(Xb(e,o));for(let o=0;o<i;o++)t.push(Yb(e,o+r));return t}function jb(e,t,n,r=void 0){let i;if(e.kind==="object"||e.kind==="json")i=e.getCell(lt(t.content)?mo(Es(t.content,t.contentType)):null);else if(["time","date","datetime"].includes(e.kind)&&lt(t.content)&&(typeof t.content=="number"||typeof t.content=="bigint")){let o;Xc(t.contentType)&&lt(t.field?.type?.unit)?o=lg(t.content,t.field):o=Gr.utc(Number(t.content)).toDate(),i=e.getCell(o)}else if(ug(t.contentType)){const o=ze(t.content)?null:Es(t.content,t.contentType);i=e.getCell(o)}else i=e.getCell(t.content);if(ki(i))return i;if(!e.isEditable){if(n&&lt(n?.displayContent)){const o=mo(n.displayContent);i.kind===Z.Text?i={...i,displayData:o}:i.kind===Z.Number&&ze(e.columnTypeOptions?.format)?i={...i,displayData:o}:i.kind===Z.Uri&&ze(e.columnTypeOptions?.display_text)?i={...i,displayData:o}:i.kind===Z.Custom&&i.data?.kind==="date-picker-cell"&&ze(e.columnTypeOptions?.format)&&(i={...i,data:{...i.data,displayDate:o}})}r&&n?.cssId&&(i=qb(i,n.cssId,r))}return i}function pf(e,t){return t?t?.useStretch??!1:e.useContainerWidth??!1}function mf(e,t){return t?t.pixelWidth?t.pixelWidth:void 0:e.width||void 0}function Kb(e){return e?.useContent??!1}function Zb(e,t){return t?t.pixelHeight?t.pixelHeight:void 0:e.height||void 0}const ia="_index",oc="_pos:",ac={small:75,medium:200,large:400},vf=qs.getLogger("useColumnLoader");function Jb(e){if(!ze(e)){if(typeof e=="number")return e;if(e in ac)return ac[e]}}const Ko=(e,t)=>Sg(e,t,(r,i)=>{if(Nb(i))return i});function sc(e,t){if(!t)return e;let n={};return e.isIndex&&t.has(ia)&&(n=Ko(n,t.get(ia)??{})),t.has(`${oc}${e.indexNumber}`)&&(n=Ko(n,t.get(`${oc}${e.indexNumber}`)??{})),t.has(e.name)&&e.name!==ia&&(n=Ko(n,t.get(e.name)??{})),t.has(e.id)&&(n=Ko(n,t.get(e.id)??{})),Ub(n)?e:Wc({...e},{title:n.label,width:Jb(n.width),isEditable:lt(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isPinned:n.pinned,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help})}function Qb(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return vf.error(t),new Map}}function lc(e){const t=e.columnTypeOptions?.type;let n;return lt(t)&&(nc.has(t)?n=nc.get(t):vf.warn(`Unknown column type configured in column configuration: ${t}`)),ze(n)&&(n=Gb(e.arrowType)),n}function e1(e,t,n,r,i){const o=Kr(),s=f.useMemo(()=>Qb(e.columns),[e.columns]),[a,l]=f.useState(s);f.useEffect(()=>{l(s)},[s]);const u=f.useMemo(()=>pf(e,i),[e,i]),c=f.useMemo(()=>mf(e,i),[e,i]),d=u||lt(c)&&c>0,g=lt(e.rowHeight)&&e.rowHeight>rn("4rem"),h=f.useMemo(()=>ic(t).map(m=>{let y={...m,...sc(m,a),isStretched:d};const b=lc(y);return(e.editingMode===on.EditingMode.READ_ONLY||n||b.isEditableType===!1)&&(y={...y,isEditable:!1}),e.editingMode!==on.EditingMode.READ_ONLY&&y.isEditable==!0&&(y={...y,icon:"editable"},y.isRequired&&e.editingMode===on.EditingMode.DYNAMIC&&(y={...y,isHidden:!1})),b(y,o)}),[t,a,d,e.editingMode,n,o]);return{columns:f.useMemo(()=>{const m=ic(t).map(C=>{let E={...C,...sc(C,a),isStretched:d,isWrappingAllowed:g};const I=lc(E);return(e.editingMode===on.EditingMode.READ_ONLY||n||I.isEditableType===!1)&&(E={...E,isEditable:!1}),e.editingMode!==on.EditingMode.READ_ONLY&&E.isEditable==!0&&(E={...E,icon:"editable"},E.isRequired&&e.editingMode===on.EditingMode.DYNAMIC&&(E={...E,isHidden:!1})),I(E,o)}).filter(C=>!C.isHidden),y=[],b=[];r?.length?(m.forEach(C=>{C.isIndex&&!r.includes(C.name)&&!r.includes(C.id)&&C.isPinned!==!1&&y.push(C)}),r.forEach(C=>{const E=m.find(I=>I.name===C||I.id===C);E&&(E.isPinned?y.push(E):b.push(E))})):m.forEach(C=>{C.isPinned?y.push(C):b.push(C)});const v=[...y,...b];return v.length>0?v:[vo(gf())]},[t,a,g,d,n,e.editingMode,r,o]),allColumns:h,setColumnConfigMapping:l}}function Xr(e){return e.isIndex?ia:ze(e.name)?"":e.name}class Zo{constructor(t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=t}toJson(t){const n=new Map;t.forEach(o=>{n.set(o.indexNumber,o)});const r={edited_rows:{},added_rows:[],deleted_rows:[]};return this.editedCells.forEach((o,s,a)=>{const l={};o.forEach((u,c)=>{const d=n.get(c);d&&(l[Xr(d)]=d.getCellValue(u))}),r.edited_rows[s]=l}),this.addedRows.forEach(o=>{const s={};let a=!1;o.forEach((l,u,c)=>{const d=n.get(u);if(d){const g=d.getCellValue(l);d.isRequired&&d.isEditable&&sl(l)&&(a=!0),lt(g)&&(s[Xr(d)]=g)}}),a||r.added_rows.push(s)}),r.deleted_rows=this.deletedRows,JSON.stringify(r,(o,s)=>s===void 0?null:s)}fromJson(t,n){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const r=JSON.parse(t),i=new Map;n.forEach(s=>{i.set(s.indexNumber,s)});const o=new Map;n.forEach(s=>{o.set(Xr(s),s)}),Object.keys(r.edited_rows).forEach(s=>{const a=Number(s),l=r.edited_rows[s];Object.keys(l).forEach(u=>{const c=l[u],d=o.get(u);if(d){const g=d.getCell(c);g&&(this.editedCells.has(a)||this.editedCells.set(a,new Map),this.editedCells.get(a)?.set(d.indexNumber,g))}})}),r.added_rows.forEach(s=>{const a=new Map;n.forEach(l=>{a.set(l.indexNumber,l.getCell(null))}),Object.keys(s).forEach(l=>{const u=s[l],c=o.get(l);if(c){const d=c.getCell(u);d&&a.set(c.indexNumber,d)}}),this.addedRows.push(a)}),this.deletedRows=r.deleted_rows}isAddedRow(t){return t>=this.numRows}getCell(t,n){if(this.isAddedRow(n))return this.addedRows[n-this.numRows].get(t);const r=this.editedCells.get(n);if(r!==void 0)return r.get(t)}setCell(t,n,r){if(this.isAddedRow(n)){if(n-this.numRows>=this.addedRows.length)return;this.addedRows[n-this.numRows].set(t,r)}else this.editedCells.get(n)===void 0&&this.editedCells.set(n,new Map),this.editedCells.get(n).set(t,r)}addRow(t){this.addedRows.push(t)}deleteRows(t){t.sort((n,r)=>r-n).forEach(n=>{this.deleteRow(n)})}deleteRow(t){if(!(ze(t)||t<0)){if(this.isAddedRow(t)){this.addedRows.splice(t-this.numRows,1);return}this.deletedRows.includes(t)||(this.deletedRows.push(t),this.deletedRows=this.deletedRows.sort((n,r)=>n-r)),this.editedCells.delete(t)}}getOriginalRowIndex(t){let n=t;for(let r=0;r<this.deletedRows.length&&!(this.deletedRows[r]>n);r++)n+=1;return n}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}}const bo=({columnId:e,columnConfigMapping:t,updatedProps:n})=>{const r=new Map(t),i=r.get(e),o={...i||{},...n||{}};return(i?.type_config||n?.type_config)&&(o.type_config={...i?.type_config||{},...n?.type_config||{}}),r.set(e,o),r};function t1(e){return{changeColumnFormat:f.useCallback((n,r)=>{e(i=>bo({columnId:n,columnConfigMapping:i,updatedProps:{type_config:{format:r}}}))},[e])}}function n1(e,t,n,r,i,o){const s=f.useMemo(()=>e.filter(c=>c.isPinned).reduce((c,d)=>c+(d.width??r*2),0)>n*.6,[e,n,r]),a=t||s?0:e.filter(c=>c.isPinned).length,l=f.useCallback(c=>{o(d=>bo({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!1}})),i(!0,!1)},[i,o]);return{pinColumn:f.useCallback(c=>{o(d=>bo({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!0}})),i(!0,!1)},[i,o]),unpinColumn:l,freezeColumns:a}}function r1(e,t,n,r,i){return{onColumnMoved:f.useCallback((s,a)=>{const l=[...e],[u]=l.splice(s,1);l.splice(a,0,u),a<t&&!u.isPinned?n(u.id):a>=t&&u.isPinned&&r(u.id),i(l.map(c=>c.id))},[e,t,n,r,i])}}function i1(e){const[t,n]=f.useState(()=>new Map),r=f.useCallback((o,s,a,l)=>{o.id&&n(new Map(t).set(o.id,l))},[t]);return{columns:f.useMemo(()=>e.map(o=>o.id&&t.has(o.id)&&t.get(o.id)!==void 0?{...o,width:t.get(o.id),grow:0}:o),[e,t]),onColumnResize:r}}function o1(e){switch(e.kind){case Z.Number:return e.data?.toString()??"";case Z.Boolean:return e.data?.toString()??"";case Z.Markdown:case Z.RowID:case Z.Text:case Z.Uri:return e.data??"";case Z.Bubble:case Z.Image:return e.data.join("");case Z.Drilldown:return e.data.map(t=>t.text).join("");case Z.Protected:case Z.Loading:return"";case Z.Custom:return e.copyData}}function uc(e){if(typeof e=="number")return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function a1(e,t){return e=uc(e),t=uc(t),typeof e=="string"&&typeof t=="string"?e.localeCompare(t):typeof e=="number"&&typeof t=="number"?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}function s1(e,t){return e>t?1:e===t?0:-1}function l1(e){const{sort:t,rows:n,getCellContent:r}=e,i=f.useMemo(()=>t===void 0?[]:Array.isArray(t)?t:[t],[t]),o=f.useMemo(()=>i.map(u=>{const c=e.columns.findIndex(d=>u.column===d||d.id!==void 0&&u.column.id===d.id);return c===-1?void 0:c}),[i,e.columns]),s=f.useMemo(()=>{const u=i.map((d,g)=>({sort:d,col:o[g]})).filter(d=>d.col!==void 0);if(u.length===0)return;const c=u.map(()=>new Array(n));for(let d=0;d<u.length;d++){const{col:g}=u[d],h=[g,0];for(let p=0;p<n;p++)h[1]=p,c[d][p]=o1(r(h))}return Or(n).sort((d,g)=>{for(let h=0;h<u.length;h++){const{sort:p}=u[h],m=c[h][d],y=c[h][g];let b;if(p.mode==="raw"?b=s1(m,y):p.mode==="smart"?b=a1(m,y):b=m.localeCompare(y),b!==0)return(p.direction??"asc")==="desc"&&(b=-b),b}return 0})},[r,n,i,o]),a=f.useCallback(u=>s===void 0?u:s[u],[s]),l=f.useCallback(([u,c])=>s===void 0?r([u,c]):(c=s[c],r([u,c])),[r,s]);return s===void 0?{getCellContent:e.getCellContent,getOriginalIndex:a}:{getOriginalIndex:a,getCellContent:l}}function u1(e,t){return t===void 0?e:e.map(n=>n.id===t.column.id?{...n,title:t.direction==="asc"?`↑ ${n.title}`:`↓ ${n.title}`}:n)}function c1(e,t,n){const[r,i]=f.useState(),{getCellContent:o,getOriginalIndex:s}=l1({columns:t.map(u=>Vs(u)),getCellContent:n,rows:e,sort:r}),a=f.useMemo(()=>u1(t,r),[t,r]),l=f.useCallback((u,c,d)=>{const g=a[u];let h;c==="auto"?(h="asc",r&&r.column.id===g.id&&(r.direction==="asc"?h="desc":h=void 0)):h=c,h===void 0||d&&h===r?.direction?i(void 0):i({column:Vs(g),direction:h,mode:g.sortMode})},[r,a]);return{columns:a,sortColumn:l,getOriginalIndex:s,getCellContent:o}}function d1(e,t){const n=f.useCallback(i=>{t(o=>bo({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!0}})),e(!0,!1)},[e,t]),r=f.useCallback(i=>{t(o=>bo({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!1}})),e(!0,!1)},[e,t]);return{hideColumn:n,showColumn:r}}function f1(){return{provideEditor:f.useCallback(t=>{if(t.kind===Z.Text&&t.readonly&&Rb(t.data))return{editor:Fb}},[])}}const h1={kind:Z.Custom,isMatch:e=>e.data.kind==="sparkline-cell",needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:o,hoverX:s}=e;let{values:a,yAxis:l,color:u,graphKind:c="area",displayValues:d,hideAxis:g}=t.data;const[h,p]=l;if(a.length===0)return!0;a=a.map(R=>Math.min(1,Math.max(0,(R-h)/(p-h))));const m=r.cellHorizontalPadding,y=m+i.x,b=i.y+3,v=i.height-6,C=i.width-m*2,E=p-h,I=p<=0?b:h>=0?b+v:b+v*(p/E);if(!g&&h<=0&&p>=0&&(n.beginPath(),n.moveTo(y,I),n.lineTo(y+C,I),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.globalAlpha=1),c==="bar"){n.beginPath();const R=2,P=(a.length-1)*R,x=(C-P)/a.length;let S=y;for(const F of a){const D=b+v-F*v;n.moveTo(S,I),n.lineTo(S+x,I),n.lineTo(S+x,D),n.lineTo(S,D),S+=x+R}n.fillStyle=t.data.color??r.accentColor,n.fill()}else{a.length===1&&(a=[a[0],a[0]],d&&(d=[d[0],d[0]])),n.beginPath();const R=(i.width-16)/(a.length-1),P=a.map((S,F)=>({x:y+R*F,y:b+v-S*v}));n.moveTo(P[0].x,P[0].y);let x=0;if(P.length>2)for(x=1;x<P.length-2;x++){const S=(P[x].x+P[x+1].x)/2,F=(P[x].y+P[x+1].y)/2;n.quadraticCurveTo(P[x].x,P[x].y,S,F)}if(n.quadraticCurveTo(P[x].x,P[x].y,P[x+1].x,P[x+1].y),n.strokeStyle=u??r.accentColor,n.lineWidth=1+o*.5,n.stroke(),n.lineTo(i.x+i.width-m,I),n.lineTo(i.x+m,I),n.closePath(),c==="area"){n.globalAlpha=.2+.2*o;const S=n.createLinearGradient(0,b,0,b+v*1.4);S.addColorStop(0,u??r.accentColor);const[F,D,M]=fo(u??r.accentColor);S.addColorStop(1,`rgba(${F}, ${D}, ${M}, 0)`),n.fillStyle=S,n.fill(),n.globalAlpha=1}if(s!==void 0&&(c==="line"||c==="area")&&d!==void 0){n.beginPath();const S=Math.min(a.length-1,Math.max(0,Math.round((s-m)/R)));n.moveTo(y+S*R,i.y+1),n.lineTo(y+S*R,i.y+i.height),n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.save(),n.font=`8px ${r.fontFamily}`,n.fillStyle=r.textMedium,n.textBaseline="top",n.fillText(d[S],y,i.y+r.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t};function cc(e,t,n,r,i,o){if(!(r<=0||i<=0)){if(typeof o=="number"&&o<=0){e.rect(t,n,r,i);return}typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},o.tl=Math.max(0,o.tl),o.tr=Math.max(0,o.tr),o.br=Math.max(0,o.br),o.bl=Math.max(0,o.bl),e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}}function dc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Oe(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?dc(Object(n),!0).forEach(function(r){ro(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dc(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var g1=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function bf(e){var t=e.defaultInputValue,n=t===void 0?"":t,r=e.defaultMenuIsOpen,i=r===void 0?!1:r,o=e.defaultValue,s=o===void 0?null:o,a=e.inputValue,l=e.menuIsOpen,u=e.onChange,c=e.onInputChange,d=e.onMenuClose,g=e.onMenuOpen,h=e.value,p=ur(e,g1),m=f.useState(a!==void 0?a:n),y=gr(m,2),b=y[0],v=y[1],C=f.useState(l!==void 0?l:i),E=gr(C,2),I=E[0],R=E[1],P=f.useState(h!==void 0?h:s),x=gr(P,2),S=x[0],F=x[1],D=f.useCallback(function(j,ue){typeof u=="function"&&u(j,ue),F(j)},[u]),M=f.useCallback(function(j,ue){var Q;typeof c=="function"&&(Q=c(j,ue)),v(Q!==void 0?Q:j)},[c]),T=f.useCallback(function(){typeof g=="function"&&g(),R(!0)},[g]),O=f.useCallback(function(){typeof d=="function"&&d(),R(!1)},[d]),k=a!==void 0?a:b,$=l!==void 0?l:I,G=h!==void 0?h:S;return Oe(Oe({},p),{},{inputValue:k,menuIsOpen:$,onChange:D,onInputChange:M,onMenuClose:O,onMenuOpen:T,value:G})}function p1(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const m1=Math.min,v1=Math.max,ma=Math.round,Jo=Math.floor,va=e=>({x:e,y:e});function b1(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function Ia(){return typeof window<"u"}function wf(e){return Cf(e)?(e.nodeName||"").toLowerCase():"#document"}function mr(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function yf(e){var t;return(t=(Cf(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Cf(e){return Ia()?e instanceof Node||e instanceof mr(e).Node:!1}function w1(e){return Ia()?e instanceof Element||e instanceof mr(e).Element:!1}function wl(e){return Ia()?e instanceof HTMLElement||e instanceof mr(e).HTMLElement:!1}function fc(e){return!Ia()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof mr(e).ShadowRoot}function Sf(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=yl(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function y1(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function C1(e){return["html","body","#document"].includes(wf(e))}function yl(e){return mr(e).getComputedStyle(e)}function S1(e){if(wf(e)==="html")return e;const t=e.assignedSlot||e.parentNode||fc(e)&&e.host||yf(e);return fc(t)?t.host:t}function xf(e){const t=S1(e);return C1(t)?e.ownerDocument?e.ownerDocument.body:e.body:wl(t)&&Sf(t)?t:xf(t)}function ba(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=xf(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),s=mr(i);if(o){const a=$s(s);return t.concat(s,s.visualViewport||[],Sf(i)?i:[],a&&n?ba(a):[])}return t.concat(i,ba(i,[],n))}function $s(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function x1(e){const t=yl(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=wl(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=ma(n)!==o||ma(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function Cl(e){return w1(e)?e:e.contextElement}function hc(e){const t=Cl(e);if(!wl(t))return va(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=x1(t);let s=(o?ma(n.width):n.width)/r,a=(o?ma(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const k1=va(0);function M1(e){const t=mr(e);return!y1()||!t.visualViewport?k1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function R1(e,t,n){return!1}function gc(e,t,n,r){t===void 0&&(t=!1);const i=e.getBoundingClientRect(),o=Cl(e);let s=va(1);t&&(s=hc(e));const a=R1()?M1(o):va(0);let l=(i.left+a.x)/s.x,u=(i.top+a.y)/s.y,c=i.width/s.x,d=i.height/s.y;if(o){const g=mr(o),h=r;let p=g,m=$s(p);for(;m&&r&&h!==p;){const y=hc(m),b=m.getBoundingClientRect(),v=yl(m),C=b.left+(m.clientLeft+parseFloat(v.paddingLeft))*y.x,E=b.top+(m.clientTop+parseFloat(v.paddingTop))*y.y;l*=y.x,u*=y.y,c*=y.x,d*=y.y,l+=C,u+=E,p=mr(m),m=$s(p)}}return b1({width:c,height:d,x:l,y:u})}function I1(e,t){let n=null,r;const i=yf(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const{left:u,top:c,width:d,height:g}=e.getBoundingClientRect();if(a||t(),!d||!g)return;const h=Jo(c),p=Jo(i.clientWidth-(u+d)),m=Jo(i.clientHeight-(c+g)),y=Jo(u),v={rootMargin:-h+"px "+-p+"px "+-m+"px "+-y+"px",threshold:v1(0,m1(1,l))||1};let C=!0;function E(I){const R=I[0].intersectionRatio;if(R!==l){if(!C)return s();R?s(!1,R):r=setTimeout(()=>{s(!1,1e-7)},1e3)}C=!1}try{n=new IntersectionObserver(E,{...v,root:i.ownerDocument})}catch{n=new IntersectionObserver(E,v)}n.observe(e)}return s(!0),o}function E1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Cl(e),c=i||o?[...u?ba(u):[],...ba(t)]:[];c.forEach(b=>{i&&b.addEventListener("scroll",n,{passive:!0}),o&&b.addEventListener("resize",n)});const d=u&&a?I1(u,n):null;let g=-1,h=null;s&&(h=new ResizeObserver(b=>{let[v]=b;v&&v.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var C;(C=h)==null||C.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let p,m=l?gc(e):null;l&&y();function y(){const b=gc(e);m&&(b.x!==m.x||b.y!==m.y||b.width!==m.width||b.height!==m.height)&&n(),m=b,p=requestAnimationFrame(y)}return n(),()=>{var b;c.forEach(v=>{i&&v.removeEventListener("scroll",n),o&&v.removeEventListener("resize",n)}),d?.(),(b=h)==null||b.disconnect(),h=null,l&&cancelAnimationFrame(p)}}var Ns=f.useLayoutEffect,T1=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],wa=function(){};function D1(e,t){return t?t[0]==="-"?e+t:e+"__"+t:e}function O1(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=[].concat(r);if(t&&e)for(var s in t)t.hasOwnProperty(s)&&t[s]&&o.push("".concat(D1(e,s)));return o.filter(function(a){return a}).map(function(a){return String(a).trim()}).join(" ")}var ya=function(t){return $1(t)?t.filter(Boolean):xg(t)==="object"&&t!==null?[t]:[]},kf=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var n=ur(t,T1);return Oe({},n)},Kt=function(t,n,r){var i=t.cx,o=t.getStyles,s=t.getClassNames,a=t.className;return{css:o(n,t),className:i(r??{},s(n,t),a)}};function Ea(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function P1(e){return Ea(e)?window.innerHeight:e.clientHeight}function Mf(e){return Ea(e)?window.pageYOffset:e.scrollTop}function Ca(e,t){if(Ea(e)){window.scrollTo(0,t);return}e.scrollTop=t}function L1(e){var t=getComputedStyle(e),n=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),!(n&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}function F1(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function Qo(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:wa,i=Mf(e),o=t-i,s=10,a=0;function l(){a+=s;var u=F1(a,i,o,n);Ca(e,u),a<n?window.requestAnimationFrame(l):r(e)}l()}function pc(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?Ca(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&Ca(e,Math.max(t.offsetTop-i,0))}function _1(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function mc(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function A1(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var Rf=!1,H1={get passive(){return Rf=!0}},ea=typeof window<"u"?window:{};ea.addEventListener&&ea.removeEventListener&&(ea.addEventListener("p",wa,H1),ea.removeEventListener("p",wa,!1));var z1=Rf;function V1(e){return e!=null}function $1(e){return Array.isArray(e)}function io(e,t,n){return e?t:n}var N1=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=Object.entries(t).filter(function(s){var a=gr(s,1),l=a[0];return!r.includes(l)});return o.reduce(function(s,a){var l=gr(a,2),u=l[0],c=l[1];return s[u]=c,s},{})},B1=["children","innerProps"],W1=["children","innerProps"];function U1(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,s=e.isFixedPosition,a=e.controlHeight,l=L1(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var c=l.getBoundingClientRect(),d=c.height,g=n.getBoundingClientRect(),h=g.bottom,p=g.height,m=g.top,y=n.offsetParent.getBoundingClientRect(),b=y.top,v=s?window.innerHeight:P1(l),C=Mf(l),E=parseInt(getComputedStyle(n).marginBottom,10),I=parseInt(getComputedStyle(n).marginTop,10),R=b-I,P=v-m,x=R+C,S=d-C-m,F=h-v+C+E,D=C+m-I,M=160;switch(i){case"auto":case"bottom":if(P>=p)return{placement:"bottom",maxHeight:t};if(S>=p&&!s)return o&&Qo(l,F,M),{placement:"bottom",maxHeight:t};if(!s&&S>=r||s&&P>=r){o&&Qo(l,F,M);var T=s?P-E:S-E;return{placement:"bottom",maxHeight:T}}if(i==="auto"||s){var O=t,k=s?R:x;return k>=r&&(O=Math.min(k-E-a,t)),{placement:"top",maxHeight:O}}if(i==="bottom")return o&&Ca(l,F),{placement:"bottom",maxHeight:t};break;case"top":if(R>=p)return{placement:"top",maxHeight:t};if(x>=p&&!s)return o&&Qo(l,D,M),{placement:"top",maxHeight:t};if(!s&&x>=r||s&&R>=r){var $=t;return(!s&&x>=r||s&&R>=r)&&($=s?R-I:x-I),o&&Qo(l,D,M),{placement:"top",maxHeight:$}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return u}function q1(e){var t={bottom:"top",top:"bottom"};return e?t[e]:"bottom"}var If=function(t){return t==="auto"?"bottom":t},G1=function(t,n){var r,i=t.placement,o=t.theme,s=o.borderRadius,a=o.spacing,l=o.colors;return Oe((r={label:"menu"},ro(r,q1(i),"100%"),ro(r,"position","absolute"),ro(r,"width","100%"),ro(r,"zIndex",1),r),n?{}:{backgroundColor:l.neutral0,borderRadius:s,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:a.menuGutter,marginTop:a.menuGutter})},Ef=f.createContext(null),X1=function(t){var n=t.children,r=t.minMenuHeight,i=t.maxMenuHeight,o=t.menuPlacement,s=t.menuPosition,a=t.menuShouldScrollIntoView,l=t.theme,u=f.useContext(Ef)||{},c=u.setPortalPlacement,d=f.useRef(null),g=f.useState(i),h=gr(g,2),p=h[0],m=h[1],y=f.useState(null),b=gr(y,2),v=b[0],C=b[1],E=l.spacing.controlHeight;return Ns(function(){var I=d.current;if(I){var R=s==="fixed",P=a&&!R,x=U1({maxHeight:i,menuEl:I,minHeight:r,placement:o,shouldScroll:P,isFixedPosition:R,controlHeight:E});m(x.maxHeight),C(x.placement),c?.(x.placement)}},[i,o,s,a,r,c,E]),n({ref:d,placerProps:Oe(Oe({},t),{},{placement:v||If(o),maxHeight:p})})},Y1=function(t){var n=t.children,r=t.innerRef,i=t.innerProps;return Fe("div",Ae({},Kt(t,"menu",{menu:!0}),{ref:r},i),n)},j1=Y1,K1=function(t,n){var r=t.maxHeight,i=t.theme.spacing.baseUnit;return Oe({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},n?{}:{paddingBottom:i,paddingTop:i})},Z1=function(t){var n=t.children,r=t.innerProps,i=t.innerRef,o=t.isMulti;return Fe("div",Ae({},Kt(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:i},r),n)},Tf=function(t,n){var r=t.theme,i=r.spacing.baseUnit,o=r.colors;return Oe({textAlign:"center"},n?{}:{color:o.neutral40,padding:"".concat(i*2,"px ").concat(i*3,"px")})},J1=Tf,Q1=Tf,ew=function(t){var n=t.children,r=n===void 0?"No options":n,i=t.innerProps,o=ur(t,B1);return Fe("div",Ae({},Kt(Oe(Oe({},o),{},{children:r,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),r)},tw=function(t){var n=t.children,r=n===void 0?"Loading...":n,i=t.innerProps,o=ur(t,W1);return Fe("div",Ae({},Kt(Oe(Oe({},o),{},{children:r,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),r)},nw=function(t){var n=t.rect,r=t.offset,i=t.position;return{left:n.left,position:i,top:r,width:n.width,zIndex:1}},rw=function(t){var n=t.appendTo,r=t.children,i=t.controlElement,o=t.innerProps,s=t.menuPlacement,a=t.menuPosition,l=f.useRef(null),u=f.useRef(null),c=f.useState(If(s)),d=gr(c,2),g=d[0],h=d[1],p=f.useMemo(function(){return{setPortalPlacement:h}},[]),m=f.useState(null),y=gr(m,2),b=y[0],v=y[1],C=f.useCallback(function(){if(i){var P=_1(i),x=a==="fixed"?0:window.pageYOffset,S=P[g]+x;(S!==b?.offset||P.left!==b?.rect.left||P.width!==b?.rect.width)&&v({offset:S,rect:P})}},[i,a,g,b?.offset,b?.rect.left,b?.rect.width]);Ns(function(){C()},[C]);var E=f.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),i&&l.current&&(u.current=E1(i,l.current,C,{elementResize:"ResizeObserver"in window}))},[i,C]);Ns(function(){E()},[E]);var I=f.useCallback(function(P){l.current=P,E()},[E]);if(!n&&a!=="fixed"||!b)return null;var R=Fe("div",Ae({ref:I},Kt(Oe(Oe({},t),{},{offset:b.offset,position:a,rect:b.rect}),"menuPortal",{"menu-portal":!0}),o),r);return Fe(Ef.Provider,{value:p},n?Yc.createPortal(R,n):R)},iw=function(t){var n=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:n?"none":void 0,position:"relative"}},ow=function(t){var n=t.children,r=t.innerProps,i=t.isDisabled,o=t.isRtl;return Fe("div",Ae({},Kt(t,"container",{"--is-disabled":i,"--is-rtl":o}),r),n)},aw=function(t,n){var r=t.theme.spacing,i=t.isMulti,o=t.hasValue,s=t.selectProps.controlShouldRenderValue;return Oe({alignItems:"center",display:i&&o&&s?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},n?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},sw=function(t){var n=t.children,r=t.innerProps,i=t.isMulti,o=t.hasValue;return Fe("div",Ae({},Kt(t,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":o}),r),n)},lw=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},uw=function(t){var n=t.children,r=t.innerProps;return Fe("div",Ae({},Kt(t,"indicatorsContainer",{indicators:!0}),r),n)},vc,cw=["size"],dw=["innerProps","isRtl","size"],fw={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},Df=function(t){var n=t.size,r=ur(t,cw);return Fe("svg",Ae({height:n,width:n,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:fw},r))},Sl=function(t){return Fe(Df,Ae({size:20},t),Fe("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},Of=function(t){return Fe(Df,Ae({size:20},t),Fe("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},Pf=function(t,n){var r=t.isFocused,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Oe({label:"indicatorContainer",display:"flex",transition:"color 150ms"},n?{}:{color:r?s.neutral60:s.neutral20,padding:o*2,":hover":{color:r?s.neutral80:s.neutral40}})},hw=Pf,gw=function(t){var n=t.children,r=t.innerProps;return Fe("div",Ae({},Kt(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),n||Fe(Of,null))},pw=Pf,mw=function(t){var n=t.children,r=t.innerProps;return Fe("div",Ae({},Kt(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),n||Fe(Sl,null))},vw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing.baseUnit,s=i.colors;return Oe({label:"indicatorSeparator",alignSelf:"stretch",width:1},n?{}:{backgroundColor:r?s.neutral10:s.neutral20,marginBottom:o*2,marginTop:o*2})},bw=function(t){var n=t.innerProps;return Fe("span",Ae({},n,Kt(t,"indicatorSeparator",{"indicator-separator":!0})))},ww=kg(vc||(vc=p1([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),yw=function(t,n){var r=t.isFocused,i=t.size,o=t.theme,s=o.colors,a=o.spacing.baseUnit;return Oe({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:i,lineHeight:1,marginRight:i,textAlign:"center",verticalAlign:"middle"},n?{}:{color:r?s.neutral60:s.neutral20,padding:a*2})},ws=function(t){var n=t.delay,r=t.offset;return Fe("span",{css:jc({animation:"".concat(ww," 1s ease-in-out ").concat(n,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},Cw=function(t){var n=t.innerProps,r=t.isRtl,i=t.size,o=i===void 0?4:i,s=ur(t,dw);return Fe("div",Ae({},Kt(Oe(Oe({},s),{},{innerProps:n,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),n),Fe(ws,{delay:0,offset:r}),Fe(ws,{delay:160,offset:!0}),Fe(ws,{delay:320,offset:!r}))},Sw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.theme,s=o.colors,a=o.borderRadius,l=o.spacing;return Oe({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:l.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},n?{}:{backgroundColor:r?s.neutral5:s.neutral0,borderColor:r?s.neutral10:i?s.primary:s.neutral20,borderRadius:a,borderStyle:"solid",borderWidth:1,boxShadow:i?"0 0 0 1px ".concat(s.primary):void 0,"&:hover":{borderColor:i?s.primary:s.neutral30}})},xw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.innerRef,s=t.innerProps,a=t.menuIsOpen;return Fe("div",Ae({ref:o},Kt(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":i,"control--menu-is-open":a}),s,{"aria-disabled":r||void 0}),n)},kw=xw,Mw=["data"],Rw=function(t,n){var r=t.theme.spacing;return n?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},Iw=function(t){var n=t.children,r=t.cx,i=t.getStyles,o=t.getClassNames,s=t.Heading,a=t.headingProps,l=t.innerProps,u=t.label,c=t.theme,d=t.selectProps;return Fe("div",Ae({},Kt(t,"group",{group:!0}),l),Fe(s,Ae({},a,{selectProps:d,theme:c,getStyles:i,getClassNames:o,cx:r}),u),Fe("div",null,n))},Ew=function(t,n){var r=t.theme,i=r.colors,o=r.spacing;return Oe({label:"group",cursor:"default",display:"block"},n?{}:{color:i.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},Tw=function(t){var n=kf(t);n.data;var r=ur(n,Mw);return Fe("div",Ae({},Kt(t,"groupHeading",{"group-heading":!0}),r))},Dw=Iw,Ow=["innerRef","isDisabled","isHidden","inputClassName"],Pw=function(t,n){var r=t.isDisabled,i=t.value,o=t.theme,s=o.spacing,a=o.colors;return Oe(Oe({visibility:r?"hidden":"visible",transform:i?"translateZ(0)":""},Lw),n?{}:{margin:s.baseUnit/2,paddingBottom:s.baseUnit/2,paddingTop:s.baseUnit/2,color:a.neutral80})},Lf={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Lw={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Oe({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},Lf)},Fw=function(t){return Oe({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},Lf)},_w=function(t){var n=t.cx,r=t.value,i=kf(t),o=i.innerRef,s=i.isDisabled,a=i.isHidden,l=i.inputClassName,u=ur(i,Ow);return Fe("div",Ae({},Kt(t,"input",{"input-container":!0}),{"data-value":r||""}),Fe("input",Ae({className:n({input:!0},l),ref:o,style:Fw(a),disabled:s},u)))},Aw=_w,Hw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors;return Oe({label:"multiValue",display:"flex",minWidth:0},n?{}:{backgroundColor:s.neutral10,borderRadius:o/2,margin:i.baseUnit/2})},zw=function(t,n){var r=t.theme,i=r.borderRadius,o=r.colors,s=t.cropWithEllipsis;return Oe({overflow:"hidden",textOverflow:s||s===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},n?{}:{borderRadius:i/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},Vw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,s=r.colors,a=t.isFocused;return Oe({alignItems:"center",display:"flex"},n?{}:{borderRadius:o/2,backgroundColor:a?s.dangerLight:void 0,paddingLeft:i.baseUnit,paddingRight:i.baseUnit,":hover":{backgroundColor:s.dangerLight,color:s.danger}})},Ff=function(t){var n=t.children,r=t.innerProps;return Fe("div",r,n)},$w=Ff,Nw=Ff;function Bw(e){var t=e.children,n=e.innerProps;return Fe("div",Ae({role:"button"},n),t||Fe(Sl,{size:14}))}var Ww=function(t){var n=t.children,r=t.components,i=t.data,o=t.innerProps,s=t.isDisabled,a=t.removeProps,l=t.selectProps,u=r.Container,c=r.Label,d=r.Remove;return Fe(u,{data:i,innerProps:Oe(Oe({},Kt(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":s})),o),selectProps:l},Fe(c,{data:i,innerProps:Oe({},Kt(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:l},n),Fe(d,{data:i,innerProps:Oe(Oe({},Kt(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(n||"option")},a),selectProps:l}))},Uw=Ww,qw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.theme,a=s.spacing,l=s.colors;return Oe({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},n?{}:{backgroundColor:o?l.primary:i?l.primary25:"transparent",color:r?l.neutral20:o?l.neutral0:"inherit",padding:"".concat(a.baseUnit*2,"px ").concat(a.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?l.primary:l.primary50}})},Gw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.isSelected,s=t.innerRef,a=t.innerProps;return Fe("div",Ae({},Kt(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":i,"option--is-selected":o}),{ref:s,"aria-disabled":r},a),n)},Xw=Gw,Yw=function(t,n){var r=t.theme,i=r.spacing,o=r.colors;return Oe({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},n?{}:{color:o.neutral50,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},jw=function(t){var n=t.children,r=t.innerProps;return Fe("div",Ae({},Kt(t,"placeholder",{placeholder:!0}),r),n)},Kw=jw,Zw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing,s=i.colors;return Oe({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n?{}:{color:r?s.neutral40:s.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Jw=function(t){var n=t.children,r=t.isDisabled,i=t.innerProps;return Fe("div",Ae({},Kt(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),i),n)},Qw=Jw,xl={ClearIndicator:mw,Control:kw,DropdownIndicator:gw,DownChevron:Of,CrossIcon:Sl,Group:Dw,GroupHeading:Tw,IndicatorsContainer:uw,IndicatorSeparator:bw,Input:Aw,LoadingIndicator:Cw,Menu:j1,MenuList:Z1,MenuPortal:rw,LoadingMessage:tw,NoOptionsMessage:ew,MultiValue:Uw,MultiValueContainer:$w,MultiValueLabel:Nw,MultiValueRemove:Bw,Option:Xw,Placeholder:Kw,SelectContainer:ow,SingleValue:Qw,ValueContainer:sw},ey=function(t){return Oe(Oe({},xl),t.components)},bc=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function ty(e,t){return!!(e===t||bc(e)&&bc(t))}function ny(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!ty(e[n],t[n]))return!1;return!0}function ry(e,t){t===void 0&&(t=ny);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var s=e.apply(this,i);return n={lastResult:s,lastArgs:i,lastThis:this},s}return r.clear=function(){n=null},r}var iy={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},oy=function(t){return Fe("span",Ae({css:iy},t))},wc=oy,ay={guidance:function(t){var n=t.isSearchable,r=t.isMulti,i=t.tabSelectsValue,o=t.context,s=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return s?"".concat(t["aria-label"]||"Select"," is focused ").concat(n?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var n=t.action,r=t.label,i=r===void 0?"":r,o=t.labels,s=t.isDisabled;switch(n){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(i,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return s?"option ".concat(i," is disabled. Select another option."):"option ".concat(i,", selected.");default:return""}},onFocus:function(t){var n=t.context,r=t.focused,i=t.options,o=t.label,s=o===void 0?"":o,a=t.selectValue,l=t.isDisabled,u=t.isSelected,c=t.isAppleDevice,d=function(m,y){return m&&m.length?"".concat(m.indexOf(y)+1," of ").concat(m.length):""};if(n==="value"&&a)return"value ".concat(s," focused, ").concat(d(a,r),".");if(n==="menu"&&c){var g=l?" disabled":"",h="".concat(u?" selected":"").concat(g);return"".concat(s).concat(h,", ").concat(d(i,r),".")}return""},onFilter:function(t){var n=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(n?" for search term "+n:"",".")}},sy=function(t){var n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.focusableOptions,s=t.isFocused,a=t.selectValue,l=t.selectProps,u=t.id,c=t.isAppleDevice,d=l.ariaLiveMessages,g=l.getOptionLabel,h=l.inputValue,p=l.isMulti,m=l.isOptionDisabled,y=l.isSearchable,b=l.menuIsOpen,v=l.options,C=l.screenReaderStatus,E=l.tabSelectsValue,I=l.isLoading,R=l["aria-label"],P=l["aria-live"],x=f.useMemo(function(){return Oe(Oe({},ay),d||{})},[d]),S=f.useMemo(function(){var k="";if(n&&x.onChange){var $=n.option,G=n.options,j=n.removedValue,ue=n.removedValues,Q=n.value,ee=function(L){return Array.isArray(L)?null:L},ce=j||$||ee(Q),ae=ce?g(ce):"",ge=G||ue||void 0,de=ge?ge.map(g):[],re=Oe({isDisabled:ce&&m(ce,a),label:ae,labels:de},n);k=x.onChange(re)}return k},[n,x,m,a,g]),F=f.useMemo(function(){var k="",$=r||i,G=!!(r&&a&&a.includes(r));if($&&x.onFocus){var j={focused:$,label:g($),isDisabled:m($,a),isSelected:G,options:o,context:$===r?"menu":"value",selectValue:a,isAppleDevice:c};k=x.onFocus(j)}return k},[r,i,g,m,x,o,a,c]),D=f.useMemo(function(){var k="";if(b&&v.length&&!I&&x.onFilter){var $=C({count:o.length});k=x.onFilter({inputValue:h,resultsMessage:$})}return k},[o,h,b,x,v,C,I]),M=n?.action==="initial-input-focus",T=f.useMemo(function(){var k="";if(x.guidance){var $=i?"value":b?"menu":"input";k=x.guidance({"aria-label":R,context:$,isDisabled:r&&m(r,a),isMulti:p,isSearchable:y,tabSelectsValue:E,isInitialFocus:M})}return k},[R,r,i,p,m,y,b,x,a,E,M]),O=Fe(f.Fragment,null,Fe("span",{id:"aria-selection"},S),Fe("span",{id:"aria-focused"},F),Fe("span",{id:"aria-results"},D),Fe("span",{id:"aria-guidance"},T));return Fe(f.Fragment,null,Fe(wc,{id:u},M&&O),Fe(wc,{"aria-live":P,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},s&&!M&&O))},ly=sy,Bs=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],uy=new RegExp("["+Bs.map(function(e){return e.letters}).join("")+"]","g"),_f={};for(var ys=0;ys<Bs.length;ys++)for(var Cs=Bs[ys],Ss=0;Ss<Cs.letters.length;Ss++)_f[Cs.letters[Ss]]=Cs.base;var Af=function(t){return t.replace(uy,function(n){return _f[n]})},cy=ry(Af),yc=function(t){return t.replace(/^\s+|\s+$/g,"")},dy=function(t){return"".concat(t.label," ").concat(t.value)},fy=function(t){return function(n,r){if(n.data.__isNew__)return!0;var i=Oe({ignoreCase:!0,ignoreAccents:!0,stringify:dy,trim:!0,matchFrom:"any"},t),o=i.ignoreCase,s=i.ignoreAccents,a=i.stringify,l=i.trim,u=i.matchFrom,c=l?yc(r):r,d=l?yc(a(n)):a(n);return o&&(c=c.toLowerCase(),d=d.toLowerCase()),s&&(c=cy(c),d=Af(d)),u==="start"?d.substr(0,c.length)===c:d.indexOf(c)>-1}},hy=["innerRef"];function gy(e){var t=e.innerRef,n=ur(e,hy),r=N1(n,"onExited","in","enter","exit","appear");return Fe("input",Ae({ref:t},r,{css:jc({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var py=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function my(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,s=f.useRef(!1),a=f.useRef(!1),l=f.useRef(0),u=f.useRef(null),c=f.useCallback(function(y,b){if(u.current!==null){var v=u.current,C=v.scrollTop,E=v.scrollHeight,I=v.clientHeight,R=u.current,P=b>0,x=E-I-C,S=!1;x>b&&s.current&&(r&&r(y),s.current=!1),P&&a.current&&(o&&o(y),a.current=!1),P&&b>x?(n&&!s.current&&n(y),R.scrollTop=E,S=!0,s.current=!0):!P&&-b>C&&(i&&!a.current&&i(y),R.scrollTop=0,S=!0,a.current=!0),S&&py(y)}},[n,r,i,o]),d=f.useCallback(function(y){c(y,y.deltaY)},[c]),g=f.useCallback(function(y){l.current=y.changedTouches[0].clientY},[]),h=f.useCallback(function(y){var b=l.current-y.changedTouches[0].clientY;c(y,b)},[c]),p=f.useCallback(function(y){if(y){var b=z1?{passive:!1}:!1;y.addEventListener("wheel",d,b),y.addEventListener("touchstart",g,b),y.addEventListener("touchmove",h,b)}},[h,g,d]),m=f.useCallback(function(y){y&&(y.removeEventListener("wheel",d,!1),y.removeEventListener("touchstart",g,!1),y.removeEventListener("touchmove",h,!1))},[h,g,d]);return f.useEffect(function(){if(t){var y=u.current;return p(y),function(){m(y)}}},[t,p,m]),function(y){u.current=y}}var Cc=["boxSizing","height","overflow","paddingRight","position"],Sc={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function xc(e){e.cancelable&&e.preventDefault()}function kc(e){e.stopPropagation()}function Mc(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;e===0?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function Rc(){return"ontouchstart"in window||navigator.maxTouchPoints}var Ic=!!(typeof window<"u"&&window.document&&window.document.createElement),to=0,pi={capture:!1,passive:!1};function vy(e){var t=e.isEnabled,n=e.accountForScrollbars,r=n===void 0?!0:n,i=f.useRef({}),o=f.useRef(null),s=f.useCallback(function(l){if(Ic){var u=document.body,c=u&&u.style;if(r&&Cc.forEach(function(p){var m=c&&c[p];i.current[p]=m}),r&&to<1){var d=parseInt(i.current.paddingRight,10)||0,g=document.body?document.body.clientWidth:0,h=window.innerWidth-g+d||0;Object.keys(Sc).forEach(function(p){var m=Sc[p];c&&(c[p]=m)}),c&&(c.paddingRight="".concat(h,"px"))}u&&Rc()&&(u.addEventListener("touchmove",xc,pi),l&&(l.addEventListener("touchstart",Mc,pi),l.addEventListener("touchmove",kc,pi))),to+=1}},[r]),a=f.useCallback(function(l){if(Ic){var u=document.body,c=u&&u.style;to=Math.max(to-1,0),r&&to<1&&Cc.forEach(function(d){var g=i.current[d];c&&(c[d]=g)}),u&&Rc()&&(u.removeEventListener("touchmove",xc,pi),l&&(l.removeEventListener("touchstart",Mc,pi),l.removeEventListener("touchmove",kc,pi)))}},[r]);return f.useEffect(function(){if(t){var l=o.current;return s(l),function(){a(l)}}},[t,s,a]),function(l){o.current=l}}var by=function(t){var n=t.target;return n.ownerDocument.activeElement&&n.ownerDocument.activeElement.blur()},wy={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function yy(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=r===void 0?!0:r,o=e.onBottomArrive,s=e.onBottomLeave,a=e.onTopArrive,l=e.onTopLeave,u=my({isEnabled:i,onBottomArrive:o,onBottomLeave:s,onTopArrive:a,onTopLeave:l}),c=vy({isEnabled:n}),d=function(h){u(h),c(h)};return Fe(f.Fragment,null,n&&Fe("div",{onClick:by,css:wy}),t(d))}var Cy={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},Sy=function(t){var n=t.name,r=t.onFocus;return Fe("input",{required:!0,name:n,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:Cy,value:"",onChange:function(){}})},xy=Sy;function kl(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function ky(){return kl(/^iPhone/i)}function Hf(){return kl(/^Mac/i)}function My(){return kl(/^iPad/i)||Hf()&&navigator.maxTouchPoints>1}function Ry(){return ky()||My()}function Iy(){return Hf()||Ry()}var Ey=function(t){return t.label},zf=function(t){return t.label},Vf=function(t){return t.value},Ty=function(t){return!!t.isDisabled},Dy={clearIndicator:pw,container:iw,control:Sw,dropdownIndicator:hw,group:Rw,groupHeading:Ew,indicatorsContainer:lw,indicatorSeparator:vw,input:Pw,loadingIndicator:yw,loadingMessage:Q1,menu:G1,menuList:K1,menuPortal:nw,multiValue:Hw,multiValueLabel:zw,multiValueRemove:Vw,noOptionsMessage:J1,option:qw,placeholder:Yw,singleValue:Zw,valueContainer:aw},Oy={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},Py=4,$f=4,Ly=38,Fy=$f*2,_y={baseUnit:$f,controlHeight:Ly,menuGutter:Fy},xs={borderRadius:Py,colors:Oy,spacing:_y},Ay={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:mc(),captureMenuScroll:!mc(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:fy(),formatGroupLabel:Ey,getOptionLabel:zf,getOptionValue:Vf,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:Ty,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!A1(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var n=t.count;return"".concat(n," result").concat(n!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function Ec(e,t,n,r){var i=Wf(e,t,n),o=Uf(e,t,n),s=Bf(e,t),a=Sa(e,t);return{type:"option",data:t,isDisabled:i,isSelected:o,label:s,value:a,index:r}}function oa(e,t){return e.options.map(function(n,r){if("options"in n){var i=n.options.map(function(s,a){return Ec(e,s,t,a)}).filter(function(s){return Dc(e,s)});return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=Ec(e,n,t,r);return Dc(e,o)?o:void 0}).filter(V1)}function Nf(e){return e.reduce(function(t,n){return n.type==="group"?t.push.apply(t,xi(n.options.map(function(r){return r.data}))):t.push(n.data),t},[])}function Tc(e,t){return e.reduce(function(n,r){return r.type==="group"?n.push.apply(n,xi(r.options.map(function(i){return{data:i.data,id:"".concat(t,"-").concat(r.index,"-").concat(i.index)}}))):n.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),n},[])}function Hy(e,t){return Nf(oa(e,t))}function Dc(e,t){var n=e.inputValue,r=n===void 0?"":n,i=t.data,o=t.isSelected,s=t.label,a=t.value;return(!Gf(e)||!o)&&qf(e,{label:s,value:a,data:i},r)}function zy(e,t){var n=e.focusedValue,r=e.selectValue,i=r.indexOf(n);if(i>-1){var o=t.indexOf(n);if(o>-1)return n;if(i<t.length)return t[i]}return null}function Vy(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}var ks=function(t,n){var r,i=(r=t.find(function(o){return o.data===n}))===null||r===void 0?void 0:r.id;return i||null},Bf=function(t,n){return t.getOptionLabel(n)},Sa=function(t,n){return t.getOptionValue(n)};function Wf(e,t,n){return typeof e.isOptionDisabled=="function"?e.isOptionDisabled(t,n):!1}function Uf(e,t,n){if(n.indexOf(t)>-1)return!0;if(typeof e.isOptionSelected=="function")return e.isOptionSelected(t,n);var r=Sa(e,t);return n.some(function(i){return Sa(e,i)===r})}function qf(e,t,n){return e.filterOption?e.filterOption(t,n):!0}var Gf=function(t){var n=t.hideSelectedOptions,r=t.isMulti;return n===void 0?r:n},$y=1,Ml=function(e){qg(n,e);var t=Yg(n);function n(r){var i;if(Xg(this,n),i=t.call(this,r),i.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},i.blockOptionHover=!1,i.isComposing=!1,i.commonProps=void 0,i.initialTouchX=0,i.initialTouchY=0,i.openAfterFocus=!1,i.scrollToFocusedOptionOnUpdate=!1,i.userIsDragging=void 0,i.isAppleDevice=Iy(),i.controlRef=null,i.getControlRef=function(l){i.controlRef=l},i.focusedOptionRef=null,i.getFocusedOptionRef=function(l){i.focusedOptionRef=l},i.menuListRef=null,i.getMenuListRef=function(l){i.menuListRef=l},i.inputRef=null,i.getInputRef=function(l){i.inputRef=l},i.focus=i.focusInput,i.blur=i.blurInput,i.onChange=function(l,u){var c=i.props,d=c.onChange,g=c.name;u.name=g,i.ariaOnChange(l,u),d(l,u)},i.setValue=function(l,u,c){var d=i.props,g=d.closeMenuOnSelect,h=d.isMulti,p=d.inputValue;i.onInputChange("",{action:"set-value",prevInputValue:p}),g&&(i.setState({inputIsHiddenAfterUpdate:!h}),i.onMenuClose()),i.setState({clearFocusValueOnUpdate:!0}),i.onChange(l,{action:u,option:c})},i.selectOption=function(l){var u=i.props,c=u.blurInputOnSelect,d=u.isMulti,g=u.name,h=i.state.selectValue,p=d&&i.isOptionSelected(l,h),m=i.isOptionDisabled(l,h);if(p){var y=i.getOptionValue(l);i.setValue(h.filter(function(b){return i.getOptionValue(b)!==y}),"deselect-option",l)}else if(!m)d?i.setValue([].concat(xi(h),[l]),"select-option",l):i.setValue(l,"select-option");else{i.ariaOnChange(l,{action:"select-option",option:l,name:g});return}c&&i.blurInput()},i.removeValue=function(l){var u=i.props.isMulti,c=i.state.selectValue,d=i.getOptionValue(l),g=c.filter(function(p){return i.getOptionValue(p)!==d}),h=io(u,g,g[0]||null);i.onChange(h,{action:"remove-value",removedValue:l}),i.focusInput()},i.clearValue=function(){var l=i.state.selectValue;i.onChange(io(i.props.isMulti,[],null),{action:"clear",removedValues:l})},i.popValue=function(){var l=i.props.isMulti,u=i.state.selectValue,c=u[u.length-1],d=u.slice(0,u.length-1),g=io(l,d,d[0]||null);c&&i.onChange(g,{action:"pop-value",removedValue:c})},i.getFocusedOptionId=function(l){return ks(i.state.focusableOptionsWithIds,l)},i.getFocusableOptionsWithIds=function(){return Tc(oa(i.props,i.state.selectValue),i.getElementId("option"))},i.getValue=function(){return i.state.selectValue},i.cx=function(){for(var l=arguments.length,u=new Array(l),c=0;c<l;c++)u[c]=arguments[c];return O1.apply(void 0,[i.props.classNamePrefix].concat(u))},i.getOptionLabel=function(l){return Bf(i.props,l)},i.getOptionValue=function(l){return Sa(i.props,l)},i.getStyles=function(l,u){var c=i.props.unstyled,d=Dy[l](u,c);d.boxSizing="border-box";var g=i.props.styles[l];return g?g(d,u):d},i.getClassNames=function(l,u){var c,d;return(c=(d=i.props.classNames)[l])===null||c===void 0?void 0:c.call(d,u)},i.getElementId=function(l){return"".concat(i.state.instancePrefix,"-").concat(l)},i.getComponents=function(){return ey(i.props)},i.buildCategorizedOptions=function(){return oa(i.props,i.state.selectValue)},i.getCategorizedOptions=function(){return i.props.menuIsOpen?i.buildCategorizedOptions():[]},i.buildFocusableOptions=function(){return Nf(i.buildCategorizedOptions())},i.getFocusableOptions=function(){return i.props.menuIsOpen?i.buildFocusableOptions():[]},i.ariaOnChange=function(l,u){i.setState({ariaSelection:Oe({value:l},u)})},i.onMenuMouseDown=function(l){l.button===0&&(l.stopPropagation(),l.preventDefault(),i.focusInput())},i.onMenuMouseMove=function(l){i.blockOptionHover=!1},i.onControlMouseDown=function(l){if(!l.defaultPrevented){var u=i.props.openMenuOnClick;i.state.isFocused?i.props.menuIsOpen?l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&i.onMenuClose():u&&i.openMenu("first"):(u&&(i.openAfterFocus=!0),i.focusInput()),l.target.tagName!=="INPUT"&&l.target.tagName!=="TEXTAREA"&&l.preventDefault()}},i.onDropdownIndicatorMouseDown=function(l){if(!(l&&l.type==="mousedown"&&l.button!==0)&&!i.props.isDisabled){var u=i.props,c=u.isMulti,d=u.menuIsOpen;i.focusInput(),d?(i.setState({inputIsHiddenAfterUpdate:!c}),i.onMenuClose()):i.openMenu("first"),l.preventDefault()}},i.onClearIndicatorMouseDown=function(l){l&&l.type==="mousedown"&&l.button!==0||(i.clearValue(),l.preventDefault(),i.openAfterFocus=!1,l.type==="touchend"?i.focusInput():setTimeout(function(){return i.focusInput()}))},i.onScroll=function(l){typeof i.props.closeMenuOnScroll=="boolean"?l.target instanceof HTMLElement&&Ea(l.target)&&i.props.onMenuClose():typeof i.props.closeMenuOnScroll=="function"&&i.props.closeMenuOnScroll(l)&&i.props.onMenuClose()},i.onCompositionStart=function(){i.isComposing=!0},i.onCompositionEnd=function(){i.isComposing=!1},i.onTouchStart=function(l){var u=l.touches,c=u&&u.item(0);c&&(i.initialTouchX=c.clientX,i.initialTouchY=c.clientY,i.userIsDragging=!1)},i.onTouchMove=function(l){var u=l.touches,c=u&&u.item(0);if(c){var d=Math.abs(c.clientX-i.initialTouchX),g=Math.abs(c.clientY-i.initialTouchY),h=5;i.userIsDragging=d>h||g>h}},i.onTouchEnd=function(l){i.userIsDragging||(i.controlRef&&!i.controlRef.contains(l.target)&&i.menuListRef&&!i.menuListRef.contains(l.target)&&i.blurInput(),i.initialTouchX=0,i.initialTouchY=0)},i.onControlTouchEnd=function(l){i.userIsDragging||i.onControlMouseDown(l)},i.onClearIndicatorTouchEnd=function(l){i.userIsDragging||i.onClearIndicatorMouseDown(l)},i.onDropdownIndicatorTouchEnd=function(l){i.userIsDragging||i.onDropdownIndicatorMouseDown(l)},i.handleInputChange=function(l){var u=i.props.inputValue,c=l.currentTarget.value;i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange(c,{action:"input-change",prevInputValue:u}),i.props.menuIsOpen||i.onMenuOpen()},i.onInputFocus=function(l){i.props.onFocus&&i.props.onFocus(l),i.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(i.openAfterFocus||i.props.openMenuOnFocus)&&i.openMenu("first"),i.openAfterFocus=!1},i.onInputBlur=function(l){var u=i.props.inputValue;if(i.menuListRef&&i.menuListRef.contains(document.activeElement)){i.inputRef.focus();return}i.props.onBlur&&i.props.onBlur(l),i.onInputChange("",{action:"input-blur",prevInputValue:u}),i.onMenuClose(),i.setState({focusedValue:null,isFocused:!1})},i.onOptionHover=function(l){if(!(i.blockOptionHover||i.state.focusedOption===l)){var u=i.getFocusableOptions(),c=u.indexOf(l);i.setState({focusedOption:l,focusedOptionId:c>-1?i.getFocusedOptionId(l):null})}},i.shouldHideSelectedOptions=function(){return Gf(i.props)},i.onValueInputFocus=function(l){l.preventDefault(),l.stopPropagation(),i.focus()},i.onKeyDown=function(l){var u=i.props,c=u.isMulti,d=u.backspaceRemovesValue,g=u.escapeClearsValue,h=u.inputValue,p=u.isClearable,m=u.isDisabled,y=u.menuIsOpen,b=u.onKeyDown,v=u.tabSelectsValue,C=u.openMenuOnFocus,E=i.state,I=E.focusedOption,R=E.focusedValue,P=E.selectValue;if(!m&&!(typeof b=="function"&&(b(l),l.defaultPrevented))){switch(i.blockOptionHover=!0,l.key){case"ArrowLeft":if(!c||h)return;i.focusValue("previous");break;case"ArrowRight":if(!c||h)return;i.focusValue("next");break;case"Delete":case"Backspace":if(h)return;if(R)i.removeValue(R);else{if(!d)return;c?i.popValue():p&&i.clearValue()}break;case"Tab":if(i.isComposing||l.shiftKey||!y||!v||!I||C&&i.isOptionSelected(I,P))return;i.selectOption(I);break;case"Enter":if(l.keyCode===229)break;if(y){if(!I||i.isComposing)return;i.selectOption(I);break}return;case"Escape":y?(i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange("",{action:"menu-close",prevInputValue:h}),i.onMenuClose()):p&&g&&i.clearValue();break;case" ":if(h)return;if(!y){i.openMenu("first");break}if(!I)return;i.selectOption(I);break;case"ArrowUp":y?i.focusOption("up"):i.openMenu("last");break;case"ArrowDown":y?i.focusOption("down"):i.openMenu("first");break;case"PageUp":if(!y)return;i.focusOption("pageup");break;case"PageDown":if(!y)return;i.focusOption("pagedown");break;case"Home":if(!y)return;i.focusOption("first");break;case"End":if(!y)return;i.focusOption("last");break;default:return}l.preventDefault()}},i.state.instancePrefix="react-select-"+(i.props.instanceId||++$y),i.state.selectValue=ya(r.value),r.menuIsOpen&&i.state.selectValue.length){var o=i.getFocusableOptionsWithIds(),s=i.buildFocusableOptions(),a=s.indexOf(i.state.selectValue[0]);i.state.focusableOptionsWithIds=o,i.state.focusedOption=s[a],i.state.focusedOptionId=ks(o,s[a])}return i}return Gg(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&pc(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(i){var o=this.props,s=o.isDisabled,a=o.menuIsOpen,l=this.state.isFocused;(l&&!s&&i.isDisabled||l&&a&&!i.menuIsOpen)&&this.focusInput(),l&&s&&!i.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!l&&!s&&i.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(pc(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(i,o){this.props.onInputChange(i,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(i){var o=this,s=this.state,a=s.selectValue,l=s.isFocused,u=this.buildFocusableOptions(),c=i==="first"?0:u.length-1;if(!this.props.isMulti){var d=u.indexOf(a[0]);d>-1&&(c=d)}this.scrollToFocusedOptionOnUpdate=!(l&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(i){var o=this.state,s=o.selectValue,a=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var l=s.indexOf(a);a||(l=-1);var u=s.length-1,c=-1;if(s.length){switch(i){case"previous":l===0?c=0:l===-1?c=u:c=l-1;break;case"next":l>-1&&l<u&&(c=l+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:s[c]})}}}},{key:"focusOption",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,s=this.state.focusedOption,a=this.getFocusableOptions();if(a.length){var l=0,u=a.indexOf(s);s||(u=-1),i==="up"?l=u>0?u-1:a.length-1:i==="down"?l=(u+1)%a.length:i==="pageup"?(l=u-o,l<0&&(l=0)):i==="pagedown"?(l=u+o,l>a.length-1&&(l=a.length-1)):i==="last"&&(l=a.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:a[l],focusedValue:null,focusedOptionId:this.getFocusedOptionId(a[l])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(xs):Oe(Oe({},xs),this.props.theme):xs}},{key:"getCommonProps",value:function(){var i=this.clearValue,o=this.cx,s=this.getStyles,a=this.getClassNames,l=this.getValue,u=this.selectOption,c=this.setValue,d=this.props,g=d.isMulti,h=d.isRtl,p=d.options,m=this.hasValue();return{clearValue:i,cx:o,getStyles:s,getClassNames:a,getValue:l,hasValue:m,isMulti:g,isRtl:h,options:p,selectOption:u,selectProps:d,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var i=this.state.selectValue;return i.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var i=this.props,o=i.isClearable,s=i.isMulti;return o===void 0?s:o}},{key:"isOptionDisabled",value:function(i,o){return Wf(this.props,i,o)}},{key:"isOptionSelected",value:function(i,o){return Uf(this.props,i,o)}},{key:"filterOption",value:function(i,o){return qf(this.props,i,o)}},{key:"formatOptionLabel",value:function(i,o){if(typeof this.props.formatOptionLabel=="function"){var s=this.props.inputValue,a=this.state.selectValue;return this.props.formatOptionLabel(i,{context:o,inputValue:s,selectValue:a})}else return this.getOptionLabel(i)}},{key:"formatGroupLabel",value:function(i){return this.props.formatGroupLabel(i)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var i=this.props,o=i.isDisabled,s=i.isSearchable,a=i.inputId,l=i.inputValue,u=i.tabIndex,c=i.form,d=i.menuIsOpen,g=i.required,h=this.getComponents(),p=h.Input,m=this.state,y=m.inputIsHidden,b=m.ariaSelection,v=this.commonProps,C=a||this.getElementId("input"),E=Oe(Oe(Oe({"aria-autocomplete":"list","aria-expanded":d,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":g,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},d&&{"aria-controls":this.getElementId("listbox")}),!s&&{"aria-readonly":!0}),this.hasValue()?b?.action==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return s?f.createElement(p,Ae({},v,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:C,innerRef:this.getInputRef,isDisabled:o,isHidden:y,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:l},E)):f.createElement(gy,Ae({id:C,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:wa,onFocus:this.onInputFocus,disabled:o,tabIndex:u,inputMode:"none",form:c,value:""},E))}},{key:"renderPlaceholderOrValue",value:function(){var i=this,o=this.getComponents(),s=o.MultiValue,a=o.MultiValueContainer,l=o.MultiValueLabel,u=o.MultiValueRemove,c=o.SingleValue,d=o.Placeholder,g=this.commonProps,h=this.props,p=h.controlShouldRenderValue,m=h.isDisabled,y=h.isMulti,b=h.inputValue,v=h.placeholder,C=this.state,E=C.selectValue,I=C.focusedValue,R=C.isFocused;if(!this.hasValue()||!p)return b?null:f.createElement(d,Ae({},g,{key:"placeholder",isDisabled:m,isFocused:R,innerProps:{id:this.getElementId("placeholder")}}),v);if(y)return E.map(function(x,S){var F=x===I,D="".concat(i.getOptionLabel(x),"-").concat(i.getOptionValue(x));return f.createElement(s,Ae({},g,{components:{Container:a,Label:l,Remove:u},isFocused:F,isDisabled:m,key:D,index:S,removeProps:{onClick:function(){return i.removeValue(x)},onTouchEnd:function(){return i.removeValue(x)},onMouseDown:function(T){T.preventDefault()}},data:x}),i.formatOptionLabel(x,"value"))});if(b)return null;var P=E[0];return f.createElement(c,Ae({},g,{data:P,isDisabled:m}),this.formatOptionLabel(P,"value"))}},{key:"renderClearIndicator",value:function(){var i=this.getComponents(),o=i.ClearIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||l||!this.hasValue()||u)return null;var d={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,Ae({},s,{innerProps:d,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var i=this.getComponents(),o=i.LoadingIndicator,s=this.commonProps,a=this.props,l=a.isDisabled,u=a.isLoading,c=this.state.isFocused;if(!o||!u)return null;var d={"aria-hidden":"true"};return f.createElement(o,Ae({},s,{innerProps:d,isDisabled:l,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator,s=i.IndicatorSeparator;if(!o||!s)return null;var a=this.commonProps,l=this.props.isDisabled,u=this.state.isFocused;return f.createElement(s,Ae({},a,{isDisabled:l,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator;if(!o)return null;var s=this.commonProps,a=this.props.isDisabled,l=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,Ae({},s,{innerProps:u,isDisabled:a,isFocused:l}))}},{key:"renderMenu",value:function(){var i=this,o=this.getComponents(),s=o.Group,a=o.GroupHeading,l=o.Menu,u=o.MenuList,c=o.MenuPortal,d=o.LoadingMessage,g=o.NoOptionsMessage,h=o.Option,p=this.commonProps,m=this.state.focusedOption,y=this.props,b=y.captureMenuScroll,v=y.inputValue,C=y.isLoading,E=y.loadingMessage,I=y.minMenuHeight,R=y.maxMenuHeight,P=y.menuIsOpen,x=y.menuPlacement,S=y.menuPosition,F=y.menuPortalTarget,D=y.menuShouldBlockScroll,M=y.menuShouldScrollIntoView,T=y.noOptionsMessage,O=y.onMenuScrollToTop,k=y.onMenuScrollToBottom;if(!P)return null;var $=function(ae,ge){var de=ae.type,re=ae.data,H=ae.isDisabled,L=ae.isSelected,X=ae.label,te=ae.value,ie=m===re,Se=H?void 0:function(){return i.onOptionHover(re)},me=H?void 0:function(){return i.selectOption(re)},nt="".concat(i.getElementId("option"),"-").concat(ge),xe={id:nt,onClick:me,onMouseMove:Se,onMouseOver:Se,tabIndex:-1,role:"option","aria-selected":i.isAppleDevice?void 0:L};return f.createElement(h,Ae({},p,{innerProps:xe,data:re,isDisabled:H,isSelected:L,key:nt,label:X,type:de,value:te,isFocused:ie,innerRef:ie?i.getFocusedOptionRef:void 0}),i.formatOptionLabel(ae.data,"menu"))},G;if(this.hasOptions())G=this.getCategorizedOptions().map(function(ce){if(ce.type==="group"){var ae=ce.data,ge=ce.options,de=ce.index,re="".concat(i.getElementId("group"),"-").concat(de),H="".concat(re,"-heading");return f.createElement(s,Ae({},p,{key:re,data:ae,options:ge,Heading:a,headingProps:{id:H,data:ce.data},label:i.formatGroupLabel(ce.data)}),ce.options.map(function(L){return $(L,"".concat(de,"-").concat(L.index))}))}else if(ce.type==="option")return $(ce,"".concat(ce.index))});else if(C){var j=E({inputValue:v});if(j===null)return null;G=f.createElement(d,p,j)}else{var ue=T({inputValue:v});if(ue===null)return null;G=f.createElement(g,p,ue)}var Q={minMenuHeight:I,maxMenuHeight:R,menuPlacement:x,menuPosition:S,menuShouldScrollIntoView:M},ee=f.createElement(X1,Ae({},p,Q),function(ce){var ae=ce.ref,ge=ce.placerProps,de=ge.placement,re=ge.maxHeight;return f.createElement(l,Ae({},p,Q,{innerRef:ae,innerProps:{onMouseDown:i.onMenuMouseDown,onMouseMove:i.onMenuMouseMove},isLoading:C,placement:de}),f.createElement(yy,{captureEnabled:b,onTopArrive:O,onBottomArrive:k,lockEnabled:D},function(H){return f.createElement(u,Ae({},p,{innerRef:function(X){i.getMenuListRef(X),H(X)},innerProps:{role:"listbox","aria-multiselectable":p.isMulti,id:i.getElementId("listbox")},isLoading:C,maxHeight:re,focusedOption:m}),G)}))});return F||S==="fixed"?f.createElement(c,Ae({},p,{appendTo:F,controlElement:this.controlRef,menuPlacement:x,menuPosition:S}),ee):ee}},{key:"renderFormField",value:function(){var i=this,o=this.props,s=o.delimiter,a=o.isDisabled,l=o.isMulti,u=o.name,c=o.required,d=this.state.selectValue;if(c&&!this.hasValue()&&!a)return f.createElement(xy,{name:u,onFocus:this.onValueInputFocus});if(!(!u||a))if(l)if(s){var g=d.map(function(m){return i.getOptionValue(m)}).join(s);return f.createElement("input",{name:u,type:"hidden",value:g})}else{var h=d.length>0?d.map(function(m,y){return f.createElement("input",{key:"i-".concat(y),name:u,type:"hidden",value:i.getOptionValue(m)})}):f.createElement("input",{name:u,type:"hidden",value:""});return f.createElement("div",null,h)}else{var p=d[0]?this.getOptionValue(d[0]):"";return f.createElement("input",{name:u,type:"hidden",value:p})}}},{key:"renderLiveRegion",value:function(){var i=this.commonProps,o=this.state,s=o.ariaSelection,a=o.focusedOption,l=o.focusedValue,u=o.isFocused,c=o.selectValue,d=this.getFocusableOptions();return f.createElement(ly,Ae({},i,{id:this.getElementId("live-region"),ariaSelection:s,focusedOption:a,focusedValue:l,isFocused:u,selectValue:c,focusableOptions:d,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var i=this.getComponents(),o=i.Control,s=i.IndicatorsContainer,a=i.SelectContainer,l=i.ValueContainer,u=this.props,c=u.className,d=u.id,g=u.isDisabled,h=u.menuIsOpen,p=this.state.isFocused,m=this.commonProps=this.getCommonProps();return f.createElement(a,Ae({},m,{className:c,innerProps:{id:d,onKeyDown:this.onKeyDown},isDisabled:g,isFocused:p}),this.renderLiveRegion(),f.createElement(o,Ae({},m,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:g,isFocused:p,menuIsOpen:h}),f.createElement(l,Ae({},m,{isDisabled:g}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(s,Ae({},m,{isDisabled:g}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(i,o){var s=o.prevProps,a=o.clearFocusValueOnUpdate,l=o.inputIsHiddenAfterUpdate,u=o.ariaSelection,c=o.isFocused,d=o.prevWasFocused,g=o.instancePrefix,h=i.options,p=i.value,m=i.menuIsOpen,y=i.inputValue,b=i.isMulti,v=ya(p),C={};if(s&&(p!==s.value||h!==s.options||m!==s.menuIsOpen||y!==s.inputValue)){var E=m?Hy(i,v):[],I=m?Tc(oa(i,v),"".concat(g,"-option")):[],R=a?zy(o,v):null,P=Vy(o,E),x=ks(I,P);C={selectValue:v,focusedOption:P,focusedOptionId:x,focusableOptionsWithIds:I,focusedValue:R,clearFocusValueOnUpdate:!1}}var S=l!=null&&i!==s?{inputIsHidden:l,inputIsHiddenAfterUpdate:void 0}:{},F=u,D=c&&d;return c&&!D&&(F={value:io(b,v,v[0]||null),options:v,action:"initial-input-focus"},D=!d),u?.action==="initial-input-focus"&&(F=null),Oe(Oe(Oe({},C),S),{},{prevProps:i,ariaSelection:F,prevWasFocused:D})}}]),n}(f.Component);Ml.defaultProps=Ay;var Ny=f.forwardRef(function(e,t){var n=bf(e);return f.createElement(Ml,Ae({ref:t},n))}),Xf=Ny;const By=e=>{const{Menu:t}=xl,{children:n,...r}=e;return f.createElement(t,{...r},n)},Wy=sn("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),Uy=sn("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),qy=sn("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),Gy=e=>{const{value:t,onFinishedEditing:n,initialValue:r,portalElementRef:i}=e,{allowedValues:o,value:s}=t.data,[a,l]=f.useState(s),[u,c]=f.useState(r??""),d=Fd(),g=f.useMemo(()=>o.map(h=>typeof h=="string"||h===null||h===void 0?{value:h,label:h?.toString()??""}:h),[o]);return t.readonly?f.createElement(qy,null,f.createElement(Jr,{highlight:!0,autoFocus:!1,disabled:!0,value:a??"",onChange:()=>{}})):f.createElement(Wy,null,f.createElement(Xf,{className:"glide-select",inputValue:u,onInputChange:c,menuPlacement:"auto",value:g.find(h=>h.value===a),styles:{control:h=>({...h,border:0,boxShadow:"none"}),option:(h,{isFocused:p})=>({...h,fontSize:d.editorFontSize,fontFamily:d.fontFamily,cursor:p?"pointer":void 0,paddingLeft:d.cellHorizontalPadding,paddingRight:d.cellHorizontalPadding,":active":{...h[":active"],color:d.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:h=>({...h,colors:{...h.colors,neutral0:d.bgCell,neutral5:d.bgCell,neutral10:d.bgCell,neutral20:d.bgCellMedium,neutral30:d.bgCellMedium,neutral40:d.bgCellMedium,neutral50:d.textLight,neutral60:d.textMedium,neutral70:d.textMedium,neutral80:d.textDark,neutral90:d.textDark,neutral100:d.textDark,primary:d.accentColor,primary75:d.accentColor,primary50:d.accentColor,primary25:d.accentLight}}),menuPortalTarget:i?.current??document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:h=>f.createElement(Uy,null,f.createElement(By,{className:"click-outside-ignore",...h}))},options:g,onChange:async h=>{h!==null&&(l(h.value),await new Promise(p=>window.requestAnimationFrame(p)),n({...t,data:{...t.data,value:h.value}}))}}))},Xy={kind:Z.Custom,isMatch:e=>e.data.kind==="dropdown-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:o}=t.data,s=t.data.allowedValues.find(l=>typeof l=="string"||l===null||l===void 0?l===o:l.value===o),a=typeof s=="string"?s:s?.label??"";return a&&(n.fillStyle=r.textDark,n.fillText(a,i.x+r.cellHorizontalPadding,i.y+i.height/2+rr(n,r))),!0},measure:(e,t,n)=>{const{value:r}=t.data;return(r?e.measureText(r).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Gy,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.some(n=>n==null?!1:typeof n=="string"?n===e:n.value===e)?e:t.value})};function Yy(e,t){const n=/(\d+\.?\d*)\s*(px|rem|em|%|pt)/,r=e.match(n);if(r){const i=parseFloat(r[1]),o=r[2],s=i*t;return e.replace(n,`${Number(s.toPrecision(3))}${o}`)}return e}const jy={marginRight:8},Ky={display:"flex",alignItems:"center",flexGrow:1},Zy={kind:Z.Custom,isMatch:e=>e.data.kind==="range-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:o,max:s,value:a,label:l,measureLabel:u}=t.data,c=i.x+r.cellHorizontalPadding,d=i.y+i.height/2,g=s-o,h=(a-o)/g,p=`${Yy(r.baseFontStyle,.9)} ${r.fontFamily}`,y=el(n,p)/2;n.save();let b=0;l!==void 0&&(n.font=p,b=Fr(u??l,n,p).width+r.cellHorizontalPadding);const v=i.width-r.cellHorizontalPadding*2-b;if(v>=y){const C=n.createLinearGradient(c,d,c+v,d);C.addColorStop(0,r.accentColor),C.addColorStop(h,r.accentColor),C.addColorStop(h,r.bgBubble),C.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=C,cc(n,c,d-y/2,v,y,y/2),n.fill(),n.beginPath(),cc(n,c+.5,d-y/2+.5,v-1,y-1,(y-1)/2),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return l!==void 0&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(l,i.x+i.width-r.cellHorizontalPadding,d+rr(n,p))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,r=t.value.toString(),i=t.min.toString(),o=t.max.toString(),s=t.step.toString(),a=l=>{e.onChange({...e.value,data:{...t,value:Number(l.target.value)}})};return f.createElement("label",{style:Ky},f.createElement("input",{style:jy,type:"range",value:r,min:i,max:o,step:s,onChange:a,disabled:n}),r)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},Jy=sn("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),Ms=(e,t,n)=>{if(t==null)return"";n&&(t=new Date(t.getTime()+n));const r=t.toISOString();switch(e){case"date":return r.split("T")[0];case"datetime-local":return r.replace("Z","");case"time":return r.split("T")[1].replace("Z","");default:throw new Error(`Unknown date kind ${e}`)}},Qy=e=>{const t=e.value.data,{format:n,displayDate:r}=t,i=t.step!==void 0&&!Number.isNaN(Number(t.step))?Number(t.step):void 0,o=t.timezoneOffset?t.timezoneOffset*60*1e3:0,s=t.min instanceof Date?Ms(n,t.min,o):t.min,a=t.max instanceof Date?Ms(n,t.max,o):t.max,l=Ms(n,t.date,o);return e.value.readonly?$t.createElement(Jr,{highlight:!0,autoFocus:!1,disabled:!0,value:r??"",onChange:()=>{}}):$t.createElement(Jy,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:l,min:s,max:a,step:i,autoFocus:!0,onChange:u=>{isNaN(u.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(u.target.valueAsNumber-o)}})}})},eC={kind:Z.Custom,isMatch:e=>e.data.kind==="date-picker-cell",draw:(e,t)=>{const{displayDate:n}=t.data;return Qs(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:r}=t.data;return e.measureText(r).width+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Qy}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),t.format==="time"&&Number.isNaN(n)&&(n=Date.parse(`1970-01-01T${e}Z`)))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}};var tC=["allowCreateWhileLoading","createOptionPosition","formatCreateLabel","isValidNewOption","getNewOptionData","onCreateOption","options","onChange"],Oc=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,i=String(t).toLowerCase(),o=String(r.getOptionValue(n)).toLowerCase(),s=String(r.getOptionLabel(n)).toLowerCase();return o===i||s===i},Rs={formatCreateLabel:function(t){return'Create "'.concat(t,'"')},isValidNewOption:function(t,n,r,i){return!(!t||n.some(function(o){return Oc(t,o,i)})||r.some(function(o){return Oc(t,o,i)}))},getNewOptionData:function(t,n){return{label:n,value:t,__isNew__:!0}}};function nC(e){var t=e.allowCreateWhileLoading,n=t===void 0?!1:t,r=e.createOptionPosition,i=r===void 0?"last":r,o=e.formatCreateLabel,s=o===void 0?Rs.formatCreateLabel:o,a=e.isValidNewOption,l=a===void 0?Rs.isValidNewOption:a,u=e.getNewOptionData,c=u===void 0?Rs.getNewOptionData:u,d=e.onCreateOption,g=e.options,h=g===void 0?[]:g,p=e.onChange,m=ur(e,tC),y=m.getOptionValue,b=y===void 0?Vf:y,v=m.getOptionLabel,C=v===void 0?zf:v,E=m.inputValue,I=m.isLoading,R=m.isMulti,P=m.value,x=m.name,S=f.useMemo(function(){return l(E,ya(P),h,{getOptionValue:b,getOptionLabel:C})?c(E,s(E)):void 0},[s,c,C,b,E,l,h,P]),F=f.useMemo(function(){return(n||!I)&&S?i==="first"?[S].concat(xi(h)):[].concat(xi(h),[S]):h},[n,i,I,S,h]),D=f.useCallback(function(M,T){if(T.action!=="select-option")return p(M,T);var O=Array.isArray(M)?M:[M];if(O[O.length-1]===S){if(d)d(E);else{var k=c(E,E),$={action:"create-option",name:x,option:k};p(io(R,[].concat(xi(ya(P)),[k]),k),$)}return}p(M,T)},[c,E,R,x,S,d,p,P]);return Oe(Oe({},m),{},{options:F,onChange:D})}var rC=f.forwardRef(function(e,t){var n=bf(e),r=nC(n);return f.createElement(Ml,Ae({ref:t},r))}),iC=rC;const Rl="__value",oC=new RegExp(`^${Rl}\\d+__`),aC=sn("div")({name:"Wrap",class:"gdg-w1i61rz",propsAsIs:!1}),sC=sn("div")({name:"PortalWrap",class:"gdg-phbadu4",propsAsIs:!1}),aa=e=>e.map(t=>typeof t=="string"||t===null||t===void 0?{value:t,label:t??"",color:void 0}:{value:t.value,label:t.label??t.value??"",color:t.color??void 0}),Yf=(e,t,n)=>e==null?[]:e.map((r,i)=>{const o=n?`${Rl}${i}__`:"",s=t.find(a=>a.value===r);return s?{...s,value:`${o}${s.value}`}:{value:`${o}${r}`,label:r}}),lC=e=>{const{Menu:t}=xl,{children:n,...r}=e;return f.createElement(t,{...r},n)},uC=e=>{const{value:t,initialValue:n,onChange:r,onFinishedEditing:i,portalElementRef:o}=e,{options:s,values:a,allowCreation:l,allowDuplicates:u}=t.data,c=Fd(),[d,g]=f.useState(a),[h,p]=f.useState(!0),[m,y]=f.useState(n??""),b=f.useMemo(()=>aa(s??[]),[s]),v=l&&u&&b.length===0,C=f.useCallback(x=>{h&&x.stopPropagation()},[h]),E={control:(x,S)=>({...x,border:0,boxShadow:"none",backgroundColor:c.bgCell,pointerEvents:S.isDisabled?"auto":x.pointerEvents,cursor:S.isDisabled?"default":x.cursor}),valueContainer:x=>({...x,flexWrap:x.flexWrap??"wrap",overflowX:"auto",overflowY:"hidden"}),menu:x=>({...x,backgroundColor:c.bgCell}),option:(x,S)=>({...x,fontSize:c.editorFontSize,fontFamily:c.fontFamily,color:c.textDark,...S.isFocused?{backgroundColor:c.accentLight,cursor:"pointer"}:{},":active":{...x[":active"],color:c.accentFg,backgroundColor:c.accentColor}}),input:(x,{isDisabled:S})=>S?{display:"none"}:{...x,fontSize:c.editorFontSize,fontFamily:c.fontFamily,color:c.textDark},placeholder:x=>({...x,fontSize:c.editorFontSize,fontFamily:c.fontFamily,color:c.textLight}),noOptionsMessage:x=>({...x,fontSize:c.editorFontSize,fontFamily:c.fontFamily,color:c.textLight}),clearIndicator:x=>({...x,color:c.textLight,":hover":{color:c.textDark,cursor:"pointer"}}),multiValue:(x,{data:S})=>({...x,backgroundColor:S.color??c.bgBubble,borderRadius:`${c.roundingRadius??c.bubbleHeight/2}px`,flexShrink:0,whiteSpace:"nowrap"}),multiValueLabel:(x,{data:S,isDisabled:F})=>({...x,paddingRight:F?c.bubblePadding:0,paddingLeft:c.bubblePadding,paddingTop:0,paddingBottom:0,color:S.color?Os(S.color)>.5?"black":"white":c.textBubble,fontSize:c.editorFontSize,fontFamily:c.fontFamily,justifyContent:"center",alignItems:"center",display:"flex",height:c.bubbleHeight,whiteSpace:"nowrap"}),multiValueRemove:(x,{data:S,isDisabled:F,isFocused:D})=>F?{display:"none"}:{...x,color:S.color?Os(S.color)>.5?"black":"white":c.textBubble,backgroundColor:void 0,borderRadius:D?`${c.roundingRadius??c.bubbleHeight/2}px`:void 0,":hover":{cursor:"pointer"}}},I=f.useCallback(x=>{const S=x.map(F=>u&&F.startsWith(Rl)?F.replace(new RegExp(oC),""):F);g(S),r({...t,data:{...t.data,values:S}})},[t,r,u]),R=x=>{switch(x.key){case"Enter":case"Tab":if(!m){i(t,[0,1]);return}u&&l&&(y(""),I([...d??[],m]),p(!1),x.preventDefault())}},P=l?iC:Xf;return f.createElement(aC,{onKeyDown:C,"data-testid":"multi-select-cell"},f.createElement(P,{className:"gdg-multi-select",isMulti:!0,isDisabled:t.readonly,isClearable:!0,isSearchable:!0,inputValue:m,onInputChange:y,options:b,placeholder:t.readonly?"":l?"Add...":void 0,noOptionsMessage:x=>l&&u&&x.inputValue?`Create "${x.inputValue}"`:void 0,menuIsOpen:t.readonly?!1:h,onMenuOpen:()=>p(!0),onMenuClose:()=>p(!1),value:Yf(d,b,u),onKeyDown:t.readonly?void 0:R,menuPlacement:"auto",menuPortalTarget:o?.current??document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,openMenuOnClick:!0,closeMenuOnSelect:!0,backspaceRemovesValue:!0,escapeClearsValue:!1,styles:E,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:x=>v?null:f.createElement(sC,null,f.createElement(lC,{className:"click-outside-ignore",...x}))},onChange:async x=>{x!==null&&I(x.map(S=>S.value))}}))},cC={kind:Z.Custom,isMatch:e=>e.data.kind==="multi-select-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i,highlighted:o}=e,{values:s,options:a}=t.data;if(s==null)return!0;const l=aa(a??[]),u={x:i.x+r.cellHorizontalPadding,y:i.y+r.cellVerticalPadding,width:i.width-2*r.cellHorizontalPadding,height:i.height-2*r.cellVerticalPadding},c=Math.max(1,Math.floor(u.height/(r.bubbleHeight+r.bubblePadding)));let{x:d}=u,g=1,h=c===1?u.y+(u.height-r.bubbleHeight)/2:u.y+(u.height-c*r.bubbleHeight-(c-1)*r.bubblePadding)/2;for(const p of s){const m=l.find(I=>I.value===p),y=m?.color??(o?r.bgBubbleSelected:r.bgBubble),b=m?.label??p,C=Fr(b,n).width+r.bubblePadding*2,E=r.bubbleHeight/2;if(d!==u.x&&d+C>u.x+u.width&&g<c&&(g++,h+=r.bubbleHeight+r.bubblePadding,d=u.x),n.fillStyle=y,n.beginPath(),tr(n,d,h,C,r.bubbleHeight,r.roundingRadius??r.bubbleHeight/2),n.fill(),n.fillStyle=m?.color?Os(y)>.5?"#000000":"#ffffff":r.textBubble,n.fillText(b,d+r.bubblePadding,h+E+rr(n,r)),d+=C+r.bubbleMargin,d>u.x+u.width+r.cellHorizontalPadding&&g>=c)break}return!0},measure:(e,t,n)=>{const{values:r,options:i}=t.data;if(!r)return n.cellHorizontalPadding*2;const o=Yf(r,aa(i??[]),t.data.allowDuplicates).map(a=>a.label??a.value),s=o.reduce((a,l)=>e.measureText(l).width+a+n.bubblePadding*2+n.bubbleMargin,0);return o.length===0?n.cellHorizontalPadding*2:s+2*n.cellHorizontalPadding-n.bubbleMargin},provideEditor:()=>({editor:uC,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,values:[]}})}),onPaste:(e,t)=>{if(!e||!e.trim())return{...t,values:[]};let n=e.split(",").map(r=>r.trim());if(t.allowDuplicates||(n=n.filter((r,i)=>n.indexOf(r)===i)),!t.allowCreation){const r=aa(t.options??[]);n=n.filter(i=>r.find(o=>o.value===i))}if(n.length!==0)return{...t,values:n}}},dC="None";function Pc(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-n.cellHorizontalPadding,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+n.cellHorizontalPadding),e.fillStyle=n.accentColor,e.fill(),e.restore()}const fC=e=>{const{cell:t,theme:n,ctx:r}=e;Qs({...e,theme:{...n,textDark:n.textLight,headerFontFull:`${n.headerFontStyle} ${n.fontFamily}`,baseFontFull:`${n.baseFontStyle} ${n.fontFamily}`,markerFontFull:`${n.markerFontStyle} ${n.fontFamily}`}},dC,t.contentAlign),r.fillStyle=n.textDark};function hC(e){const t=f.useCallback((r,i)=>{const{cell:o,theme:s,ctx:a,rect:l}=r,u=r.col;if(ki(o))Pc(a,l,s);else if(sl(o)&&u<e.length){const c=e[u];["checkbox","line_chart","bar_chart","progress"].includes(c.kind)?i():fC(r),c.isRequired&&c.isEditable&&Pc(a,l,s);return}i()},[e]),n=f.useMemo(()=>[h1,Xy,Zy,eC,cC,...Vb],[]);return{drawCell:t,customRenderers:n}}function gC(){const e=Kr();return f.useMemo(()=>{const n={editable:i=>`<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="${i.bgColor}"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>`};return{glideTheme:{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:mi(e.colors.primary,.9),borderColor:e.colors.dataframeBorderColor,horizontalBorderColor:e.colors.dataframeBorderColor,fontFamily:e.genericFonts.bodyFont,bgSearchResult:mi(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.dataframeHeaderBackgroundColor,bgHeaderHasFocus:mi(e.colors.darkenedBgMix100,.9),bgHeaderHovered:mi(e.colors.darkenedBgMix100,.9),textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerIconSize:Math.round(rn("1.125rem")),headerFontStyle:`${e.fontWeights.normal} ${rn(e.fontSizes.sm)}px`,baseFontStyle:`${e.fontWeights.normal} ${rn(e.fontSizes.sm)}px`,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:mi(e.colors.bodyText,.2),textLight:e.colors.fadedText40,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:Math.round(rn(e.spacing.sm)),cellVerticalPadding:Math.round(rn("0.1875rem")),textBubble:e.colors.fadedText60,bgBubble:e.colors.secondaryBg,bgBubbleSelected:Mg(e.colors.secondaryBg,.1),bubbleHeight:Math.round(rn("1.25rem")),bubblePadding:Math.round(rn(e.spacing.sm)),bubbleMargin:Math.round(rn(e.spacing.twoXS)),linkColor:e.colors.link,drilldownBorder:e.colors.darkenedBgMix25,checkboxMaxSize:Math.round(rn(e.sizes.checkbox))},tableBorderRadius:e.radii.default,tableBorderWidth:1,defaultTableHeight:Math.round(rn("25rem")),minColumnWidth:Math.round(rn("3.125rem")),maxColumnWidth:Math.round(rn("62.5rem")),maxColumnAutoWidth:Math.round(rn("31.25rem")),defaultRowHeight:Math.round(rn("2.1875rem")),defaultHeaderHeight:Math.round(rn("2.1875rem")),bgRowHovered:Rg(e.colors.bgColor,e.colors.secondaryBg,.3),headerIcons:n}},[e])}const pC=qs.getLogger("useDataEditor");function mC(e,t,n,r,i,o,s,a,l){const u=f.useCallback(([m,y],b)=>{const v=e[m];if(!v.isEditable)return;const C=v.indexNumber,E=n.current.getOriginalRowIndex(i(y)),I=r([m,y]),R=v.getCellValue(I),P=v.getCellValue(b);if(!ki(I)&&P===R)return;const x=v.getCell(P,!0);ki(x)?pC.warn(`Not applying the cell edit since it causes this error:
 ${x.data}`):(n.current.setCell(C,E,{...x,lastUpdated:performance.now()}),a())},[e,n,i,r,a]),c=f.useCallback(()=>{if(t)return;const m=new Map;e.forEach(y=>{m.set(y.indexNumber,y.getCell(y.defaultValue))}),n.current.addRow(m),s()},[e,n,t,s]),d=f.useCallback(()=>{t||(c(),a())},[c,a,t]),g=f.useCallback(m=>{if(m.rows.length>0){if(t)return!0;const y=m.rows.toArray().map(b=>n.current.getOriginalRowIndex(i(b)));return n.current.deleteRows(y),s(),l(),a(),!1}if(m.current?.range){const y=[],b=m.current.range;for(let v=b.y;v<b.y+b.height;v++)for(let C=b.x;C<b.x+b.width;C++){const E=e[C];E.isEditable&&!E.isRequired&&(y.push({cell:[C,v]}),u([C,v],E.getCell(null)))}return y.length>0&&(a(),o(y)),!1}return!0},[e,n,t,o,i,a,u,l,s]),h=f.useCallback((m,y)=>{const[b,v]=m,C=[];for(let E=0;E<y.length;E++){const I=y[E];if(E+v>=n.current.getNumRows()){if(t)break;c()}for(let R=0;R<I.length;R++){const P=I[R],x=E+v,S=R+b;if(S>=e.length)break;const F=e[S];if(F.isEditable){const D=F.getCell(P,!0);if(lt(D)&&!ki(D)){const M=F.indexNumber,T=n.current.getOriginalRowIndex(i(x)),O=F.getCellValue(r([S,x]));F.getCellValue(D)!==O&&(n.current.setCell(M,T,{...D,lastUpdated:performance.now()}),C.push({cell:[S,x]}))}}}C.length>0&&(a(),o(C))}return!1},[e,n,t,i,r,c,a,o]),p=f.useCallback((m,y)=>{const b=m[0];if(b>=e.length)return!0;const v=e[b];if(v.validateInput){const C=v.validateInput(v.getCellValue(y));return C===!0||C===!1?C:v.getCell(C)}return!0},[e]);return{onCellEdited:u,onPaste:h,onRowAppended:d,onDelete:g,validateCell:p}}const jf=",",oo='"',vC='"',Kf=`
`,bC="\uFEFF",wC=new RegExp(`[${[jf,oo,Kf].join("")}]`),Lc=qs.getLogger("useDataExporter");function Fc(e){return e.map(t=>yC(t)).join(jf)+Kf}function yC(e){if(ze(e))return"";const t=dt(e);return wC.test(t)?`${oo}${t.replace(new RegExp(oo,"g"),vC+oo)}${oo}`:t}async function _c(e,t,n,r){const i=new TextEncoder;await e.write(i.encode(bC));const o=n.map(s=>s.name);await e.write(i.encode(Fc(o)));for(let s=0;s<r;s++){const a=[];n.forEach((l,u,c)=>{a.push(l.getCellValue(t([u,s])))}),await e.write(i.encode(Fc(a)))}await e.close()}function CC(e,t,n,r){return{exportToCsv:f.useCallback(async()=>{const s=`${new Date().toISOString().slice(0,16).replace(":","-")}_export.csv`;try{const u=await(await(await Us(()=>import("./es6.B8zRNPZ-.js").then(c=>c.a),__vite__mapDeps([14,1,2]),import.meta.url)).showSaveFilePicker({suggestedName:s,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1})).createWritable();await _c(u,e,t,n)}catch(a){if(a instanceof Error&&a.name==="AbortError")return;try{Lc.warn("Failed to export data as CSV with FileSystem API, trying fallback method",a);let l="";const u=new WritableStream({write:h=>{l+=new TextDecoder("utf-8").decode(h)},close:async()=>{}});await _c(u.getWriter(),e,t,n);const c=new Blob([l],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(c),g=Wg({enforceDownloadInNewTab:r,url:d,filename:s});g.style.display="none",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(d)}catch(l){Lc.error("Failed to export data as CSV",l)}}},[t,n,e,r])}}function SC(e,t,n,r){return{getCellContent:f.useCallback(([o,s])=>{if(o>t.length-1)return Dt("Column index out of bounds","This error should never happen. Please report this bug.");if(s>n-1)return Dt("Row index out of bounds","This error should never happen. Please report this bug.");try{const a=t[o],l=a.indexNumber,u=r.current.getOriginalRowIndex(s),c=r.current.isAddedRow(u);if(a.isEditable||c){const h=r.current.getCell(l,u);if(lt(h))return{...a.getCell(a.getCellValue(h),!1),lastUpdated:h.lastUpdated};if(c)return Dt("Error during cell creation",`This error should never happen. Please report this bug. No cell found for an added row: col=${l}; row=${u}`)}const d=e.getCell(u,l),g=Ig(e,u,l);return jb(a,d,g,e.styler?.cssStyles)}catch(a){return Dt("Error during cell creation",`This error should never happen. Please report this bug. 
Error: ${a}`)}},[t,n,e,r])}}function xC(e){const[t,n]=f.useState(void 0),r=f.useCallback(o=>{if(o.kind!=="cell")n(void 0);else{const[,s]=o.location;n(s)}},[n]);return{getRowThemeOverride:f.useCallback(o=>{if(o===t)return{bgCell:e.bgRowHovered,bgCellMedium:e.bgRowHovered}},[e.bgRowHovered,t]),onItemHovered:r}}function kC(e,t,n,r,i){const[o,s]=f.useState({columns:Qe.empty(),rows:Qe.empty(),current:void 0}),a=!t&&!n&&(e.selectionMode.includes(on.SelectionMode.MULTI_ROW)||e.selectionMode.includes(on.SelectionMode.SINGLE_ROW)),l=a&&e.selectionMode.includes(on.SelectionMode.MULTI_ROW),u=!t&&!n&&(e.selectionMode.includes(on.SelectionMode.SINGLE_COLUMN)||e.selectionMode.includes(on.SelectionMode.MULTI_COLUMN)),c=u&&e.selectionMode.includes(on.SelectionMode.MULTI_COLUMN),d=!t&&!n&&(e.selectionMode.includes(on.SelectionMode.SINGLE_CELL)||e.selectionMode.includes(on.SelectionMode.MULTI_CELL)),g=d&&e.selectionMode.includes(on.SelectionMode.MULTI_CELL),h=o.rows.length>0,p=o.columns.length>0,m=o.current!==void 0,y=f.useCallback(v=>{const C=!Ka(v.rows.toArray(),o.rows.toArray()),E=!Ka(v.columns.toArray(),o.columns.toArray()),I=!Ka(v.current,o.current);let R=a&&C||u&&E||d&&I,P=v;if((a||u)&&v.current!==void 0&&I&&(P={...v,rows:v.rows.length===0?o.rows:v.rows,columns:v.columns.length===0?o.columns:v.columns},R=d),C&&v.rows.length>0&&E&&v.columns.length===0&&(P={...P,rows:v.rows,columns:o.columns},R=!0),E&&v.columns.length>0&&C&&v.rows.length===0&&(P={...P,columns:v.columns,rows:o.rows,current:o.current},R=!0),E&&P.columns.length>=0){let x=P.columns;r.forEach((S,F)=>{S.isIndex&&(x=x.remove(F))}),x.length<P.columns.length&&(P={...P,columns:x})}s(P),R&&i(P,d)},[o,a,u,d,i,r]),b=f.useCallback((v=!1,C=!1)=>{const E={columns:C?o.columns:Qe.empty(),rows:v?o.rows:Qe.empty(),current:void 0};s(E),(!v&&a||!C&&u||d)&&i(E,d)},[o,a,u,d,i]);return{gridSelection:o,isRowSelectionActivated:a,isMultiRowSelectionActivated:l,isColumnSelectionActivated:u,isMultiColumnSelectionActivated:c,isCellSelectionActivated:d,isMultiCellSelectionActivated:g,isRowSelected:h,isColumnSelected:p,isCellSelected:m,clearSelection:b,processSelectionChange:y}}function MC(e,t,n,r,i,o,s,a,l){const u=e.rowHeight??t.defaultRowHeight,c=t.defaultHeaderHeight+u+2*t.tableBorderWidth,d=r?2:1,g=e.editingMode===on.EditingMode.DYNAMIC?1:0,h=n+g;let p=Math.max(h*u+d*t.defaultHeaderHeight+2*t.tableBorderWidth,c),m=Math.min(p,t.defaultTableHeight);const y=Zb(e,l);y&&(m=Math.max(y,c),p=Math.max(y,p)),o&&(m=Math.min(m,o),p=Math.min(p,o),y||(m=p));const b=t.minColumnWidth+2*t.tableBorderWidth,v=Math.max(i,b);let C,E=v;const I=pf(e,a),R=mf(e,a),P=Kb(a);I?C=v:R?(C=Math.min(Math.max(R,b),v),E=Math.min(Math.max(R,E),v)):P&&(C=void 0);const[x,S]=f.useState({width:C||"100%",height:m});return f.useLayoutEffect(()=>{I&&x.width==="100%"&&S(F=>({...F,width:v}))},[v]),f.useLayoutEffect(()=>{S(F=>({...F,width:C||"100%"}))},[C]),f.useLayoutEffect(()=>{S(F=>({...F,height:m}))},[m,n]),f.useLayoutEffect(()=>{if(s){const F=I||lt(R)&&R>0;S({width:F?E:"100%",height:p})}else S({width:C||"100%",height:m})},[s]),{minHeight:c,maxHeight:p,minWidth:b,maxWidth:E,rowHeight:u,resizableSize:x,setResizableSize:S}}const RC=600,IC="⚠️ Please fill out this cell.";function EC(e,t,n=[]){const[r,i]=f.useState(),o=f.useRef(null),s=f.useCallback(l=>{if(clearTimeout(o.current),o.current=0,i(void 0),(l.kind==="header"||l.kind==="cell")&&l.location){const u=l.location[0],c=l.location[1];let d;if(u<0||u>=e.length||n.includes(c))return;const g=e[u];if(l.kind==="header"&&lt(g))d=g.help;else if(l.kind==="cell"){const h=t([u,c]);ki(h)?d=h.errorDetails:g.isRequired&&g.isEditable&&sl(h)?d=IC:xb(h)&&(d=h.tooltip)}d&&(o.current=setTimeout(()=>{d&&i({content:d,left:l.bounds.x+l.bounds.width/2,top:l.bounds.y})},RC))}},[e,t,i,o,n]),a=f.useCallback(()=>{i(void 0)},[i]);return{tooltip:r,clearTooltip:a,onItemHovered:s}}const Zf=nr("div",{target:"e32gfpu0"})(({theme:e})=>({paddingTop:e.spacing.xs,paddingBottom:e.spacing.xs})),Dr=nr("div",{target:"e32gfpu1"})(({theme:e,isActive:t,hasSubmenu:n})=>({display:"flex",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,paddingTop:e.spacing.twoXS,paddingBottom:e.spacing.twoXS,cursor:"pointer",backgroundColor:t?e.colors.darkenedBgMix15:void 0,"&:hover":{backgroundColor:e.colors.darkenedBgMix15},minWidth:e.sizes.minMenuWidth,...n&&{justifyContent:"space-between","& > :first-of-type":{display:"flex",alignItems:"center",gap:e.spacing.sm}}})),Jf=nr("div",{target:"e32gfpu2"})(({theme:e})=>({height:e.sizes.borderWidth,backgroundColor:e.colors.borderColor,marginTop:e.spacing.xs,marginBottom:e.spacing.xs})),TC=nr("div",{target:"e32gfpu3"})(({theme:e})=>({display:"flex",alignItems:"center",padding:`${e.spacing.sm} ${e.spacing.sm}`,cursor:"default",gap:e.spacing.twoXS})),DC=nr("div",{target:"e32gfpu4"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",padding:e.spacing.twoXS,border:`${e.sizes.borderWidth} solid ${e.colors.borderColor}`,borderRadius:e.radii.md,backgroundColor:"transparent",color:e.colors.bodyText,height:"fit-content"})),OC=nr("div",{target:"e32gfpu5"})(({theme:e})=>({display:"flex",alignItems:"center",flexGrow:1,padding:`${e.spacing.threeXS} ${e.spacing.threeXS}`,border:`${e.sizes.borderWidth} solid ${e.colors.borderColor}`,borderRadius:e.radii.md,backgroundColor:e.colors.secondaryBg,minWidth:0,overflow:"hidden"})),PC=nr("span",{target:"e32gfpu6"})(({theme:e})=>({whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",flexGrow:1,margin:`0 ${e.spacing.xs}`,fontSize:e.fontSizes.twoSm,maxWidth:"4rem"})),LC=nr("button",{target:"e32gfpu7"})(({theme:e})=>({background:"none",border:"none",padding:e.spacing.twoXS,cursor:"pointer",color:e.colors.bodyText,display:"flex",alignItems:"center",justifyContent:"center",borderRadius:e.radii.md,transition:"background-color 0.2s ease","&:hover":{backgroundColor:e.colors.fadedText05},"&:active":{backgroundColor:e.colors.fadedText10}})),Ac=[{format:"",label:"Automatic",icon:":material/123:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"plain",label:"Plain",icon:":material/speed_1_75:"},{format:"compact",label:"Compact",icon:":material/1k:"},{format:"dollar",label:"Dollar",icon:":material/attach_money:"},{format:"euro",label:"Euro",icon:":material/euro:"},{format:"yen",label:"Yen",icon:":material/currency_yen:"},{format:"percent",label:"Percent",icon:":material/percent:"},{format:"scientific",label:"Scientific",icon:":material/experiment:"},{format:"accounting",label:"Accounting",icon:":material/finance_chip:"}],FC={number:Ac,progress:Ac,datetime:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"},{format:"calendar",label:"Calendar",icon:":material/today:"}],date:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"}],time:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"}]};function _C({columnKind:e,isOpen:t,onMouseEnter:n,onMouseLeave:r,onChangeFormat:i,onCloseMenu:o,children:s}){const a=Kr(),{colors:l,fontSizes:u,radii:c,fontWeights:d}=a,g=FC[e]||[];return g.length===0?He(Kc,{}):He(Ma,{triggerType:Zc.hover,returnFocus:!0,autoFocus:!0,focusLock:!0,isOpen:t,onMouseEnter:n,onMouseLeave:r,ignoreBoundary:!0,content:He(Zf,{role:"menu",children:g.map(h=>vn(Dr,{onClick:()=>{i(h.format),o()},role:"menuitem",children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:h.icon}),h.label]},h.format))}),placement:xa.right,showArrow:!1,popoverMargin:2,overrides:{Body:{props:{"data-testid":"stDataFrameColumnFormattingMenu"},style:{borderTopLeftRadius:c.default,borderTopRightRadius:c.default,borderBottomLeftRadius:c.default,borderBottomRightRadius:c.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${a.sizes.borderWidth} solid ${a.colors.borderColor}`}},Inner:{style:{backgroundColor:ka(a)?l.bgColor:l.secondaryBg,color:l.bodyText,fontSize:u.sm,fontWeight:d.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:s})}const AC=f.memo(_C);function HC({top:e,left:t,isColumnPinned:n,onPinColumn:r,onUnpinColumn:i,onCloseMenu:o,onSortColumn:s,onHideColumn:a,column:l,onChangeFormat:u,onAutosize:c}){const d=Kr(),[g,h]=f.useState(!1),{colors:p,fontSizes:m,radii:y,fontWeights:b}=d,{isCopied:v,copyToClipboard:C}=Eg();f.useEffect(()=>{function R(P){P.preventDefault()}return document.addEventListener("wheel",R,{passive:!1}),document.addEventListener("touchmove",R,{passive:!1}),()=>{document.removeEventListener("wheel",R),document.removeEventListener("touchmove",R)}},[]);const E=f.useCallback(()=>{o()},[o]),I=f.useCallback(()=>{C(l.title)},[l.title,C]);return He(Ma,{autoFocus:!0,"aria-label":"Dataframe column menu",content:vn(Zf,{children:[vn(TC,{children:[He(DC,{title:l.kind,children:He(Jn,{iconValue:l.typeIcon||":material/notes:",size:"base",color:"inherit"})}),vn(OC,{title:l.title,children:[He(PC,{children:l.title}),He(LC,{onClick:I,title:"Copy column name","aria-label":"Copy column name",children:He(Jn,{iconValue:v?":material/check:":":material/content_copy:",size:"sm",margin:"0",color:"inherit"})})]})]}),s&&vn(Kc,{children:[vn(Dr,{onClick:()=>{s("asc"),E()},role:"menuitem",children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_upward:"}),"Sort ascending"]}),vn(Dr,{onClick:()=>{s("desc"),E()},role:"menuitem",children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_downward:"}),"Sort descending"]}),He(Jf,{})]}),u&&He(AC,{columnKind:l.kind,isOpen:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onChangeFormat:u,onCloseMenu:E,children:vn(Dr,{onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),isActive:g,hasSubmenu:!0,children:[vn("div",{children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/format_list_numbered:"}),"Format"]}),He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/chevron_right:"})]})}),c&&vn(Dr,{onClick:()=>{c(),E()},children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrows_outward:"}),"Autosize"]}),n&&vn(Dr,{onClick:()=>{i(),E()},children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep_off:"}),"Unpin column"]}),!n&&vn(Dr,{onClick:()=>{r(),E()},children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep:"}),"Pin column"]}),a&&vn(Dr,{onClick:()=>{a(),E()},children:[He(Jn,{size:"base",margin:"0",color:"inherit",iconValue:":material/visibility_off:"}),"Hide column"]})]}),placement:xa.bottomRight,accessibilityType:Jc.menu,showArrow:!1,popoverMargin:rn("0.375rem"),onClickOutside:g?void 0:E,onEsc:E,overrides:{Body:{props:{"data-testid":"stDataFrameColumnMenu"},style:{paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{border:`${d.sizes.borderWidth} solid ${d.colors.borderColor}`,backgroundColor:ka(d)?p.bgColor:p.secondaryBg,color:p.bodyText,fontSize:m.sm,fontWeight:b.normal,borderTopLeftRadius:y.default,borderTopRightRadius:y.default,borderBottomLeftRadius:y.default,borderBottomRightRadius:y.default,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:!0,children:He("div",{"data-testid":"stDataFrameColumnMenuTarget",style:{position:"fixed",top:e,left:t,visibility:"hidden",transform:"unset"}})})}const zC=f.memo(HC),VC="(index)";function Is(e,t){return!t.length||e.isIndex?!1:!t.includes(e.id)&&!t.includes(e.name)}const Hc=({label:e,initialValue:t,isIndeterminate:n,onChange:r})=>{const i=Kr();return He(Bg,{isIndeterminate:n,checked:t,onChange:o=>{r(o.target.checked)},"aria-label":e,checkmarkType:Ng.default,labelPlacement:$g.right,overrides:{Root:{style:({$isFocusVisible:o})=>({marginBottom:i.spacing.none,marginTop:i.spacing.none,paddingLeft:i.spacing.md,paddingRight:i.spacing.md,paddingTop:i.spacing.twoXS,paddingBottom:i.spacing.twoXS,backgroundColor:o?i.colors.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Checkmark:{style:({$isFocusVisible:o,$checked:s,$isIndeterminate:a})=>{const l=s||a?i.colors.primary:i.colors.fadedText40;return{outline:0,width:i.sizes.checkbox,height:i.sizes.checkbox,marginTop:i.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:o&&(s||a)?`0 0 0 0.2rem ${mi(i.colors.primary,.5)}`:"",borderLeftWidth:i.sizes.borderWidth,borderRightWidth:i.sizes.borderWidth,borderTopWidth:i.sizes.borderWidth,borderBottomWidth:i.sizes.borderWidth,borderLeftColor:l,borderRightColor:l,borderTopColor:l,borderBottomColor:l}}},Label:{style:{lineHeight:i.lineHeights.small,paddingLeft:i.spacing.sm,position:"relative",color:i.colors.bodyText,fontSize:i.fontSizes.sm,fontWeight:i.fontWeights.normal}}},children:e})},$C=({columns:e,columnOrder:t,setColumnOrder:n,hideColumn:r,showColumn:i,children:o,isOpen:s,onClose:a})=>{const l=Kr(),u=h=>!(h.isHidden===!0||Is(h,t)),c=e.every(u),d=e.some(u)&&!c,g=h=>{e.forEach(p=>{const m=Is(p,t);h?(i(p.id),m&&n(y=>[...y,p.id])):r(p.id)})};return He(Ma,{triggerType:Zc.click,placement:xa.bottomRight,autoFocus:!0,focusLock:!0,content:()=>vn("div",{style:{paddingTop:l.spacing.sm,paddingBottom:l.spacing.sm,maxHeight:l.sizes.maxDropdownHeight,overflow:"auto"},children:[He(Hc,{label:"Select all",isIndeterminate:d,initialValue:c,onChange:h=>{g(h)}}),He(Jf,{}),He("div",{children:e.map(h=>{const p=Is(h,t);return He(Hc,{label:!h.title&&h.isIndex?VC:h.title,initialValue:!(h.isHidden===!0||p),onChange:m=>{m?(i(h.id),p&&n(y=>[...y,h.id])):r(h.id)}},h.id)})})]}),isOpen:s,onClickOutside:a,onClick:()=>s?a():void 0,onEsc:a,ignoreBoundary:!1,overrides:{Body:{props:{"data-testid":"stDataFrameColumnVisibilityMenu"},style:{borderTopLeftRadius:l.radii.default,borderTopRightRadius:l.radii.default,borderBottomLeftRadius:l.radii.default,borderBottomRightRadius:l.radii.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${l.sizes.borderWidth} solid ${l.colors.borderColor}`}},Inner:{style:{backgroundColor:ka(l)?l.colors.bgColor:l.colors.secondaryBg,color:l.colors.bodyText,fontSize:l.fontSizes.sm,fontWeight:l.fontWeights.normal,minWidth:l.sizes.minMenuWidth,maxWidth:`calc(${l.sizes.minMenuWidth} * 2)`,maxHeight:l.sizes.maxDropdownHeight,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:He("div",{children:o})})},NC=f.memo($C),zc=nr("div",{target:"e8es05k0"})(({theme:e,isInHorizontalLayout:t})=>({position:"relative",display:t?"flex":"inline-block","& .stDataFrameGlideDataEditor":{height:"100%",minWidth:"100%",borderRadius:e.radii.default},"& .dvn-scroller":{overflowX:"auto !important",overflowY:"auto !important"},"& .gdg-search-bar":{maxWidth:"19rem",width:"80%",minWidth:"6rem",top:e.spacing.sm,right:e.spacing.sm,padding:e.spacing.sm,borderRadius:e.radii.default,"& .gdg-search-status":{paddingTop:e.spacing.twoXS,fontSize:e.fontSizes.twoSm},"& .gdg-search-progress":{display:"none"},"& input":{width:"100%"},"& button":{width:e.iconSizes.xl,height:e.iconSizes.xl,"& .button-icon":{width:e.iconSizes.base,height:e.iconSizes.base}}}}));function BC({top:e,left:t,content:n,clearTooltip:r}){const[i,o]=f.useState(!0),s=Kr(),{colors:a,fontSizes:l,radii:u,fontWeights:c}=s,d=f.useCallback(()=>{o(!1),r()},[r,o]);return He(Ma,{content:He(Tg,{"data-testid":"stDataFrameTooltipContent",children:He(Dg,{style:{fontSize:l.sm},source:n,allowHTML:!1})}),placement:xa.top,accessibilityType:Jc.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:d,onEsc:d,overrides:{Body:{style:{borderTopLeftRadius:u.default,borderTopRightRadius:u.default,borderBottomLeftRadius:u.default,borderBottomRightRadius:u.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:ka(s)?a.bgColor:a.secondaryBg,color:a.bodyText,fontSize:l.sm,fontWeight:c.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:i,children:He("div",{"data-testid":"stDataFrameTooltipTarget",style:{position:"fixed",top:e,left:t}})})}const WC=f.memo(BC),Vc=150,UC=15e4,qC="0.5rem";function GC({element:e,data:t,disabled:n,widgetMgr:r,disableFullscreenMode:i,fragmentId:o,customToolbarActions:s,widthConfig:a,heightConfig:l}){const{expanded:u,expand:c,collapse:d,width:g,height:h}=Wl(Ag),{isInHorizontalLayout:p}=Wl(Og),m=f.useRef(null),y=f.useRef(null),b=f.useRef(null),v=Pg(),C=gC(),{getRowThemeOverride:E,onItemHovered:I}=xC(C),{libConfig:{enforceDownloadInNewTab:R=!1}}=f.useContext(Lg),[P,x]=f.useState(!0),[S,F]=f.useState(!1),[D,M]=f.useState(!1),[T,O]=f.useState(!1),[k,$]=f.useState(),[G,j]=f.useState(!1),ue=f.useCallback(()=>j(Ce=>!Ce),[]),Q=f.useCallback(()=>j(!1),[]),ee=f.useMemo(()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,[]);ze(e.editingMode)&&(e.editingMode=on.EditingMode.READ_ONLY);const{READ_ONLY:ce,DYNAMIC:ae}=on.EditingMode,ge=t.dimensions,de=Math.max(0,ge.numDataRows),re=de===0&&!(e.editingMode===ae&&ge.numDataColumns>0),H=de>UC,L=!H&&!re&&e.editingMode!==ae,X=!re&&e.editingMode===ae&&!n,te=f.useRef(new Zo(de)),[ie,Se]=f.useState(te.current.getNumRows());f.useEffect(()=>{te.current=new Zo(de),Se(te.current.getNumRows())},[de]);const me=f.useCallback(()=>{te.current=new Zo(de),Se(te.current.getNumRows())},[de]),[nt,xe]=f.useState(e.columnOrder);f.useEffect(()=>{xe(e.columnOrder)},[e.columnOrder.join(",")]);const{columns:gt,allColumns:Mt,setColumnConfigMapping:Ue}=e1(e,t,n,nt,a);f.useEffect(()=>{if(e.editingMode===ce||!r)return;const Ce=r.getStringValue({id:e.id,formId:e.formId});Ce&&(te.current.fromJson(Ce,gt),Se(te.current.getNumRows()))},[]);const{getCellContent:ne}=SC(t,gt,ie,te),{columns:oe,sortColumn:we,getOriginalIndex:fe,getCellContent:ye}=c1(de,gt,ne),$e=f.useCallback((Ce,_t)=>{if(!r)return;const Jt={selection:{rows:[],columns:[],cells:[]}};if(Jt.selection.rows=Ce.rows.toArray().map(Yt=>fe(Yt)),Jt.selection.columns=Ce.columns.toArray().map(Yt=>Xr(oe[Yt])),_t&&Ce.current){const{cell:Yt,range:jt}=Ce.current;if(jt)for(let Cn=jt.y;Cn<jt.y+jt.height;Cn++)for(let Bn=jt.x;Bn<jt.x+jt.width;Bn++)oe[Bn].isIndex||Jt.selection.cells.push([fe(Cn),Xr(oe[Bn])]);else if(Yt){const[Cn,Bn]=Yt;oe[Cn].isIndex||Jt.selection.cells.push([fe(Bn),Xr(oe[Cn])])}}const Ln=JSON.stringify(Jt),un=r.getStringValue({id:e.id,formId:e.formId});(un===void 0||un!==Ln)&&r.setStringValue({id:e.id,formId:e.formId},Ln,{fromUi:!0},o)},[oe,e.id,e.formId,r,o,fe]),{debouncedCallback:Pe}=Zu($e,Vc),{gridSelection:it,isRowSelectionActivated:Me,isMultiRowSelectionActivated:qe,isColumnSelectionActivated:Te,isMultiColumnSelectionActivated:Ee,isCellSelectionActivated:De,isMultiCellSelectionActivated:ft,isRowSelected:ot,isColumnSelected:Xe,isCellSelected:wt,clearSelection:yt,processSelectionChange:Ft}=kC(e,re,n,oe,Pe);f.useEffect(()=>{De||yt(!0,!0)},[u]);const It=f.useCallback(Ce=>{y.current?.updateCells(Ce)},[]);f.useEffect(()=>{if(!Me&&!Te&&!De||!r)return;const Ce=r.getStringValue({id:e.id,formId:e.formId});if(Ce){const _t=oe.map(jt=>Xr(jt)),Jt=JSON.parse(Ce);let Ln=Qe.empty(),un=Qe.empty(),Yt;if(Jt.selection?.rows?.forEach(jt=>{Ln=Ln.add(jt)}),Jt.selection?.columns?.forEach(jt=>{un=un.add(_t.indexOf(jt))}),De&&!ft){const[jt,Cn]=Jt.selection?.cells?.[0]??[];jt!==void 0&&Cn!==void 0&&(Yt=[_t.indexOf(Cn),jt])}if(Ln.length>0||un.length>0||Yt!==void 0){const jt={rows:Ln,columns:un,current:Yt?{cell:Yt,range:{x:Yt[0],y:Yt[1],width:1,height:1},rangeStack:[]}:void 0};Ft(jt)}}},[]);const dn=f.useCallback(()=>{ie!==te.current.getNumRows()&&Se(te.current.getNumRows())},[ie]),Zt=f.useCallback(()=>{if(!r)return;const Ce=te.current.toJson(oe);let _t=r.getStringValue({id:e.id,formId:e.formId});_t===void 0&&(_t=new Zo(0).toJson([])),Ce!==_t&&r.setStringValue({id:e.id,formId:e.formId},Ce,{fromUi:!0},o)},[oe,e.id,e.formId,r,o]),{debouncedCallback:fn}=Zu(Zt,Vc),{exportToCsv:Ot}=CC(ye,oe,ie,R),{onCellEdited:Ct,onPaste:Tn,onRowAppended:pt,onDelete:en,validateCell:Bt}=mC(oe,e.editingMode!==ae,te,ye,fe,It,dn,fn,yt),Le=f.useMemo(()=>re?[0]:X?[ie]:[],[re,X,ie]),{tooltip:St,clearTooltip:mt,onItemHovered:Dn}=EC(oe,ye,Le),{drawCell:Gt,customRenderers:hn}=hC(oe),{provideEditor:On}=f1(),Xt=f.useCallback(Ce=>({...Ce,hasMenu:!re}),[re]),tn=f.useMemo(()=>oe.map(Ce=>Xt(Vs(Ce))),[oe,Xt]),{columns:Be,onColumnResize:Pt}=i1(tn),$n=t.dimensions.numHeaderRows>1,{minHeight:xt,maxHeight:Et,minWidth:kn,maxWidth:cr,rowHeight:Nn,resizableSize:N,setResizableSize:We}=MC(e,C,ie,$n,g||0,h,u,a,l),Ge=f.useCallback(([Ce,_t])=>({...kb(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:C.glideTheme.textLight},span:[0,Math.max(oe.length-1,0)]}),[oe,C.glideTheme.textLight]),Rt=f.useCallback(()=>{me(),yt()},[me,yt]);zg({element:e,widgetMgr:r,onFormCleared:Rt});const{pinColumn:gn,unpinColumn:ln,freezeColumns:Mn}=n1(oe,re,g||0,C.minColumnWidth,yt,Ue),{changeColumnFormat:wn}=t1(Ue),{hideColumn:Wt,showColumn:yn}=d1(yt,Ue),{onColumnMoved:Pn}=r1(oe,Mn,gn,ln,xe);return f.useEffect(()=>{let Ce;const _t=requestAnimationFrame(()=>{Ce=setTimeout(()=>{if(b.current&&y.current){const Jt=b.current?.querySelector(".dvn-stack")?.getBoundingClientRect();Jt&&(M(Jt.height>b.current.clientHeight),O(Jt.width>b.current.clientWidth))}},0)});return()=>{cancelAnimationFrame(_t),Ce&&clearTimeout(Ce)}},[N,ie,Be]),f.useEffect(()=>{Mt.length==oe.length&&j(!1)},[Mt.length,oe.length]),vn(zc,{className:"stDataFrame","data-testid":"stDataFrame",ref:b,isInHorizontalLayout:p,onPointerDown:Ce=>{if(b.current){const _t=b.current.getBoundingClientRect(),Jt=(v||Math.round(rn(qC)))+1;T&&_t.height-Jt<Ce.clientY-_t.top&&Ce.stopPropagation(),D&&_t.width-Jt<Ce.clientX-_t.left&&Ce.stopPropagation()}},onBlur:Ce=>{!P&&!ee&&!Ce.currentTarget.contains(Ce.relatedTarget)&&!De&&yt(!0,!0)},children:[vn(Hg,{isFullScreen:u,disableFullscreenMode:i,locked:ot&&!Me||wt||ee&&P||G,onExpand:c,onCollapse:d,target:zc,children:[s?.map(Ce=>Ce),(Me&&ot||Te&&Xe||De&&wt)&&He(gi,{label:"Clear selection",icon:Ug,onClick:()=>{yt(),mt()}}),X&&ot&&He(gi,{label:"Delete row(s)",icon:jg,onClick:()=>{en&&(en(it),mt())}}),X&&!ot&&He(gi,{label:"Add row",icon:Qc,onClick:()=>{pt&&(x(!0),pt(),mt(),y.current?.scrollTo(0,ie,"vertical"))}}),!re&&Mt.length>oe.length&&He(NC,{columns:Mt,columnOrder:nt,setColumnOrder:xe,hideColumn:Wt,showColumn:yn,isOpen:G,onClose:Q,children:He(gi,{label:"Show/hide columns",icon:td,onClick:ue})}),!H&&!re&&He(gi,{label:"Download as CSV",icon:Kg,onClick:Ot}),!re&&He(gi,{label:"Search",icon:ed,onClick:()=>{S?F(!1):(x(!0),F(!0)),mt()}})]}),He(Fg,{"data-testid":"stDataFrameResizable",ref:m,defaultSize:N,style:{border:`${C.tableBorderWidth}px solid ${C.glideTheme.borderColor}`,borderRadius:`${C.tableBorderRadius}`},minHeight:xt,maxHeight:Et,minWidth:kn,maxWidth:p?void 0:cr,size:N,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!p,bottomLeft:!1,topLeft:!1},grid:[1,Nn],snapGap:Nn/3,onResizeStop:(Ce,_t,Jt,Ln)=>{if(m.current){const un=2*C.tableBorderWidth;We({width:m.current.size.width,height:Et-m.current.size.height===un?m.current.size.height+un:m.current.size.height})}},children:He(mb,{className:"stDataFrameGlideDataEditor","data-testid":"stDataFrameGlideDataEditor",ref:y,columns:Be,rows:re?1:ie,minColumnWidth:C.minColumnWidth,maxColumnWidth:C.maxColumnWidth,maxColumnAutoWidth:C.maxColumnAutoWidth,rowHeight:Nn,headerHeight:C.defaultHeaderHeight,getCellContent:re?Ge:ye,onColumnResize:ee?void 0:Pt,resizeIndicator:"header",freezeColumns:Mn,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:ee?"cell":"rect",columnSelect:"none",rowSelect:"none",onColumnMoved:Te?void 0:Pn,onItemHovered:Ce=>{I?.(Ce),Dn?.(Ce)},keybindings:{downFill:!0,...De||H?{selectAll:!1}:{}},onKeyDown:Ce=>{(Ce.ctrlKey||Ce.metaKey)&&Ce.key==="f"&&(F(_t=>!_t),Ce.stopPropagation(),Ce.preventDefault())},showSearch:S,searchResults:S?void 0:[],onSearchClose:()=>{F(!1),mt()},onHeaderClicked:(Ce,_t)=>{!L||Te||(S&&F(!1),Me&&ot?yt():yt(!0,!0),we(Ce,"auto"))},gridSelection:it,onGridSelectionChange:Ce=>{(P||ee)&&(Ft(Ce),St!==void 0&&mt(),$(void 0),j(!1))},theme:C.glideTheme,getRowThemeOverride:E,onMouseMove:Ce=>{Ce.kind==="out-of-bounds"&&P?x(!1):Ce.kind!=="out-of-bounds"&&!P&&x(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,paddingBottom:T?-v:void 0,paddingRight:D?-v:void 0},provideEditor:On,drawCell:Gt,customRenderers:hn,imageEditorOverride:zb,headerIcons:C.headerIcons,validateCell:Bt,onHeaderMenuClick:(Ce,_t)=>{G||$({columnIdx:Ce,headerBounds:_t})},onPaste:!1,...Me&&{rowMarkers:{kind:"checkbox-visible",checkboxStyle:"square",theme:{bgCell:C.glideTheme.bgHeader,bgCellMedium:C.glideTheme.bgHeader,textMedium:C.glideTheme.textLight}},rowSelectionMode:qe?"multi":"auto",rowSelect:n?"none":qe?"multi":"single",rowSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...Te&&{columnSelect:n?"none":Ee?"multi":"single",columnSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...De&&{rangeSelect:ft?"rect":"cell",rangeSelectionBlending:"mixed"},...!re&&e.editingMode!==ce&&!n&&{fillHandle:!ee,onCellEdited:Ct,onPaste:Tn,onDelete:en},...!re&&e.editingMode===ae&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:C.glideTheme.bgHeader,bgCellMedium:C.glideTheme.bgHeader}},rowSelectionMode:"multi",rowSelect:n?"none":"multi",onRowAppended:n?void 0:pt,onHeaderClicked:void 0}})}),St&&St.content&&He(WC,{top:St.top,left:St.left,content:St.content,clearTooltip:mt}),k&&Yc.createPortal(He(zC,{top:k.headerBounds.y+k.headerBounds.height,left:k.headerBounds.x+k.headerBounds.width,column:gt[k.columnIdx],onCloseMenu:()=>$(void 0),onSortColumn:L?Ce=>{S&&F(!1),yt(!(Me&&ot),!0),we(k.columnIdx,Ce,!0)}:void 0,isColumnPinned:gt[k.columnIdx].isPinned,onUnpinColumn:()=>{ln(gt[k.columnIdx].id)},onPinColumn:()=>{gn(gt[k.columnIdx].id)},onHideColumn:()=>{Wt(gt[k.columnIdx].id)},onChangeFormat:Ce=>{wn(gt[k.columnIdx].id,Ce),setTimeout(()=>{y.current?.remeasureColumns(Qe.fromSingleSelection(k.columnIdx))},100)},onAutosize:()=>{y.current?.remeasureColumns(Qe.fromSingleSelection(k.columnIdx))}}),document.querySelector("#portal"))]})}const XC=_g(GC),Qf=f.memo(XC),YC=({data:e,height:t,customToolbarActions:n})=>He(Qf,{element:new on({useContainerWidth:!0,editingMode:on.EditingMode.READ_ONLY,disabled:!0,data:void 0,styler:null,width:void 0,height:t??null,id:"",columns:"",formId:"",columnOrder:[],selectionMode:[]}),data:e,widgetMgr:void 0,disabled:!0,fragmentId:void 0,disableFullscreenMode:!0,customToolbarActions:n,widthConfig:new Ul.WidthConfig({useStretch:!0}),heightConfig:t?new Ul.HeightConfig({pixelHeight:t}):void 0}),aS=Object.freeze(Object.defineProperty({__proto__:null,ReadOnlyGrid:YC,default:Qf},Symbol.toStringTag,{value:"Module"}));export{K0 as C,YC as R,Ld as T,yi as a,np as b,aS as c,vi as i,bm as m,sn as s};
