# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class AgentDeploymentSummary(pydantic.BaseModel):
    id: str = pydantic.Field(description="Deployment ID. Prefixed with dpl-")
    project_id: str = pydantic.Field(description="Project ID")
    agent_slug: str = pydantic.Field(description="readable ID of the deployed app")
    thumbnail_url: typing.Optional[str]
    base_url: str = pydantic.Field(description="Base URL of the deployed app")
    display_name: str = pydantic.Field(description="Display name of the deployed app")
    created_at: dt.datetime = pydantic.Field(description="Timestamp when the app deployment was created")
    updated_at: dt.datetime = pydantic.Field(description="Timestamp when the app deployment was last updated")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
