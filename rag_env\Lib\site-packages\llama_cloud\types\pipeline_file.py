# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .pipeline_file_config_hash_value import PipelineFileConfigHashValue
from .pipeline_file_custom_metadata_value import PipelineFileCustomMetadataValue
from .pipeline_file_permission_info_value import PipelineFilePermissionInfoValue
from .pipeline_file_resource_info_value import PipelineFileResourceInfoValue
from .pipeline_file_status import PipelineFileStatus

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PipelineFile(pydantic.BaseModel):
    """
    Schema for a file that is associated with a pipeline.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    name: typing.Optional[str]
    external_file_id: typing.Optional[str]
    file_size: typing.Optional[int]
    file_type: typing.Optional[str]
    project_id: str = pydantic.Field(description="The ID of the project that the file belongs to")
    last_modified_at: typing.Optional[dt.datetime]
    resource_info: typing.Optional[typing.Dict[str, typing.Optional[PipelineFileResourceInfoValue]]]
    permission_info: typing.Optional[typing.Dict[str, typing.Optional[PipelineFilePermissionInfoValue]]]
    data_source_id: typing.Optional[str]
    file_id: typing.Optional[str]
    pipeline_id: str = pydantic.Field(description="The ID of the pipeline that the file is associated with")
    custom_metadata: typing.Optional[typing.Dict[str, typing.Optional[PipelineFileCustomMetadataValue]]]
    config_hash: typing.Optional[typing.Dict[str, typing.Optional[PipelineFileConfigHashValue]]]
    indexed_page_count: typing.Optional[int]
    status: typing.Optional[PipelineFileStatus]
    status_updated_at: typing.Optional[dt.datetime]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
