# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .llama_parse_parameters import LlamaParseParameters

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class Batch(pydantic.BaseModel):
    tool: str = pydantic.Field(description="The tool to be used for all requests in the batch.")
    tool_data: typing.Optional[LlamaParseParameters]
    input_type: str = pydantic.Field(description="The type of input file. Currently only 'datasource' is supported.")
    input_id: str = pydantic.Field(description="The ID of the input file for the batch.")
    output_type: typing.Optional[str]
    output_id: typing.Optional[str]
    id: str = pydantic.Field(description="Unique identifier for the batch")
    project_id: str = pydantic.Field(description="The ID of the project to which the batch belongs")
    organization_id: str = pydantic.Field(description="The ID of the organization to which the batch belongs")
    user_id: str = pydantic.Field(description="The ID of the user who created the batch")
    external_id: typing.Optional[str]
    completion_window: int = pydantic.Field(description="The time frame within which the batch should be processed")
    pipeline_id: str = pydantic.Field(description="The ID of the pipeline to which the batch belongs")
    status: str = pydantic.Field(description="The current status of the batch")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
