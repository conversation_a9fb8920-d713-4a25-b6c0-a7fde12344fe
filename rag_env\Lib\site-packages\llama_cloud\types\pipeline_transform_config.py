# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .advanced_mode_transform_config import AdvancedModeTransformConfig
from .auto_transform_config import AutoTransformConfig


class PipelineTransformConfig_Auto(AutoTransformConfig):
    mode: typing_extensions.Literal["auto"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineTransformConfig_Advanced(AdvancedModeTransformConfig):
    mode: typing_extensions.Literal["advanced"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


PipelineTransformConfig = typing.Union[PipelineTransformConfig_Auto, PipelineTransformConfig_Advanced]
