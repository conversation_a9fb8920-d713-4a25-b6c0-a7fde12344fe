# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class LlamaParseSupportedFileExtensions(str, enum.Enum):
    PDF = ".pdf"
    DOC = ".doc"
    DOCX = ".docx"
    DOCM = ".docm"
    DOT = ".dot"
    DOTX = ".dotx"
    DOTM = ".dotm"
    RTF = ".rtf"
    WPS = ".wps"
    WPD = ".wpd"
    SXW = ".sxw"
    STW = ".stw"
    SXG = ".sxg"
    PAGES = ".pages"
    MW = ".mw"
    MCW = ".mcw"
    UOT = ".uot"
    UOF = ".uof"
    UOS = ".uos"
    UOP = ".uop"
    PPT = ".ppt"
    PPTX = ".pptx"
    POT = ".pot"
    PPTM = ".pptm"
    POTX = ".potx"
    POTM = ".potm"
    KEY = ".key"
    ODP = ".odp"
    ODG = ".odg"
    OTP = ".otp"
    FOPD = ".fopd"
    SXI = ".sxi"
    STI = ".sti"
    EPUB = ".epub"
    JPG = ".jpg"
    JPEG = ".jpeg"
    PNG = ".png"
    GIF = ".gif"
    BMP = ".bmp"
    SVG = ".svg"
    TIFF = ".tiff"
    WEBP = ".webp"
    HTML = ".html"
    HTM = ".htm"
    XLS = ".xls"
    XLSX = ".xlsx"
    XLSM = ".xlsm"
    XLSB = ".xlsb"
    XLW = ".xlw"
    CSV = ".csv"
    DIF = ".dif"
    SYLK = ".sylk"
    SLK = ".slk"
    PRN = ".prn"
    NUMBERS = ".numbers"
    ET = ".et"
    ODS = ".ods"
    FODS = ".fods"
    UOS_1 = ".uos1"
    UOS_2 = ".uos2"
    DBF = ".dbf"
    WK_1 = ".wk1"
    WK_2 = ".wk2"
    WK_3 = ".wk3"
    WK_4 = ".wk4"
    WKS = ".wks"
    WQ_1 = ".wq1"
    WQ_2 = ".wq2"
    WB_1 = ".wb1"
    WB_2 = ".wb2"
    WB_3 = ".wb3"
    QPW = ".qpw"
    XLR = ".xlr"
    ETH = ".eth"
    TSV = ".tsv"

    def visit(
        self,
        pdf: typing.Callable[[], T_Result],
        doc: typing.Callable[[], T_Result],
        docx: typing.Callable[[], T_Result],
        docm: typing.Callable[[], T_Result],
        dot: typing.Callable[[], T_Result],
        dotx: typing.Callable[[], T_Result],
        dotm: typing.Callable[[], T_Result],
        rtf: typing.Callable[[], T_Result],
        wps: typing.Callable[[], T_Result],
        wpd: typing.Callable[[], T_Result],
        sxw: typing.Callable[[], T_Result],
        stw: typing.Callable[[], T_Result],
        sxg: typing.Callable[[], T_Result],
        pages: typing.Callable[[], T_Result],
        mw: typing.Callable[[], T_Result],
        mcw: typing.Callable[[], T_Result],
        uot: typing.Callable[[], T_Result],
        uof: typing.Callable[[], T_Result],
        uos: typing.Callable[[], T_Result],
        uop: typing.Callable[[], T_Result],
        ppt: typing.Callable[[], T_Result],
        pptx: typing.Callable[[], T_Result],
        pot: typing.Callable[[], T_Result],
        pptm: typing.Callable[[], T_Result],
        potx: typing.Callable[[], T_Result],
        potm: typing.Callable[[], T_Result],
        key: typing.Callable[[], T_Result],
        odp: typing.Callable[[], T_Result],
        odg: typing.Callable[[], T_Result],
        otp: typing.Callable[[], T_Result],
        fopd: typing.Callable[[], T_Result],
        sxi: typing.Callable[[], T_Result],
        sti: typing.Callable[[], T_Result],
        epub: typing.Callable[[], T_Result],
        jpg: typing.Callable[[], T_Result],
        jpeg: typing.Callable[[], T_Result],
        png: typing.Callable[[], T_Result],
        gif: typing.Callable[[], T_Result],
        bmp: typing.Callable[[], T_Result],
        svg: typing.Callable[[], T_Result],
        tiff: typing.Callable[[], T_Result],
        webp: typing.Callable[[], T_Result],
        html: typing.Callable[[], T_Result],
        htm: typing.Callable[[], T_Result],
        xls: typing.Callable[[], T_Result],
        xlsx: typing.Callable[[], T_Result],
        xlsm: typing.Callable[[], T_Result],
        xlsb: typing.Callable[[], T_Result],
        xlw: typing.Callable[[], T_Result],
        csv: typing.Callable[[], T_Result],
        dif: typing.Callable[[], T_Result],
        sylk: typing.Callable[[], T_Result],
        slk: typing.Callable[[], T_Result],
        prn: typing.Callable[[], T_Result],
        numbers: typing.Callable[[], T_Result],
        et: typing.Callable[[], T_Result],
        ods: typing.Callable[[], T_Result],
        fods: typing.Callable[[], T_Result],
        uos_1: typing.Callable[[], T_Result],
        uos_2: typing.Callable[[], T_Result],
        dbf: typing.Callable[[], T_Result],
        wk_1: typing.Callable[[], T_Result],
        wk_2: typing.Callable[[], T_Result],
        wk_3: typing.Callable[[], T_Result],
        wk_4: typing.Callable[[], T_Result],
        wks: typing.Callable[[], T_Result],
        wq_1: typing.Callable[[], T_Result],
        wq_2: typing.Callable[[], T_Result],
        wb_1: typing.Callable[[], T_Result],
        wb_2: typing.Callable[[], T_Result],
        wb_3: typing.Callable[[], T_Result],
        qpw: typing.Callable[[], T_Result],
        xlr: typing.Callable[[], T_Result],
        eth: typing.Callable[[], T_Result],
        tsv: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is LlamaParseSupportedFileExtensions.PDF:
            return pdf()
        if self is LlamaParseSupportedFileExtensions.DOC:
            return doc()
        if self is LlamaParseSupportedFileExtensions.DOCX:
            return docx()
        if self is LlamaParseSupportedFileExtensions.DOCM:
            return docm()
        if self is LlamaParseSupportedFileExtensions.DOT:
            return dot()
        if self is LlamaParseSupportedFileExtensions.DOTX:
            return dotx()
        if self is LlamaParseSupportedFileExtensions.DOTM:
            return dotm()
        if self is LlamaParseSupportedFileExtensions.RTF:
            return rtf()
        if self is LlamaParseSupportedFileExtensions.WPS:
            return wps()
        if self is LlamaParseSupportedFileExtensions.WPD:
            return wpd()
        if self is LlamaParseSupportedFileExtensions.SXW:
            return sxw()
        if self is LlamaParseSupportedFileExtensions.STW:
            return stw()
        if self is LlamaParseSupportedFileExtensions.SXG:
            return sxg()
        if self is LlamaParseSupportedFileExtensions.PAGES:
            return pages()
        if self is LlamaParseSupportedFileExtensions.MW:
            return mw()
        if self is LlamaParseSupportedFileExtensions.MCW:
            return mcw()
        if self is LlamaParseSupportedFileExtensions.UOT:
            return uot()
        if self is LlamaParseSupportedFileExtensions.UOF:
            return uof()
        if self is LlamaParseSupportedFileExtensions.UOS:
            return uos()
        if self is LlamaParseSupportedFileExtensions.UOP:
            return uop()
        if self is LlamaParseSupportedFileExtensions.PPT:
            return ppt()
        if self is LlamaParseSupportedFileExtensions.PPTX:
            return pptx()
        if self is LlamaParseSupportedFileExtensions.POT:
            return pot()
        if self is LlamaParseSupportedFileExtensions.PPTM:
            return pptm()
        if self is LlamaParseSupportedFileExtensions.POTX:
            return potx()
        if self is LlamaParseSupportedFileExtensions.POTM:
            return potm()
        if self is LlamaParseSupportedFileExtensions.KEY:
            return key()
        if self is LlamaParseSupportedFileExtensions.ODP:
            return odp()
        if self is LlamaParseSupportedFileExtensions.ODG:
            return odg()
        if self is LlamaParseSupportedFileExtensions.OTP:
            return otp()
        if self is LlamaParseSupportedFileExtensions.FOPD:
            return fopd()
        if self is LlamaParseSupportedFileExtensions.SXI:
            return sxi()
        if self is LlamaParseSupportedFileExtensions.STI:
            return sti()
        if self is LlamaParseSupportedFileExtensions.EPUB:
            return epub()
        if self is LlamaParseSupportedFileExtensions.JPG:
            return jpg()
        if self is LlamaParseSupportedFileExtensions.JPEG:
            return jpeg()
        if self is LlamaParseSupportedFileExtensions.PNG:
            return png()
        if self is LlamaParseSupportedFileExtensions.GIF:
            return gif()
        if self is LlamaParseSupportedFileExtensions.BMP:
            return bmp()
        if self is LlamaParseSupportedFileExtensions.SVG:
            return svg()
        if self is LlamaParseSupportedFileExtensions.TIFF:
            return tiff()
        if self is LlamaParseSupportedFileExtensions.WEBP:
            return webp()
        if self is LlamaParseSupportedFileExtensions.HTML:
            return html()
        if self is LlamaParseSupportedFileExtensions.HTM:
            return htm()
        if self is LlamaParseSupportedFileExtensions.XLS:
            return xls()
        if self is LlamaParseSupportedFileExtensions.XLSX:
            return xlsx()
        if self is LlamaParseSupportedFileExtensions.XLSM:
            return xlsm()
        if self is LlamaParseSupportedFileExtensions.XLSB:
            return xlsb()
        if self is LlamaParseSupportedFileExtensions.XLW:
            return xlw()
        if self is LlamaParseSupportedFileExtensions.CSV:
            return csv()
        if self is LlamaParseSupportedFileExtensions.DIF:
            return dif()
        if self is LlamaParseSupportedFileExtensions.SYLK:
            return sylk()
        if self is LlamaParseSupportedFileExtensions.SLK:
            return slk()
        if self is LlamaParseSupportedFileExtensions.PRN:
            return prn()
        if self is LlamaParseSupportedFileExtensions.NUMBERS:
            return numbers()
        if self is LlamaParseSupportedFileExtensions.ET:
            return et()
        if self is LlamaParseSupportedFileExtensions.ODS:
            return ods()
        if self is LlamaParseSupportedFileExtensions.FODS:
            return fods()
        if self is LlamaParseSupportedFileExtensions.UOS_1:
            return uos_1()
        if self is LlamaParseSupportedFileExtensions.UOS_2:
            return uos_2()
        if self is LlamaParseSupportedFileExtensions.DBF:
            return dbf()
        if self is LlamaParseSupportedFileExtensions.WK_1:
            return wk_1()
        if self is LlamaParseSupportedFileExtensions.WK_2:
            return wk_2()
        if self is LlamaParseSupportedFileExtensions.WK_3:
            return wk_3()
        if self is LlamaParseSupportedFileExtensions.WK_4:
            return wk_4()
        if self is LlamaParseSupportedFileExtensions.WKS:
            return wks()
        if self is LlamaParseSupportedFileExtensions.WQ_1:
            return wq_1()
        if self is LlamaParseSupportedFileExtensions.WQ_2:
            return wq_2()
        if self is LlamaParseSupportedFileExtensions.WB_1:
            return wb_1()
        if self is LlamaParseSupportedFileExtensions.WB_2:
            return wb_2()
        if self is LlamaParseSupportedFileExtensions.WB_3:
            return wb_3()
        if self is LlamaParseSupportedFileExtensions.QPW:
            return qpw()
        if self is LlamaParseSupportedFileExtensions.XLR:
            return xlr()
        if self is LlamaParseSupportedFileExtensions.ETH:
            return eth()
        if self is LlamaParseSupportedFileExtensions.TSV:
            return tsv()
