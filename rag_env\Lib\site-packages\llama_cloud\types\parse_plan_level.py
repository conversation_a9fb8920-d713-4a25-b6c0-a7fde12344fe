# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ParsePlanLevel(str, enum.Enum):
    """
    Enum for the Parse plan level.
    """

    DEFAULT = "DEFAULT"
    PREMIUM = "PREMIUM"

    def visit(self, default: typing.Callable[[], T_Result], premium: typing.Callable[[], T_Result]) -> T_Result:
        if self is ParsePlanLevel.DEFAULT:
            return default()
        if self is ParsePlanLevel.PREMIUM:
            return premium()
