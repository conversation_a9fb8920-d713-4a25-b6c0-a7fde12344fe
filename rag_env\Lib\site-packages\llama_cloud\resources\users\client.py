# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
import urllib.parse
from json.decoder import <PERSON>SONDecode<PERSON>rror

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.http_validation_error import HttpValidationError
from ...types.update_user_response import UpdateUserResponse

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class UsersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def update_user(
        self,
        user_id: str,
        *,
        first_name: typing.Optional[str] = OMIT,
        last_name: typing.Optional[str] = OMIT,
        email: typing.Optional[str] = OMIT,
        current_password: typing.Optional[str] = OMIT,
        new_password: typing.Optional[str] = OMIT,
    ) -> UpdateUserResponse:
        """
        Parameters:
            - user_id: str.

            - first_name: typing.Optional[str].

            - last_name: typing.Optional[str].

            - email: typing.Optional[str].

            - current_password: typing.Optional[str].

            - new_password: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.users.update_user(
            user_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if first_name is not OMIT:
            _request["first_name"] = first_name
        if last_name is not OMIT:
            _request["last_name"] = last_name
        if email is not OMIT:
            _request["email"] = email
        if current_password is not OMIT:
            _request["current_password"] = current_password
        if new_password is not OMIT:
            _request["new_password"] = new_password
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/users/{user_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(UpdateUserResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncUsersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def update_user(
        self,
        user_id: str,
        *,
        first_name: typing.Optional[str] = OMIT,
        last_name: typing.Optional[str] = OMIT,
        email: typing.Optional[str] = OMIT,
        current_password: typing.Optional[str] = OMIT,
        new_password: typing.Optional[str] = OMIT,
    ) -> UpdateUserResponse:
        """
        Parameters:
            - user_id: str.

            - first_name: typing.Optional[str].

            - last_name: typing.Optional[str].

            - email: typing.Optional[str].

            - current_password: typing.Optional[str].

            - new_password: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.users.update_user(
            user_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if first_name is not OMIT:
            _request["first_name"] = first_name
        if last_name is not OMIT:
            _request["last_name"] = last_name
        if email is not OMIT:
            _request["email"] = email
        if current_password is not OMIT:
            _request["current_password"] = current_password
        if new_password is not OMIT:
            _request["new_password"] = new_password
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/users/{user_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(UpdateUserResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
