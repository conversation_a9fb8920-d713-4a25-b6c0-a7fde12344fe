# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .azure_open_ai_embedding_config import AzureOpenAiEmbeddingConfig
from .bedrock_embedding_config import BedrockEmbeddingConfig
from .cohere_embedding_config import CohereEmbeddingConfig
from .gemini_embedding_config import GeminiEmbeddingConfig
from .hugging_face_inference_api_embedding_config import HuggingFaceInferenceApiEmbeddingConfig
from .open_ai_embedding_config import OpenAiEmbeddingConfig
from .vertex_ai_embedding_config import VertexAiEmbeddingConfig


class PipelineCreateEmbeddingConfig_AzureEmbedding(AzureOpenAiEmbeddingConfig):
    type: typing_extensions.Literal["AZURE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_BedrockEmbedding(BedrockEmbeddingConfig):
    type: typing_extensions.Literal["BEDROCK_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_CohereEmbedding(CohereEmbeddingConfig):
    type: typing_extensions.Literal["COHERE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_GeminiEmbedding(GeminiEmbeddingConfig):
    type: typing_extensions.Literal["GEMINI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_HuggingfaceApiEmbedding(HuggingFaceInferenceApiEmbeddingConfig):
    type: typing_extensions.Literal["HUGGINGFACE_API_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_OpenaiEmbedding(OpenAiEmbeddingConfig):
    type: typing_extensions.Literal["OPENAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineCreateEmbeddingConfig_VertexaiEmbedding(VertexAiEmbeddingConfig):
    type: typing_extensions.Literal["VERTEXAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


PipelineCreateEmbeddingConfig = typing.Union[
    PipelineCreateEmbeddingConfig_AzureEmbedding,
    PipelineCreateEmbeddingConfig_BedrockEmbedding,
    PipelineCreateEmbeddingConfig_CohereEmbedding,
    PipelineCreateEmbeddingConfig_GeminiEmbedding,
    PipelineCreateEmbeddingConfig_HuggingfaceApiEmbedding,
    PipelineCreateEmbeddingConfig_OpenaiEmbedding,
    PipelineCreateEmbeddingConfig_VertexaiEmbedding,
]
