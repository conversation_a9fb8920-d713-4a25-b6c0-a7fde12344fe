# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .file_parse_public import FileParsePublic

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class BatchItem(pydantic.BaseModel):
    id: str = pydantic.Field(description="Unique identifier for the batch item")
    batch_id: str = pydantic.Field(description="The ID of the batch to which the item belongs")
    status: str = pydantic.Field(description="The current status of the batch item")
    status_updated_at: typing.Optional[dt.datetime]
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    input_file: str = pydantic.Field(description="The input file associated with the batch item")
    output_file: typing.Optional[str]
    task: typing.Optional[FileParsePublic]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
