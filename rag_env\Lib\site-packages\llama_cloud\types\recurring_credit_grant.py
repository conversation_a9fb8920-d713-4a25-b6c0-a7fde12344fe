# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .credit_type import CreditType

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class RecurringCreditGrant(pydantic.BaseModel):
    name: str
    credit_amount: int
    credit_type: CreditType
    product_id: str = pydantic.Field(
        description="The ID of the product in Metronome used to represent the credit grant"
    )
    priority: float
    rollover_fraction: float = pydantic.Field(
        description="The fraction of the credit that will roll over to the next period, between 0 and 1"
    )
    periods_duration: typing.Optional[float] = pydantic.Field(
        description="How many billing periods the credit grant will last for"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
