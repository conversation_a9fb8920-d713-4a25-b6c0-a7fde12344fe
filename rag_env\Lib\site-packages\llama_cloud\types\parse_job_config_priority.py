# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ParseJobConfigPriority(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

    def visit(
        self,
        low: typing.Callable[[], T_Result],
        medium: typing.Callable[[], T_Result],
        high: typing.Callable[[], T_Result],
        critical: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ParseJobConfigPriority.LOW:
            return low()
        if self is ParseJobConfigPriority.MEDIUM:
            return medium()
        if self is ParseJobConfigPriority.HIGH:
            return high()
        if self is ParseJobConfigPriority.CRITICAL:
            return critical()
