"""
Gradio Web Interface for Medical Insurance RAG Agent
"""

import gradio as gr
import time
from pathlib import Path
from dotenv import load_dotenv
import os
from llama_index.core import VectorS<PERSON>Index, SimpleDirectoryReader, Settings, StorageContext, load_index_from_storage
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.chat_engine import SimpleChatEngine
from llama_index.core.memory import ChatMemoryBuffer
from llama_parse import <PERSON>lamaParse
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

# Load environment variables
load_dotenv()

# Configure global settings
Settings.llm = OpenAI(model="gpt-4o-mini", temperature=0.1)
Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-small")

class GradioInsuranceRAG:
    """Gradio-based Insurance RAG Agent."""
    
    def __init__(self, storage_dir="./storage"):
        self.storage_dir = storage_dir
        self.index = None
        self.query_engine = None
        self.chat_engine = None
        self.chat_history = []
        self.load_rag_system()
        
    def load_rag_system(self):
        """Load RAG system."""
        try:
            if Path(self.storage_dir).exists():
                # Load existing index
                storage_context = StorageContext.from_defaults(persist_dir=self.storage_dir)
                self.index = load_index_from_storage(storage_context)
                
                # Create query engine
                self.query_engine = self.index.as_query_engine(
                    similarity_top_k=8,
                    response_mode="compact"
                )
                
                # Create chat engine with memory
                memory = ChatMemoryBuffer.from_defaults(token_limit=3000)
                self.chat_engine = SimpleChatEngine.from_defaults(
                    llm=Settings.llm,
                    memory=memory,
                    system_prompt=(
                        "You are a helpful assistant specializing in medical insurance policies. "
                        "Answer questions about insurance coverage, benefits, exclusions, and procedures. "
                        "Be specific about coverage amounts, time limits, and any conditions."
                    )
                )
                
                return True
            else:
                return False
        except Exception as e:
            print(f"Error loading RAG system: {e}")
            return False
    
    def chat_query(self, message, history):
        """Process chat query with history."""
        if not self.query_engine or not self.chat_engine:
            return history + [("System Error", "RAG system not loaded. Please check your setup.")]
        
        try:
            # Get information using query engine for better retrieval
            start_time = time.time()
            query_response = self.query_engine.query(message)
            
            # Use chat engine with retrieved information
            enhanced_message = f"Based on the insurance policy information: {query_response}\n\nUser question: {message}"
            chat_response = self.chat_engine.chat(enhanced_message)
            
            response_time = time.time() - start_time
            response_text = f"{str(chat_response)}\n\n⏱️ *Response time: {response_time:.2f} seconds*"
            
            # Update history
            history.append((message, response_text))
            return history
            
        except Exception as e:
            error_response = f"Error processing query: {e}"
            history.append((message, error_response))
            return history
    
    def query_with_sources(self, question):
        """Query with source information."""
        if not self.query_engine:
            return "RAG system not loaded. Please check your setup.", "No sources available."
        
        try:
            start_time = time.time()
            response = self.query_engine.query(question)
            response_time = time.time() - start_time
            
            # Format response
            response_text = f"{str(response)}\n\n⏱️ *Response time: {response_time:.2f} seconds*"
            
            # Format sources
            sources_text = ""
            if hasattr(response, 'source_nodes') and response.source_nodes:
                sources_text = "📚 **Sources:**\n\n"
                for i, node in enumerate(response.source_nodes[:3], 1):
                    score = getattr(node, 'score', 0)
                    text_preview = node.text[:200] + "..." if len(node.text) > 200 else node.text
                    sources_text += f"**Source {i}** (Score: {score:.3f})\n{text_preview}\n\n"
            else:
                sources_text = "No sources found."
            
            return response_text, sources_text
            
        except Exception as e:
            return f"Error processing query: {e}", "No sources available."
    
    def reset_chat(self):
        """Reset chat conversation."""
        if hasattr(self.chat_engine, 'reset'):
            self.chat_engine.reset()
        return []
    
    def get_suggested_questions(self):
        """Get list of suggested questions."""
        return [
            "What is my annual dental coverage limit?",
            "What vision benefits do I have?",
            "Are there any deductibles I need to pay?",
            "What are the main exclusions in my policy?",
            "How do I submit a claim?",
            "What happens to my benefits when I retire?",
            "What is covered for prescription drugs?",
            "Do I have travel insurance coverage?"
        ]

# Initialize RAG agent
rag_agent = GradioInsuranceRAG()

def create_interface():
    """Create Gradio interface."""
    
    # Custom CSS
    css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    """
    
    with gr.Blocks(css=css, title="Medical Insurance RAG Agent") as interface:
        
        # Header
        gr.HTML("""
        <div class="header">
            <h1>🏥 Medical Insurance RAG Agent</h1>
            <p>Ask questions about your medical insurance policy and get accurate, contextual answers</p>
        </div>
        """)
        
        # System status
        if rag_agent.index:
            gr.HTML("<div style='text-align: center; color: green;'>✅ RAG System Loaded Successfully</div>")
        else:
            gr.HTML("<div style='text-align: center; color: red;'>❌ RAG System Not Loaded - Please run query.py first</div>")
        
        with gr.Tabs():
            
            # Chat Tab
            with gr.TabItem("💬 Chat Mode"):
                gr.Markdown("### Interactive chat with conversation memory")
                
                chatbot = gr.Chatbot(
                    height=400,
                    label="Insurance Assistant",
                    show_label=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        placeholder="Ask about your insurance policy...",
                        label="Your Question",
                        scale=4
                    )
                    submit_btn = gr.Button("Send", variant="primary", scale=1)
                    clear_btn = gr.Button("Clear Chat", variant="secondary", scale=1)
                
                # Suggested questions for chat
                gr.Markdown("### 💡 Suggested Questions (Click to use)")
                with gr.Row():
                    for i in range(0, 4):
                        question = rag_agent.get_suggested_questions()[i]
                        btn = gr.Button(question, size="sm")
                        btn.click(lambda q=question: q, outputs=msg)
                
                with gr.Row():
                    for i in range(4, 8):
                        question = rag_agent.get_suggested_questions()[i]
                        btn = gr.Button(question, size="sm")
                        btn.click(lambda q=question: q, outputs=msg)
                
                # Chat functionality
                def respond(message, history):
                    return rag_agent.chat_query(message, history)
                
                submit_btn.click(respond, [msg, chatbot], [chatbot])
                msg.submit(respond, [msg, chatbot], [chatbot])
                clear_btn.click(lambda: rag_agent.reset_chat(), outputs=chatbot)
            
            # Query with Sources Tab
            with gr.TabItem("🔍 Query with Sources"):
                gr.Markdown("### Single query with source citations")
                
                with gr.Row():
                    query_input = gr.Textbox(
                        placeholder="What is my dental coverage?",
                        label="Your Question",
                        scale=4
                    )
                    query_btn = gr.Button("Search", variant="primary", scale=1)
                
                with gr.Row():
                    with gr.Column(scale=2):
                        response_output = gr.Markdown(label="Response")
                    with gr.Column(scale=1):
                        sources_output = gr.Markdown(label="Sources")
                
                # Suggested questions for query
                gr.Markdown("### 💡 Quick Questions")
                with gr.Row():
                    for question in rag_agent.get_suggested_questions()[:4]:
                        btn = gr.Button(question, size="sm")
                        btn.click(lambda q=question: q, outputs=query_input)
                
                # Query functionality
                query_btn.click(
                    rag_agent.query_with_sources,
                    inputs=query_input,
                    outputs=[response_output, sources_output]
                )
                query_input.submit(
                    rag_agent.query_with_sources,
                    inputs=query_input,
                    outputs=[response_output, sources_output]
                )
            
            # System Info Tab
            with gr.TabItem("ℹ️ System Info"):
                gr.Markdown("### System Information")
                
                system_info = f"""
                **System Status:** {'🟢 Online' if rag_agent.index else '🔴 Offline'}
                
                **Components:**
                - **PDF Processing:** LlamaParse
                - **Language Model:** OpenAI GPT-4o-mini
                - **Embeddings:** OpenAI text-embedding-3-small
                - **Vector Store:** LlamaIndex VectorStoreIndex
                - **Storage:** {'💾 Persistent' if Path(rag_agent.storage_dir).exists() else '❌ Not Found'}
                
                **Features:**
                - ✅ Conversation memory in Chat Mode
                - ✅ Source citations in Query Mode
                - ✅ Suggested questions
                - ✅ Response time tracking
                
                **Usage Tips:**
                - Use **Chat Mode** for conversational queries with context
                - Use **Query with Sources** to see document citations
                - Click suggested questions for quick access
                - Be specific in your questions for best results
                
                **About:**
                This RAG (Retrieval-Augmented Generation) agent is designed specifically for 
                medical insurance policy queries. It uses advanced PDF processing and vector 
                search to provide accurate, contextual answers with source attribution.
                """
                
                gr.Markdown(system_info)
        
        # Footer
        gr.HTML("""
        <div style='text-align: center; margin-top: 20px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;'>
            <small>Medical Insurance RAG Agent | Built with LlamaIndex, OpenAI, and Gradio</small>
        </div>
        """)
    
    return interface

def main():
    """Launch Gradio interface."""
    interface = create_interface()
    
    # Launch with custom settings
    interface.launch(
        server_name="0.0.0.0",  # Allow external access
        server_port=7860,       # Default Gradio port
        share=False,            # Set to True for public sharing
        debug=False,
        show_error=True
    )

if __name__ == "__main__":
    main()
