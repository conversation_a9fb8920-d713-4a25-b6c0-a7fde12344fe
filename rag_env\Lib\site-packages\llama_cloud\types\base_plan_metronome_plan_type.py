# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class BasePlanMetronomePlanType(str, enum.Enum):
    PLAN = "plan"
    CONTRACT = "contract"

    def visit(self, plan: typing.Callable[[], T_Result], contract: typing.Callable[[], T_Result]) -> T_Result:
        if self is BasePlanMetronomePlanType.PLAN:
            return plan()
        if self is BasePlanMetronomePlanType.CONTRACT:
            return contract()
