# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class RetrievalMode(str, enum.Enum):
    CHUNKS = "chunks"
    FILES_VIA_METADATA = "files_via_metadata"
    FILES_VIA_CONTENT = "files_via_content"
    AUTO_ROUTED = "auto_routed"

    def visit(
        self,
        chunks: typing.Callable[[], T_Result],
        files_via_metadata: typing.Callable[[], T_Result],
        files_via_content: typing.Callable[[], T_Result],
        auto_routed: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is RetrievalMode.CHUNKS:
            return chunks()
        if self is RetrievalMode.FILES_VIA_METADATA:
            return files_via_metadata()
        if self is RetrievalMode.FILES_VIA_CONTENT:
            return files_via_content()
        if self is RetrievalMode.AUTO_ROUTED:
            return auto_routed()
