# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ReportBlockDependency(str, enum.Enum):
    NONE = "none"
    ALL = "all"
    PREVIOUS = "previous"
    NEXT = "next"

    def visit(
        self,
        none: typing.Callable[[], T_Result],
        all: typing.Callable[[], T_Result],
        previous: typing.Callable[[], T_Result],
        next: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ReportBlockDependency.NONE:
            return none()
        if self is ReportBlockDependency.ALL:
            return all()
        if self is ReportBlockDependency.PREVIOUS:
            return previous()
        if self is ReportBlockDependency.NEXT:
            return next()
