# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .character_chunking_config import CharacterChunkingConfig
from .none_chunking_config import NoneChunkingConfig
from .semantic_chunking_config import SemanticChunkingConfig
from .sentence_chunking_config import SentenceChunkingConfig
from .token_chunking_config import TokenChunkingConfig


class AdvancedModeTransformConfigChunkingConfig_None(NoneChunkingConfig):
    mode: typing_extensions.Literal["none"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigChunkingConfig_Character(CharacterChunkingConfig):
    mode: typing_extensions.Literal["character"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigChunkingConfig_Token(TokenChunkingConfig):
    mode: typing_extensions.Literal["token"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigChunkingConfig_Sentence(SentenceChunkingConfig):
    mode: typing_extensions.Literal["sentence"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class AdvancedModeTransformConfigChunkingConfig_Semantic(SemanticChunkingConfig):
    mode: typing_extensions.Literal["semantic"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


AdvancedModeTransformConfigChunkingConfig = typing.Union[
    AdvancedModeTransformConfigChunkingConfig_None,
    AdvancedModeTransformConfigChunkingConfig_Character,
    AdvancedModeTransformConfigChunkingConfig_Token,
    AdvancedModeTransformConfigChunkingConfig_Sentence,
    AdvancedModeTransformConfigChunkingConfig_Semantic,
]
