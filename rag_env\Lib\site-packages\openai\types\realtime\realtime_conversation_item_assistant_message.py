# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["RealtimeConversationItemAssistantMessage", "Content"]


class Content(BaseModel):
    audio: Optional[str] = None
    """
    Base64-encoded audio bytes, these will be parsed as the format specified in the
    session output audio type configuration. This defaults to PCM 16-bit 24kHz mono
    if not specified.
    """

    text: Optional[str] = None
    """The text content."""

    transcript: Optional[str] = None
    """
    The transcript of the audio content, this will always be present if the output
    type is `audio`.
    """

    type: Optional[Literal["output_text", "output_audio"]] = None
    """
    The content type, `output_text` or `output_audio` depending on the session
    `output_modalities` configuration.
    """


class RealtimeConversationItemAssistantMessage(BaseModel):
    content: List[Content]
    """The content of the message."""

    role: Literal["assistant"]
    """The role of the message sender. Always `assistant`."""

    type: Literal["message"]
    """The type of the item. Always `message`."""

    id: Optional[str] = None
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    object: Optional[Literal["realtime.item"]] = None
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Optional[Literal["completed", "incomplete", "in_progress"]] = None
    """The status of the item. Has no effect on the conversation."""
