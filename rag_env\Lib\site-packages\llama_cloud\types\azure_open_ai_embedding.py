# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class AzureOpenAiEmbedding(pydantic.BaseModel):
    model_name: typing.Optional[str] = pydantic.Field(description="The name of the OpenAI embedding model.")
    embed_batch_size: typing.Optional[int] = pydantic.Field(description="The batch size for embedding calls.")
    num_workers: typing.Optional[int]
    additional_kwargs: typing.Optional[typing.Dict[str, typing.Any]] = pydantic.Field(
        description="Additional kwargs for the OpenAI API."
    )
    api_key: typing.Optional[str]
    api_base: typing.Optional[str] = pydantic.Field(description="The base URL for Azure deployment.")
    api_version: typing.Optional[str] = pydantic.Field(description="The version for Azure OpenAI API.")
    max_retries: typing.Optional[int] = pydantic.Field(description="Maximum number of retries.")
    timeout: typing.Optional[float] = pydantic.Field(description="Timeout for each request.")
    default_headers: typing.Optional[typing.Dict[str, typing.Optional[str]]]
    reuse_client: typing.Optional[bool] = pydantic.Field(
        description="Reuse the OpenAI client between requests. When doing anything with large volumes of async API calls, setting this to false can improve stability."
    )
    dimensions: typing.Optional[int]
    azure_endpoint: typing.Optional[str]
    azure_deployment: typing.Optional[str]
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
