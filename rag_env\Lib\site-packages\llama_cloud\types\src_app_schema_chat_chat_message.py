# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .message_annotation import MessageAnnotation
from .message_role import MessageRole

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class SrcAppSchemaChatChatMessage(pydantic.BaseModel):
    id: str
    index: int = pydantic.Field(description="The index of the message in the chat.")
    annotations: typing.Optional[typing.List[MessageAnnotation]] = pydantic.Field(
        description="Retrieval annotations for the message."
    )
    role: MessageRole = pydantic.Field(description="The role of the message.")
    content: typing.Optional[str]
    additional_kwargs: typing.Optional[typing.Dict[str, str]] = pydantic.Field(
        description="Additional arguments passed to the model"
    )
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
