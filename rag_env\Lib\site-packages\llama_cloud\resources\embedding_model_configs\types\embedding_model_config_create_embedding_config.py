# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from ....types.azure_open_ai_embedding_config import AzureOpenAiEmbeddingConfig
from ....types.bedrock_embedding_config import BedrockEmbeddingConfig
from ....types.cohere_embedding_config import CohereEmbeddingConfig
from ....types.gemini_embedding_config import GeminiEmbeddingConfig
from ....types.hugging_face_inference_api_embedding_config import HuggingFaceInferenceApiEmbeddingConfig
from ....types.open_ai_embedding_config import OpenAiEmbeddingConfig
from ....types.vertex_ai_embedding_config import VertexAiEmbeddingConfig


class EmbeddingModelConfigCreateEmbeddingConfig_AzureEmbedding(AzureOpenAiEmbeddingConfig):
    type: typing_extensions.Literal["AZURE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_BedrockEmbedding(BedrockEmbeddingConfig):
    type: typing_extensions.Literal["BEDROCK_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_CohereEmbedding(CohereEmbeddingConfig):
    type: typing_extensions.Literal["COHERE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_GeminiEmbedding(GeminiEmbeddingConfig):
    type: typing_extensions.Literal["GEMINI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_HuggingfaceApiEmbedding(HuggingFaceInferenceApiEmbeddingConfig):
    type: typing_extensions.Literal["HUGGINGFACE_API_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_OpenaiEmbedding(OpenAiEmbeddingConfig):
    type: typing_extensions.Literal["OPENAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class EmbeddingModelConfigCreateEmbeddingConfig_VertexaiEmbedding(VertexAiEmbeddingConfig):
    type: typing_extensions.Literal["VERTEXAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


EmbeddingModelConfigCreateEmbeddingConfig = typing.Union[
    EmbeddingModelConfigCreateEmbeddingConfig_AzureEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_BedrockEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_CohereEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_GeminiEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_HuggingfaceApiEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_OpenaiEmbedding,
    EmbeddingModelConfigCreateEmbeddingConfig_VertexaiEmbedding,
]
