# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ManagedIngestionStatus(str, enum.Enum):
    """
    Status of managed ingestion with partial Updates.
    """

    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    PARTIAL_SUCCESS = "PARTIAL_SUCCESS"
    CANCELLED = "CANCELLED"

    def visit(
        self,
        not_started: typing.Callable[[], T_Result],
        in_progress: typing.Callable[[], T_Result],
        success: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
        partial_success: typing.Callable[[], T_Result],
        cancelled: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ManagedIngestionStatus.NOT_STARTED:
            return not_started()
        if self is ManagedIngestionStatus.IN_PROGRESS:
            return in_progress()
        if self is ManagedIngestionStatus.SUCCESS:
            return success()
        if self is ManagedIngestionStatus.ERROR:
            return error()
        if self is ManagedIngestionStatus.PARTIAL_SUCCESS:
            return partial_success()
        if self is ManagedIngestionStatus.CANCELLED:
            return cancelled()
