# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["RealtimeConversationItemUserMessage", "Content"]


class Content(BaseModel):
    audio: Optional[str] = None
    """
    Base64-encoded audio bytes (for `input_audio`), these will be parsed as the
    format specified in the session input audio type configuration. This defaults to
    PCM 16-bit 24kHz mono if not specified.
    """

    detail: Optional[Literal["auto", "low", "high"]] = None
    """The detail level of the image (for `input_image`).

    `auto` will default to `high`.
    """

    image_url: Optional[str] = None
    """Base64-encoded image bytes (for `input_image`) as a data URI.

    For example `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...`. Supported
    formats are PNG and JPEG.
    """

    text: Optional[str] = None
    """The text content (for `input_text`)."""

    transcript: Optional[str] = None
    """Transcript of the audio (for `input_audio`).

    This is not sent to the model, but will be attached to the message item for
    reference.
    """

    type: Optional[Literal["input_text", "input_audio", "input_image"]] = None
    """The content type (`input_text`, `input_audio`, or `input_image`)."""


class RealtimeConversationItemUserMessage(BaseModel):
    content: List[Content]
    """The content of the message."""

    role: Literal["user"]
    """The role of the message sender. Always `user`."""

    type: Literal["message"]
    """The type of the item. Always `message`."""

    id: Optional[str] = None
    """The unique ID of the item.

    This may be provided by the client or generated by the server.
    """

    object: Optional[Literal["realtime.item"]] = None
    """Identifier for the API object being returned - always `realtime.item`.

    Optional when creating a new item.
    """

    status: Optional[Literal["completed", "incomplete", "in_progress"]] = None
    """The status of the item. Has no effect on the conversation."""
