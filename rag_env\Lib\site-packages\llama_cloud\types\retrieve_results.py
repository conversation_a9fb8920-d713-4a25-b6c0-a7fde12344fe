# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .metadata_filters import MetadataFilters
from .page_figure_node_with_score import PageFigureNodeWithScore
from .page_screenshot_node_with_score import PageScreenshotNodeWithScore
from .text_node_with_score import TextNodeWithScore

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class RetrieveResults(pydantic.BaseModel):
    """
    Schema for the result of an retrieval execution.
    """

    pipeline_id: str = pydantic.Field(description="The ID of the pipeline that the query was retrieved against.")
    retrieval_nodes: typing.List[TextNodeWithScore] = pydantic.Field(
        description="The nodes retrieved by the pipeline for the given query."
    )
    image_nodes: typing.Optional[typing.List[PageScreenshotNodeWithScore]] = pydantic.Field(
        description="The image nodes retrieved by the pipeline for the given query. Deprecated - will soon be replaced with 'page_screenshot_nodes'."
    )
    page_figure_nodes: typing.Optional[typing.List[PageFigureNodeWithScore]] = pydantic.Field(
        description="The page figure nodes retrieved by the pipeline for the given query."
    )
    retrieval_latency: typing.Optional[typing.Dict[str, float]] = pydantic.Field(
        description="The end-to-end latency for retrieval and reranking."
    )
    metadata: typing.Optional[typing.Dict[str, str]] = pydantic.Field(
        description="Metadata associated with the retrieval execution"
    )
    inferred_search_filters: typing.Optional[MetadataFilters]
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
