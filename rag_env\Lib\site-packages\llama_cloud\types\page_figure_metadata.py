# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PageFigureMetadata(pydantic.BaseModel):
    figure_name: str = pydantic.Field(description="The name of the figure")
    file_id: str = pydantic.Field(description="The ID of the file that the figure was taken from")
    page_index: int = pydantic.Field(description="The index of the page for which the figure is taken (0-indexed)")
    figure_size: int = pydantic.Field(description="The size of the figure in bytes")
    is_likely_noise: typing.Optional[bool] = pydantic.Field(description="Whether the figure is likely to be noise")
    confidence: float = pydantic.Field(description="The confidence of the figure")
    metadata: typing.Optional[typing.Dict[str, typing.Any]]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
