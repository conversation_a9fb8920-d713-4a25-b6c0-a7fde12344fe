# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .llm_parameters import LlmParameters
from .preset_retrieval_params import PresetRetrievalParams
from .src_app_schema_chat_chat_message import SrcAppSchemaChatChatMessage

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PlaygroundSession(pydantic.BaseModel):
    """
    A playground session for a user.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    pipeline_id: str
    user_id: str
    llm_params_id: str
    llm_params: typing.Optional[LlmParameters] = pydantic.Field(description="LLM parameters last used in this session.")
    retrieval_params_id: str
    retrieval_params: typing.Optional[PresetRetrievalParams] = pydantic.Field(
        description="Preset retrieval parameters last used in this session."
    )
    chat_messages: typing.Optional[typing.List[SrcAppSchemaChatChatMessage]] = pydantic.Field(
        description="Chat message history for this session."
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
