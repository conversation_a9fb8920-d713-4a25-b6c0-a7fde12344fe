# This file was auto-generated by <PERSON>rn from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ReportState(str, enum.Enum):
    PENDING = "pending"
    PLANNING = "planning"
    WAITING_APPROVAL = "waiting_approval"
    GENERATING = "generating"
    COMPLETED = "completed"
    ERROR = "error"

    def visit(
        self,
        pending: typing.Callable[[], T_Result],
        planning: typing.Callable[[], T_Result],
        waiting_approval: typing.Callable[[], T_Result],
        generating: typing.Callable[[], T_Result],
        completed: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ReportState.PENDING:
            return pending()
        if self is ReportState.PLANNING:
            return planning()
        if self is ReportState.WAITING_APPROVAL:
            return waiting_approval()
        if self is ReportState.GENERATING:
            return generating()
        if self is ReportState.COMPLETED:
            return completed()
        if self is ReportState.ERROR:
            return error()
