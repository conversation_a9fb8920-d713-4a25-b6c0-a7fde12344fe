# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
import urllib.parse
from json.decoder import <PERSON><PERSON>NDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import AsyncClientWrapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...core.remove_none_from_dict import remove_none_from_dict
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.composite_retrieval_mode import CompositeRetrievalMode
from ...types.composite_retrieval_result import CompositeRetrievalResult
from ...types.http_validation_error import HttpValidationError
from ...types.re_rank_config import ReRankConfig
from ...types.retriever import Retriever
from ...types.retriever_create import RetrieverCreate
from ...types.retriever_pipeline import RetrieverPipeline

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RetrieversClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list_retrievers(
        self,
        *,
        name: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> typing.List[Retriever]:
        """
        List Retrievers for a project.

        Parameters:
            - name: typing.Optional[str].

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.list_retrievers()
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"name": name, "project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[Retriever], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_retriever(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: RetrieverCreate,
    ) -> Retriever:
        """
        Create a new Retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: RetrieverCreate.
        ---
        from llama_cloud import RetrieverCreate
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.create_retriever(
            request=RetrieverCreate(
                name="string",
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def upsert_retriever(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: RetrieverCreate,
    ) -> Retriever:
        """
        Upsert a new Retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: RetrieverCreate.
        ---
        from llama_cloud import RetrieverCreate
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.upsert_retriever(
            request=RetrieverCreate(
                name="string",
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_retriever(
        self,
        retriever_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> Retriever:
        """
        Get a Retriever by ID.

        Parameters:
            - retriever_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.get_retriever(
            retriever_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_retriever(
        self,
        retriever_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        pipelines: typing.Optional[typing.List[RetrieverPipeline]] = OMIT,
    ) -> Retriever:
        """
        Update an existing Retriever.

        Parameters:
            - retriever_id: str.

            - name: typing.Optional[str].

            - pipelines: typing.Optional[typing.List[RetrieverPipeline]].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.update_retriever(
            retriever_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if name is not OMIT:
            _request["name"] = name
        if pipelines is not OMIT:
            _request["pipelines"] = pipelines
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_retriever(self, retriever_id: str) -> None:
        """
        Delete a Retriever by ID.

        Parameters:
            - retriever_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.delete_retriever(
            retriever_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def retrieve(
        self,
        retriever_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        mode: typing.Optional[CompositeRetrievalMode] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        rerank_config: typing.Optional[ReRankConfig] = OMIT,
        query: str,
    ) -> CompositeRetrievalResult:
        """
        Retrieve data using a Retriever.

        Parameters:
            - retriever_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - mode: typing.Optional[CompositeRetrievalMode]. The mode of composite retrieval.

            - rerank_top_n: typing.Optional[int].

            - rerank_config: typing.Optional[ReRankConfig]. The rerank configuration for composite retrieval.

            - query: str. The query to retrieve against.
        ---
        from llama_cloud import CompositeRetrievalMode, ReRankConfig, ReRankerType
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.retrieve(
            retriever_id="string",
            mode=CompositeRetrievalMode.ROUTING,
            rerank_config=ReRankConfig(
                type=ReRankerType.SYSTEM_DEFAULT,
            ),
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if mode is not OMIT:
            _request["mode"] = mode
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if rerank_config is not OMIT:
            _request["rerank_config"] = rerank_config
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}/retrieve"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CompositeRetrievalResult, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def direct_retrieve(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        mode: typing.Optional[CompositeRetrievalMode] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        rerank_config: typing.Optional[ReRankConfig] = OMIT,
        query: str,
        pipelines: typing.Optional[typing.List[RetrieverPipeline]] = OMIT,
    ) -> CompositeRetrievalResult:
        """
        Retrieve data using specified pipelines without creating a persistent retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - mode: typing.Optional[CompositeRetrievalMode]. The mode of composite retrieval.

            - rerank_top_n: typing.Optional[int].

            - rerank_config: typing.Optional[ReRankConfig]. The rerank configuration for composite retrieval.

            - query: str. The query to retrieve against.

            - pipelines: typing.Optional[typing.List[RetrieverPipeline]]. The pipelines to use for retrieval.
        ---
        from llama_cloud import CompositeRetrievalMode, ReRankConfig, ReRankerType
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.retrievers.direct_retrieve(
            mode=CompositeRetrievalMode.ROUTING,
            rerank_config=ReRankConfig(
                type=ReRankerType.SYSTEM_DEFAULT,
            ),
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if mode is not OMIT:
            _request["mode"] = mode
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if rerank_config is not OMIT:
            _request["rerank_config"] = rerank_config
        if pipelines is not OMIT:
            _request["pipelines"] = pipelines
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers/retrieve"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CompositeRetrievalResult, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncRetrieversClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list_retrievers(
        self,
        *,
        name: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> typing.List[Retriever]:
        """
        List Retrievers for a project.

        Parameters:
            - name: typing.Optional[str].

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.list_retrievers()
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"name": name, "project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[Retriever], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_retriever(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: RetrieverCreate,
    ) -> Retriever:
        """
        Create a new Retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: RetrieverCreate.
        ---
        from llama_cloud import RetrieverCreate
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.create_retriever(
            request=RetrieverCreate(
                name="string",
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def upsert_retriever(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: RetrieverCreate,
    ) -> Retriever:
        """
        Upsert a new Retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: RetrieverCreate.
        ---
        from llama_cloud import RetrieverCreate
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.upsert_retriever(
            request=RetrieverCreate(
                name="string",
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_retriever(
        self,
        retriever_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> Retriever:
        """
        Get a Retriever by ID.

        Parameters:
            - retriever_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.get_retriever(
            retriever_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_retriever(
        self,
        retriever_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        pipelines: typing.Optional[typing.List[RetrieverPipeline]] = OMIT,
    ) -> Retriever:
        """
        Update an existing Retriever.

        Parameters:
            - retriever_id: str.

            - name: typing.Optional[str].

            - pipelines: typing.Optional[typing.List[RetrieverPipeline]].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.update_retriever(
            retriever_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if name is not OMIT:
            _request["name"] = name
        if pipelines is not OMIT:
            _request["pipelines"] = pipelines
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Retriever, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_retriever(self, retriever_id: str) -> None:
        """
        Delete a Retriever by ID.

        Parameters:
            - retriever_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.delete_retriever(
            retriever_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def retrieve(
        self,
        retriever_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        mode: typing.Optional[CompositeRetrievalMode] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        rerank_config: typing.Optional[ReRankConfig] = OMIT,
        query: str,
    ) -> CompositeRetrievalResult:
        """
        Retrieve data using a Retriever.

        Parameters:
            - retriever_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - mode: typing.Optional[CompositeRetrievalMode]. The mode of composite retrieval.

            - rerank_top_n: typing.Optional[int].

            - rerank_config: typing.Optional[ReRankConfig]. The rerank configuration for composite retrieval.

            - query: str. The query to retrieve against.
        ---
        from llama_cloud import CompositeRetrievalMode, ReRankConfig, ReRankerType
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.retrieve(
            retriever_id="string",
            mode=CompositeRetrievalMode.ROUTING,
            rerank_config=ReRankConfig(
                type=ReRankerType.SYSTEM_DEFAULT,
            ),
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if mode is not OMIT:
            _request["mode"] = mode
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if rerank_config is not OMIT:
            _request["rerank_config"] = rerank_config
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/retrievers/{retriever_id}/retrieve"
            ),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CompositeRetrievalResult, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def direct_retrieve(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        mode: typing.Optional[CompositeRetrievalMode] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        rerank_config: typing.Optional[ReRankConfig] = OMIT,
        query: str,
        pipelines: typing.Optional[typing.List[RetrieverPipeline]] = OMIT,
    ) -> CompositeRetrievalResult:
        """
        Retrieve data using specified pipelines without creating a persistent retriever.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - mode: typing.Optional[CompositeRetrievalMode]. The mode of composite retrieval.

            - rerank_top_n: typing.Optional[int].

            - rerank_config: typing.Optional[ReRankConfig]. The rerank configuration for composite retrieval.

            - query: str. The query to retrieve against.

            - pipelines: typing.Optional[typing.List[RetrieverPipeline]]. The pipelines to use for retrieval.
        ---
        from llama_cloud import CompositeRetrievalMode, ReRankConfig, ReRankerType
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.retrievers.direct_retrieve(
            mode=CompositeRetrievalMode.ROUTING,
            rerank_config=ReRankConfig(
                type=ReRankerType.SYSTEM_DEFAULT,
            ),
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if mode is not OMIT:
            _request["mode"] = mode
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if rerank_config is not OMIT:
            _request["rerank_config"] = rerank_config
        if pipelines is not OMIT:
            _request["pipelines"] = pipelines
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/retrievers/retrieve"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CompositeRetrievalResult, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
