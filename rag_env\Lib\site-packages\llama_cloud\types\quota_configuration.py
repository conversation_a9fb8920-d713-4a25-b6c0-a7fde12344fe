# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

import typing_extensions

from ..core.datetime_utils import serialize_datetime
from .quota_configuration_configuration_type import QuotaConfigurationConfigurationType
from .quota_configuration_status import QuotaConfigurationStatus
from .quota_rate_limit_configuration_value import QuotaRateLimitConfigurationValue

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class QuotaConfiguration(pydantic.BaseModel):
    """
    Full quota configuration model.
    """

    source_type: typing_extensions.Literal["organization"]
    source_id: str = pydantic.Field(description="The source ID, e.g. the organization ID")
    configuration_type: QuotaConfigurationConfigurationType = pydantic.Field(description="The quota configuration type")
    configuration_value: QuotaRateLimitConfigurationValue = pydantic.Field(description="The quota configuration value")
    configuration_metadata: typing.Optional[typing.Dict[str, typing.Any]]
    started_at: typing.Optional[dt.datetime] = pydantic.Field(description="The start date of the quota")
    ended_at: typing.Optional[dt.datetime]
    idempotency_key: typing.Optional[str]
    status: QuotaConfigurationStatus = pydantic.Field(
        description="The status of the quota, i.e. 'ACTIVE' or 'INACTIVE'"
    )
    id: typing.Optional[str]
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
