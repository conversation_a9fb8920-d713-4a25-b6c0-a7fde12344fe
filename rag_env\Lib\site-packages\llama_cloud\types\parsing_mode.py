# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ParsingMode(str, enum.Enum):
    """
    Enum for representing the mode of parsing to be used
    """

    PARSE_PAGE_WITHOUT_LLM = "parse_page_without_llm"
    PARSE_PAGE_WITH_LLM = "parse_page_with_llm"
    PARSE_PAGE_WITH_LVM = "parse_page_with_lvm"
    PARSE_PAGE_WITH_AGENT = "parse_page_with_agent"
    PARSE_PAGE_WITH_LAYOUT_AGENT = "parse_page_with_layout_agent"
    PARSE_DOCUMENT_WITH_LLM = "parse_document_with_llm"
    PARSE_DOCUMENT_WITH_LVM = "parse_document_with_lvm"
    PARSE_DOCUMENT_WITH_AGENT = "parse_document_with_agent"

    def visit(
        self,
        parse_page_without_llm: typing.Callable[[], T_Result],
        parse_page_with_llm: typing.Callable[[], T_Result],
        parse_page_with_lvm: typing.Callable[[], T_Result],
        parse_page_with_agent: typing.Callable[[], T_Result],
        parse_page_with_layout_agent: typing.Callable[[], T_Result],
        parse_document_with_llm: typing.Callable[[], T_Result],
        parse_document_with_lvm: typing.Callable[[], T_Result],
        parse_document_with_agent: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ParsingMode.PARSE_PAGE_WITHOUT_LLM:
            return parse_page_without_llm()
        if self is ParsingMode.PARSE_PAGE_WITH_LLM:
            return parse_page_with_llm()
        if self is ParsingMode.PARSE_PAGE_WITH_LVM:
            return parse_page_with_lvm()
        if self is ParsingMode.PARSE_PAGE_WITH_AGENT:
            return parse_page_with_agent()
        if self is ParsingMode.PARSE_PAGE_WITH_LAYOUT_AGENT:
            return parse_page_with_layout_agent()
        if self is ParsingMode.PARSE_DOCUMENT_WITH_LLM:
            return parse_document_with_llm()
        if self is ParsingMode.PARSE_DOCUMENT_WITH_LVM:
            return parse_document_with_lvm()
        if self is ParsingMode.PARSE_DOCUMENT_WITH_AGENT:
            return parse_document_with_agent()
