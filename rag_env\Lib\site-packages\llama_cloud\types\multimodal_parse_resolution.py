# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class MultimodalParseResolution(str, enum.Enum):
    MEDIUM = "medium"
    HIGH = "high"

    def visit(self, medium: typing.Callable[[], T_Result], high: typing.Callable[[], T_Result]) -> T_Result:
        if self is MultimodalParseResolution.MEDIUM:
            return medium()
        if self is MultimodalParseResolution.HIGH:
            return high()
