# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
import urllib.parse
from json.decoder import J<PERSON>NDecodeError

import typing_extensions

from ...core.api_error import ApiError
from ...core.client_wrapper import AsyncClientWrapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...core.remove_none_from_dict import remove_none_from_dict
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.agent_data import AgentData
from ...types.batch import Batch
from ...types.batch_paginated_list import BatchPaginatedList
from ...types.batch_public_output import BatchPublicOutput
from ...types.filter_operation import FilterOperation
from ...types.http_validation_error import HttpValidationError
from ...types.llama_parse_parameters import LlamaParseParameters
from ...types.paginated_response_agent_data import PaginatedResponseAgentData
from ...types.paginated_response_aggregate_group import PaginatedResponseAggregateGroup
from ...types.paginated_response_quota_configuration import PaginatedResponseQuotaConfiguration

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class BetaClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list_batches(
        self,
        *,
        limit: typing.Optional[int] = None,
        offset: typing.Optional[int] = None,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> BatchPaginatedList:
        """
        Parameters:
            - limit: typing.Optional[int].

            - offset: typing.Optional[int].

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.list_batches()
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/batches"),
            params=remove_none_from_dict(
                {"limit": limit, "offset": offset, "project_id": project_id, "organization_id": organization_id}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(BatchPaginatedList, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_batch(
        self,
        *,
        organization_id: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        tool: str,
        tool_data: typing.Optional[LlamaParseParameters] = OMIT,
        input_type: str,
        input_id: str,
        output_type: typing.Optional[str] = OMIT,
        output_id: typing.Optional[str] = OMIT,
        batch_create_project_id: str,
        external_id: str,
        completion_window: typing.Optional[int] = OMIT,
    ) -> Batch:
        """
        Parameters:
            - organization_id: typing.Optional[str].

            - project_id: typing.Optional[str].

            - tool: str. The tool to be used for all requests in the batch.

            - tool_data: typing.Optional[LlamaParseParameters].

            - input_type: str. The type of input file. Currently only 'datasource' is supported.

            - input_id: str. The ID of the input file for the batch.

            - output_type: typing.Optional[str].

            - output_id: typing.Optional[str].

            - batch_create_project_id: str. The ID of the project to which the batch belongs

            - external_id: str. A developer-provided ID for the batch. This ID will be returned in the response.

            - completion_window: typing.Optional[int]. The time frame within which the batch should be processed. Currently only 24h is supported.
        ---
        from llama_cloud import (
            FailPageMode,
            LlamaParseParameters,
            LlamaParseParametersPriority,
            ParsingMode,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.create_batch(
            tool="string",
            tool_data=LlamaParseParameters(
                priority=LlamaParseParametersPriority.LOW,
                parse_mode=ParsingMode.PARSE_PAGE_WITHOUT_LLM,
                replace_failed_page_mode=FailPageMode.RAW_TEXT,
            ),
            input_type="string",
            input_id="string",
            batch_create_project_id="string",
            external_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {
            "tool": tool,
            "input_type": input_type,
            "input_id": input_id,
            "project_id": batch_create_project_id,
            "external_id": external_id,
        }
        if tool_data is not OMIT:
            _request["tool_data"] = tool_data
        if output_type is not OMIT:
            _request["output_type"] = output_type
        if output_id is not OMIT:
            _request["output_id"] = output_id
        if completion_window is not OMIT:
            _request["completion_window"] = completion_window
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/batches"),
            params=remove_none_from_dict({"organization_id": organization_id, "project_id": project_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Batch, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_batch(self, batch_id: str, *, organization_id: typing.Optional[str] = None) -> BatchPublicOutput:
        """
        Parameters:
            - batch_id: str.

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.get_batch(
            batch_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/batches/{batch_id}"),
            params=remove_none_from_dict({"organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(BatchPublicOutput, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_agent_data(
        self, item_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> AgentData:
        """
        Get agent data by ID.

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.get_agent_data(
            item_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_agent_data(
        self,
        item_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        data: typing.Dict[str, typing.Any],
    ) -> AgentData:
        """
        Update agent data by ID (overwrites).

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - data: typing.Dict[str, typing.Any].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.update_agent_data(
            item_id="string",
            data={"string": {}},
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder({"data": data}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_agent_data(
        self, item_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.Dict[str, str]:
        """
        Delete agent data by ID.

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.delete_agent_data(
            item_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Dict[str, str], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_agent_data(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        data: typing.Dict[str, typing.Any],
    ) -> AgentData:
        """
        Create new agent data.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - agent_slug: str.

            - collection: typing.Optional[str].

            - data: typing.Dict[str, typing.Any].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.create_agent_data(
            agent_slug="string",
            data={"string": {}},
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug, "data": data}
        if collection is not OMIT:
            _request["collection"] = collection
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def search_agent_data_api_v_1_beta_agent_data_search_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        page_size: typing.Optional[int] = OMIT,
        page_token: typing.Optional[str] = OMIT,
        filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]] = OMIT,
        order_by: typing.Optional[str] = OMIT,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        include_total: typing.Optional[bool] = OMIT,
        offset: typing.Optional[int] = OMIT,
    ) -> PaginatedResponseAgentData:
        """
        Search agent data with filtering, sorting, and pagination.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - page_size: typing.Optional[int].

            - page_token: typing.Optional[str].

            - filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]].

            - order_by: typing.Optional[str].

            - agent_slug: str. The agent deployment's agent_slug to search within

            - collection: typing.Optional[str]. The logical agent data collection to search within

            - include_total: typing.Optional[bool]. Whether to include the total number of items in the response

            - offset: typing.Optional[int].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.search_agent_data_api_v_1_beta_agent_data_search_post(
            agent_slug="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug}
        if page_size is not OMIT:
            _request["page_size"] = page_size
        if page_token is not OMIT:
            _request["page_token"] = page_token
        if filter is not OMIT:
            _request["filter"] = filter
        if order_by is not OMIT:
            _request["order_by"] = order_by
        if collection is not OMIT:
            _request["collection"] = collection
        if include_total is not OMIT:
            _request["include_total"] = include_total
        if offset is not OMIT:
            _request["offset"] = offset
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data/:search"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseAgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def aggregate_agent_data_api_v_1_beta_agent_data_aggregate_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        page_size: typing.Optional[int] = OMIT,
        page_token: typing.Optional[str] = OMIT,
        filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]] = OMIT,
        order_by: typing.Optional[str] = OMIT,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        group_by: typing.Optional[typing.List[str]] = OMIT,
        count: typing.Optional[bool] = OMIT,
        first: typing.Optional[bool] = OMIT,
        offset: typing.Optional[int] = OMIT,
    ) -> PaginatedResponseAggregateGroup:
        """
        Aggregate agent data with grouping and optional counting/first item retrieval.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - page_size: typing.Optional[int].

            - page_token: typing.Optional[str].

            - filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]].

            - order_by: typing.Optional[str].

            - agent_slug: str. The agent deployment's agent_slug to aggregate data for

            - collection: typing.Optional[str]. The logical agent data collection to aggregate data for

            - group_by: typing.Optional[typing.List[str]].

            - count: typing.Optional[bool].

            - first: typing.Optional[bool].

            - offset: typing.Optional[int].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.aggregate_agent_data_api_v_1_beta_agent_data_aggregate_post(
            agent_slug="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug}
        if page_size is not OMIT:
            _request["page_size"] = page_size
        if page_token is not OMIT:
            _request["page_token"] = page_token
        if filter is not OMIT:
            _request["filter"] = filter
        if order_by is not OMIT:
            _request["order_by"] = order_by
        if collection is not OMIT:
            _request["collection"] = collection
        if group_by is not OMIT:
            _request["group_by"] = group_by
        if count is not OMIT:
            _request["count"] = count
        if first is not OMIT:
            _request["first"] = first
        if offset is not OMIT:
            _request["offset"] = offset
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data/:aggregate"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseAggregateGroup, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_quota_configurations(
        self,
        *,
        source_type: typing_extensions.Literal["organization"],
        source_id: str,
        page: typing.Optional[int] = None,
        page_size: typing.Optional[int] = None,
    ) -> PaginatedResponseQuotaConfiguration:
        """
        Retrieve a paginated list of quota configurations with optional filtering.

        Parameters:
            - source_type: typing_extensions.Literal["organization"].

            - source_id: str.

            - page: typing.Optional[int].

            - page_size: typing.Optional[int].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.beta.list_quota_configurations(
            source_type="organization",
            source_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/quota-management"),
            params=remove_none_from_dict(
                {"source_type": source_type, "source_id": source_id, "page": page, "page_size": page_size}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseQuotaConfiguration, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncBetaClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list_batches(
        self,
        *,
        limit: typing.Optional[int] = None,
        offset: typing.Optional[int] = None,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
    ) -> BatchPaginatedList:
        """
        Parameters:
            - limit: typing.Optional[int].

            - offset: typing.Optional[int].

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.list_batches()
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/batches"),
            params=remove_none_from_dict(
                {"limit": limit, "offset": offset, "project_id": project_id, "organization_id": organization_id}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(BatchPaginatedList, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_batch(
        self,
        *,
        organization_id: typing.Optional[str] = None,
        project_id: typing.Optional[str] = None,
        tool: str,
        tool_data: typing.Optional[LlamaParseParameters] = OMIT,
        input_type: str,
        input_id: str,
        output_type: typing.Optional[str] = OMIT,
        output_id: typing.Optional[str] = OMIT,
        batch_create_project_id: str,
        external_id: str,
        completion_window: typing.Optional[int] = OMIT,
    ) -> Batch:
        """
        Parameters:
            - organization_id: typing.Optional[str].

            - project_id: typing.Optional[str].

            - tool: str. The tool to be used for all requests in the batch.

            - tool_data: typing.Optional[LlamaParseParameters].

            - input_type: str. The type of input file. Currently only 'datasource' is supported.

            - input_id: str. The ID of the input file for the batch.

            - output_type: typing.Optional[str].

            - output_id: typing.Optional[str].

            - batch_create_project_id: str. The ID of the project to which the batch belongs

            - external_id: str. A developer-provided ID for the batch. This ID will be returned in the response.

            - completion_window: typing.Optional[int]. The time frame within which the batch should be processed. Currently only 24h is supported.
        ---
        from llama_cloud import (
            FailPageMode,
            LlamaParseParameters,
            LlamaParseParametersPriority,
            ParsingMode,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.create_batch(
            tool="string",
            tool_data=LlamaParseParameters(
                priority=LlamaParseParametersPriority.LOW,
                parse_mode=ParsingMode.PARSE_PAGE_WITHOUT_LLM,
                replace_failed_page_mode=FailPageMode.RAW_TEXT,
            ),
            input_type="string",
            input_id="string",
            batch_create_project_id="string",
            external_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {
            "tool": tool,
            "input_type": input_type,
            "input_id": input_id,
            "project_id": batch_create_project_id,
            "external_id": external_id,
        }
        if tool_data is not OMIT:
            _request["tool_data"] = tool_data
        if output_type is not OMIT:
            _request["output_type"] = output_type
        if output_id is not OMIT:
            _request["output_id"] = output_id
        if completion_window is not OMIT:
            _request["completion_window"] = completion_window
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/batches"),
            params=remove_none_from_dict({"organization_id": organization_id, "project_id": project_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Batch, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_batch(self, batch_id: str, *, organization_id: typing.Optional[str] = None) -> BatchPublicOutput:
        """
        Parameters:
            - batch_id: str.

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.get_batch(
            batch_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/batches/{batch_id}"),
            params=remove_none_from_dict({"organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(BatchPublicOutput, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_agent_data(
        self, item_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> AgentData:
        """
        Get agent data by ID.

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.get_agent_data(
            item_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_agent_data(
        self,
        item_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        data: typing.Dict[str, typing.Any],
    ) -> AgentData:
        """
        Update agent data by ID (overwrites).

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - data: typing.Dict[str, typing.Any].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.update_agent_data(
            item_id="string",
            data={"string": {}},
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder({"data": data}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_agent_data(
        self, item_id: str, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.Dict[str, str]:
        """
        Delete agent data by ID.

        Parameters:
            - item_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.delete_agent_data(
            item_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/beta/agent-data/{item_id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Dict[str, str], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_agent_data(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        data: typing.Dict[str, typing.Any],
    ) -> AgentData:
        """
        Create new agent data.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - agent_slug: str.

            - collection: typing.Optional[str].

            - data: typing.Dict[str, typing.Any].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.create_agent_data(
            agent_slug="string",
            data={"string": {}},
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug, "data": data}
        if collection is not OMIT:
            _request["collection"] = collection
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(AgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def search_agent_data_api_v_1_beta_agent_data_search_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        page_size: typing.Optional[int] = OMIT,
        page_token: typing.Optional[str] = OMIT,
        filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]] = OMIT,
        order_by: typing.Optional[str] = OMIT,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        include_total: typing.Optional[bool] = OMIT,
        offset: typing.Optional[int] = OMIT,
    ) -> PaginatedResponseAgentData:
        """
        Search agent data with filtering, sorting, and pagination.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - page_size: typing.Optional[int].

            - page_token: typing.Optional[str].

            - filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]].

            - order_by: typing.Optional[str].

            - agent_slug: str. The agent deployment's agent_slug to search within

            - collection: typing.Optional[str]. The logical agent data collection to search within

            - include_total: typing.Optional[bool]. Whether to include the total number of items in the response

            - offset: typing.Optional[int].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.search_agent_data_api_v_1_beta_agent_data_search_post(
            agent_slug="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug}
        if page_size is not OMIT:
            _request["page_size"] = page_size
        if page_token is not OMIT:
            _request["page_token"] = page_token
        if filter is not OMIT:
            _request["filter"] = filter
        if order_by is not OMIT:
            _request["order_by"] = order_by
        if collection is not OMIT:
            _request["collection"] = collection
        if include_total is not OMIT:
            _request["include_total"] = include_total
        if offset is not OMIT:
            _request["offset"] = offset
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data/:search"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseAgentData, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def aggregate_agent_data_api_v_1_beta_agent_data_aggregate_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        page_size: typing.Optional[int] = OMIT,
        page_token: typing.Optional[str] = OMIT,
        filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]] = OMIT,
        order_by: typing.Optional[str] = OMIT,
        agent_slug: str,
        collection: typing.Optional[str] = OMIT,
        group_by: typing.Optional[typing.List[str]] = OMIT,
        count: typing.Optional[bool] = OMIT,
        first: typing.Optional[bool] = OMIT,
        offset: typing.Optional[int] = OMIT,
    ) -> PaginatedResponseAggregateGroup:
        """
        Aggregate agent data with grouping and optional counting/first item retrieval.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - page_size: typing.Optional[int].

            - page_token: typing.Optional[str].

            - filter: typing.Optional[typing.Dict[str, typing.Optional[FilterOperation]]].

            - order_by: typing.Optional[str].

            - agent_slug: str. The agent deployment's agent_slug to aggregate data for

            - collection: typing.Optional[str]. The logical agent data collection to aggregate data for

            - group_by: typing.Optional[typing.List[str]].

            - count: typing.Optional[bool].

            - first: typing.Optional[bool].

            - offset: typing.Optional[int].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.aggregate_agent_data_api_v_1_beta_agent_data_aggregate_post(
            agent_slug="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"agent_slug": agent_slug}
        if page_size is not OMIT:
            _request["page_size"] = page_size
        if page_token is not OMIT:
            _request["page_token"] = page_token
        if filter is not OMIT:
            _request["filter"] = filter
        if order_by is not OMIT:
            _request["order_by"] = order_by
        if collection is not OMIT:
            _request["collection"] = collection
        if group_by is not OMIT:
            _request["group_by"] = group_by
        if count is not OMIT:
            _request["count"] = count
        if first is not OMIT:
            _request["first"] = first
        if offset is not OMIT:
            _request["offset"] = offset
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/agent-data/:aggregate"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseAggregateGroup, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_quota_configurations(
        self,
        *,
        source_type: typing_extensions.Literal["organization"],
        source_id: str,
        page: typing.Optional[int] = None,
        page_size: typing.Optional[int] = None,
    ) -> PaginatedResponseQuotaConfiguration:
        """
        Retrieve a paginated list of quota configurations with optional filtering.

        Parameters:
            - source_type: typing_extensions.Literal["organization"].

            - source_id: str.

            - page: typing.Optional[int].

            - page_size: typing.Optional[int].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.beta.list_quota_configurations(
            source_type="organization",
            source_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/beta/quota-management"),
            params=remove_none_from_dict(
                {"source_type": source_type, "source_id": source_id, "page": page, "page_size": page_size}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedResponseQuotaConfiguration, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
