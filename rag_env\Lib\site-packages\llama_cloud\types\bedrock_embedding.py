# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class BedrockEmbedding(pydantic.BaseModel):
    model_name: typing.Optional[str] = pydantic.Field(description="The modelId of the Bedrock model to use.")
    embed_batch_size: typing.Optional[int] = pydantic.Field(description="The batch size for embedding calls.")
    num_workers: typing.Optional[int]
    profile_name: typing.Optional[str]
    aws_access_key_id: typing.Optional[str]
    aws_secret_access_key: typing.Optional[str]
    aws_session_token: typing.Optional[str]
    region_name: typing.Optional[str]
    max_retries: typing.Optional[int] = pydantic.Field(description="The maximum number of API retries.")
    timeout: typing.Optional[float] = pydantic.Field(
        description="The timeout for the Bedrock API request in seconds. It will be used for both connect and read timeouts."
    )
    additional_kwargs: typing.Optional[typing.Dict[str, typing.Any]] = pydantic.Field(
        description="Additional kwargs for the bedrock client."
    )
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
