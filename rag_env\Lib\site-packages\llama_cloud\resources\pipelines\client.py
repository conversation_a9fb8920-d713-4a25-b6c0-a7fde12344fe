# This file was auto-generated by <PERSON>rn from our API Definition.

import typing
import urllib.parse
from json.decoder import JSONDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lientWrapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...core.remove_none_from_dict import remove_none_from_dict
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.chat_data import ChatData
from ...types.cloud_document import CloudDocument
from ...types.cloud_document_create import CloudDocumentCreate
from ...types.data_sink_create import DataSinkCreate
from ...types.eval_execution_params import EvalExecutionParams
from ...types.file_count_by_status_response import FileCountByStatusResponse
from ...types.http_validation_error import HttpValidationError
from ...types.input_message import InputMessage
from ...types.llama_parse_parameters import LlamaParseParameters
from ...types.managed_ingestion_status_response import ManagedIngestionStatusResponse
from ...types.metadata_filters import MetadataFilters
from ...types.paginated_list_cloud_documents_response import PaginatedListCloudDocumentsResponse
from ...types.paginated_list_pipeline_files_response import PaginatedListPipelineFilesResponse
from ...types.pipeline import Pipeline
from ...types.pipeline_create import PipelineCreate
from ...types.pipeline_data_source import PipelineDataSource
from ...types.pipeline_data_source_create import PipelineDataSourceCreate
from ...types.pipeline_deployment import PipelineDeployment
from ...types.pipeline_file import PipelineFile
from ...types.pipeline_file_create import PipelineFileCreate
from ...types.pipeline_metadata_config import PipelineMetadataConfig
from ...types.pipeline_type import PipelineType
from ...types.playground_session import PlaygroundSession
from ...types.preset_retrieval_params import PresetRetrievalParams
from ...types.retrieval_mode import RetrievalMode
from ...types.retrieve_results import RetrieveResults
from ...types.text_node import TextNode
from .types.pipeline_file_update_custom_metadata_value import PipelineFileUpdateCustomMetadataValue
from .types.pipeline_update_embedding_config import PipelineUpdateEmbeddingConfig
from .types.pipeline_update_transform_config import PipelineUpdateTransformConfig
from .types.retrieval_params_search_filters_inference_schema_value import (
    RetrievalParamsSearchFiltersInferenceSchemaValue,
)

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PipelinesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def search_pipelines(
        self,
        *,
        project_id: typing.Optional[str] = None,
        project_name: typing.Optional[str] = None,
        pipeline_name: typing.Optional[str] = None,
        pipeline_type: typing.Optional[PipelineType] = None,
        organization_id: typing.Optional[str] = None,
    ) -> typing.List[Pipeline]:
        """
        Search for pipelines by various parameters.

        Parameters:
            - project_id: typing.Optional[str].

            - project_name: typing.Optional[str].

            - pipeline_name: typing.Optional[str].

            - pipeline_type: typing.Optional[PipelineType].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud import PipelineType
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.search_pipelines(
            pipeline_type=PipelineType.PLAYGROUND,
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict(
                {
                    "project_id": project_id,
                    "project_name": project_name,
                    "pipeline_name": pipeline_name,
                    "pipeline_type": pipeline_type,
                    "organization_id": organization_id,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[Pipeline], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_pipeline(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: PipelineCreate,
    ) -> Pipeline:
        """
        Create a new pipeline for a project.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: PipelineCreate.
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def upsert_pipeline(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: PipelineCreate,
    ) -> Pipeline:
        """
        Upsert a pipeline for a project.
        Updates if a pipeline with the same name and project_id already exists. Otherwise, creates a new pipeline.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: PipelineCreate.
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Get a pipeline by ID for a given project.

        Parameters:
            - pipeline_id: str.
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_existing_pipeline(
        self,
        pipeline_id: str,
        *,
        embedding_config: typing.Optional[PipelineUpdateEmbeddingConfig] = OMIT,
        transform_config: typing.Optional[PipelineUpdateTransformConfig] = OMIT,
        data_sink_id: typing.Optional[str] = OMIT,
        embedding_model_config_id: typing.Optional[str] = OMIT,
        data_sink: typing.Optional[DataSinkCreate] = OMIT,
        preset_retrieval_parameters: typing.Optional[PresetRetrievalParams] = OMIT,
        eval_parameters: typing.Optional[EvalExecutionParams] = OMIT,
        llama_parse_parameters: typing.Optional[LlamaParseParameters] = OMIT,
        status: typing.Optional[str] = OMIT,
        metadata_config: typing.Optional[PipelineMetadataConfig] = OMIT,
        name: typing.Optional[str] = OMIT,
        managed_pipeline_id: typing.Optional[str] = OMIT,
    ) -> Pipeline:
        """
        Update an existing pipeline for a project.

        Parameters:
            - pipeline_id: str.

            - embedding_config: typing.Optional[PipelineUpdateEmbeddingConfig].

            - transform_config: typing.Optional[PipelineUpdateTransformConfig]. Configuration for the transformation.

            - data_sink_id: typing.Optional[str].

            - embedding_model_config_id: typing.Optional[str].

            - data_sink: typing.Optional[DataSinkCreate].

            - preset_retrieval_parameters: typing.Optional[PresetRetrievalParams].

            - eval_parameters: typing.Optional[EvalExecutionParams].

            - llama_parse_parameters: typing.Optional[LlamaParseParameters].

            - status: typing.Optional[str].

            - metadata_config: typing.Optional[PipelineMetadataConfig].

            - name: typing.Optional[str].

            - managed_pipeline_id: typing.Optional[str].
        """
        _request: typing.Dict[str, typing.Any] = {}
        if embedding_config is not OMIT:
            _request["embedding_config"] = embedding_config
        if transform_config is not OMIT:
            _request["transform_config"] = transform_config
        if data_sink_id is not OMIT:
            _request["data_sink_id"] = data_sink_id
        if embedding_model_config_id is not OMIT:
            _request["embedding_model_config_id"] = embedding_model_config_id
        if data_sink is not OMIT:
            _request["data_sink"] = data_sink
        if preset_retrieval_parameters is not OMIT:
            _request["preset_retrieval_parameters"] = preset_retrieval_parameters
        if eval_parameters is not OMIT:
            _request["eval_parameters"] = eval_parameters
        if llama_parse_parameters is not OMIT:
            _request["llama_parse_parameters"] = llama_parse_parameters
        if status is not OMIT:
            _request["status"] = status
        if metadata_config is not OMIT:
            _request["metadata_config"] = metadata_config
        if name is not OMIT:
            _request["name"] = name
        if managed_pipeline_id is not OMIT:
            _request["managed_pipeline_id"] = managed_pipeline_id
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_pipeline(self, pipeline_id: str) -> None:
        """
        Delete a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.delete_pipeline(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_status(self, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Get the status of a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_status(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/status"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def sync_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Run ingestion for the pipeline by incrementally updating the data-sink with upstream changes from data-sources & files.

        Parameters:
            - pipeline_id: str.
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/sync"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def cancel_pipeline_sync(self, pipeline_id: str) -> Pipeline:
        """
        Parameters:
            - pipeline_id: str.
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/sync/cancel"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def force_delete_pipeline(self, pipeline_id: str) -> None:
        """
        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.force_delete_pipeline(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/force-delete"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def copy_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Copy a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/copy"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_files(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
    ) -> typing.List[PipelineFile]:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_files(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files"),
            params=remove_none_from_dict(
                {"data_source_id": data_source_id, "only_manually_uploaded": only_manually_uploaded}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineFile], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_files_to_pipeline_api(
        self, pipeline_id: str, *, request: typing.List[PipelineFileCreate]
    ) -> typing.List[PipelineFile]:
        """
        Add files to a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[PipelineFileCreate].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.add_files_to_pipeline_api(
            pipeline_id="string",
            request=[],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files"),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineFile], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_files_2(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
        limit: typing.Optional[int] = None,
        offset: typing.Optional[int] = None,
        order_by: typing.Optional[str] = None,
    ) -> PaginatedListPipelineFilesResponse:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].

            - limit: typing.Optional[int].

            - offset: typing.Optional[int].

            - order_by: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_files_2(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files2"),
            params=remove_none_from_dict(
                {
                    "data_source_id": data_source_id,
                    "only_manually_uploaded": only_manually_uploaded,
                    "limit": limit,
                    "offset": offset,
                    "order_by": order_by,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedListPipelineFilesResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_file_status_counts(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
    ) -> FileCountByStatusResponse:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_file_status_counts(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/status-counts"
            ),
            params=remove_none_from_dict(
                {"data_source_id": data_source_id, "only_manually_uploaded": only_manually_uploaded}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(FileCountByStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_file_status(self, file_id: str, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Get status of a file for a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_file_status(
            file_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}/status"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_pipeline_file(
        self,
        file_id: str,
        pipeline_id: str,
        *,
        custom_metadata: typing.Optional[
            typing.Dict[str, typing.Optional[PipelineFileUpdateCustomMetadataValue]]
        ] = OMIT,
    ) -> PipelineFile:
        """
        Update a file for a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.

            - custom_metadata: typing.Optional[typing.Dict[str, typing.Optional[PipelineFileUpdateCustomMetadataValue]]].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.update_pipeline_file(
            file_id="string",
            pipeline_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if custom_metadata is not OMIT:
            _request["custom_metadata"] = custom_metadata
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}"
            ),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineFile, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_pipeline_file(self, file_id: str, pipeline_id: str) -> None:
        """
        Delete a file from a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.delete_pipeline_file(
            file_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def import_pipeline_metadata(self, pipeline_id: str, *, upload_file: typing.IO) -> typing.Dict[str, str]:
        """
        Import metadata for a pipeline.

        Parameters:
            - pipeline_id: str.

            - upload_file: typing.IO.
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/metadata"),
            data=jsonable_encoder({}),
            files={"upload_file": upload_file},
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Dict[str, str], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_pipeline_files_metadata(self, pipeline_id: str) -> None:
        """
        Delete metadata for all files in a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.delete_pipeline_files_metadata(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/metadata"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_data_sources(self, pipeline_id: str) -> typing.List[PipelineDataSource]:
        """
        Get data sources for a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_data_sources(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/data-sources"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDataSource], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def add_data_sources_to_pipeline(
        self, pipeline_id: str, *, request: typing.List[PipelineDataSourceCreate]
    ) -> typing.List[PipelineDataSource]:
        """
        Add data sources to a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[PipelineDataSourceCreate].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.add_data_sources_to_pipeline(
            pipeline_id="string",
            request=[],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/data-sources"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDataSource], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_pipeline_data_source(
        self, data_source_id: str, pipeline_id: str, *, sync_interval: typing.Optional[float] = OMIT
    ) -> PipelineDataSource:
        """
        Update the configuration of a data source in a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.

            - sync_interval: typing.Optional[float].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.update_pipeline_data_source(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if sync_interval is not OMIT:
            _request["sync_interval"] = sync_interval
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
            ),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineDataSource, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_pipeline_data_source(self, data_source_id: str, pipeline_id: str) -> None:
        """
        Delete a data source from a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.delete_pipeline_data_source(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def sync_pipeline_data_source(self, data_source_id: str, pipeline_id: str) -> Pipeline:
        """
        Run ingestion for the pipeline data source by incrementally updating the data-sink with upstream changes from data-source.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/sync",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_data_source_status(self, data_source_id: str, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Get the status of a data source for a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_data_source_status(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/status",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def run_search(
        self,
        pipeline_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        dense_similarity_top_k: typing.Optional[int] = OMIT,
        dense_similarity_cutoff: typing.Optional[float] = OMIT,
        sparse_similarity_top_k: typing.Optional[int] = OMIT,
        enable_reranking: typing.Optional[bool] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        alpha: typing.Optional[float] = OMIT,
        search_filters: typing.Optional[MetadataFilters] = OMIT,
        search_filters_inference_schema: typing.Optional[
            typing.Dict[str, typing.Optional[RetrievalParamsSearchFiltersInferenceSchemaValue]]
        ] = OMIT,
        files_top_k: typing.Optional[int] = OMIT,
        retrieval_mode: typing.Optional[RetrievalMode] = OMIT,
        retrieve_image_nodes: typing.Optional[bool] = OMIT,
        retrieve_page_screenshot_nodes: typing.Optional[bool] = OMIT,
        retrieve_page_figure_nodes: typing.Optional[bool] = OMIT,
        query: str,
        class_name: typing.Optional[str] = OMIT,
    ) -> RetrieveResults:
        """
        Get retrieval results for a managed pipeline and a query

        Parameters:
            - pipeline_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - dense_similarity_top_k: typing.Optional[int].

            - dense_similarity_cutoff: typing.Optional[float].

            - sparse_similarity_top_k: typing.Optional[int].

            - enable_reranking: typing.Optional[bool].

            - rerank_top_n: typing.Optional[int].

            - alpha: typing.Optional[float].

            - search_filters: typing.Optional[MetadataFilters].

            - search_filters_inference_schema: typing.Optional[typing.Dict[str, typing.Optional[RetrievalParamsSearchFiltersInferenceSchemaValue]]].

            - files_top_k: typing.Optional[int].

            - retrieval_mode: typing.Optional[RetrievalMode]. The retrieval mode for the query.

            - retrieve_image_nodes: typing.Optional[bool]. Whether to retrieve image nodes.

            - retrieve_page_screenshot_nodes: typing.Optional[bool]. Whether to retrieve page screenshot nodes.

            - retrieve_page_figure_nodes: typing.Optional[bool]. Whether to retrieve page figure nodes.

            - query: str. The query to retrieve against.

            - class_name: typing.Optional[str].
        ---
        from llama_cloud import FilterCondition, MetadataFilters, RetrievalMode
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.run_search(
            pipeline_id="string",
            search_filters=MetadataFilters(
                filters=[],
                condition=FilterCondition.AND,
            ),
            retrieval_mode=RetrievalMode.CHUNKS,
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if dense_similarity_top_k is not OMIT:
            _request["dense_similarity_top_k"] = dense_similarity_top_k
        if dense_similarity_cutoff is not OMIT:
            _request["dense_similarity_cutoff"] = dense_similarity_cutoff
        if sparse_similarity_top_k is not OMIT:
            _request["sparse_similarity_top_k"] = sparse_similarity_top_k
        if enable_reranking is not OMIT:
            _request["enable_reranking"] = enable_reranking
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if alpha is not OMIT:
            _request["alpha"] = alpha
        if search_filters is not OMIT:
            _request["search_filters"] = search_filters
        if search_filters_inference_schema is not OMIT:
            _request["search_filters_inference_schema"] = search_filters_inference_schema
        if files_top_k is not OMIT:
            _request["files_top_k"] = files_top_k
        if retrieval_mode is not OMIT:
            _request["retrieval_mode"] = retrieval_mode
        if retrieve_image_nodes is not OMIT:
            _request["retrieve_image_nodes"] = retrieve_image_nodes
        if retrieve_page_screenshot_nodes is not OMIT:
            _request["retrieve_page_screenshot_nodes"] = retrieve_page_screenshot_nodes
        if retrieve_page_figure_nodes is not OMIT:
            _request["retrieve_page_figure_nodes"] = retrieve_page_figure_nodes
        if class_name is not OMIT:
            _request["class_name"] = class_name
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/retrieve"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(RetrieveResults, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_jobs(self, pipeline_id: str) -> typing.List[PipelineDeployment]:
        """
        Get jobs for a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_jobs(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/jobs"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDeployment], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_job(self, job_id: str, pipeline_id: str) -> PipelineDeployment:
        """
        Get a job for a pipeline.

        Parameters:
            - job_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_job(
            job_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/jobs/{job_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineDeployment, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_playground_session(self, pipeline_id: str) -> PlaygroundSession:
        """
        Get a playground session for a user and pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_playground_session(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/playground-session"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PlaygroundSession, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def chat(
        self,
        pipeline_id: str,
        *,
        messages: typing.Optional[typing.List[InputMessage]] = OMIT,
        data: typing.Optional[ChatData] = OMIT,
        class_name: typing.Optional[str] = OMIT,
    ) -> typing.Any:
        """
        Make a retrieval query + chat completion for a managed pipeline.

        Parameters:
            - pipeline_id: str.

            - messages: typing.Optional[typing.List[InputMessage]].

            - data: typing.Optional[ChatData].

            - class_name: typing.Optional[str].
        ---
        from llama_cloud import (
            ChatData,
            FilterCondition,
            LlmParameters,
            MetadataFilters,
            PresetRetrievalParams,
            RetrievalMode,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.chat(
            pipeline_id="string",
            data=ChatData(
                retrieval_parameters=PresetRetrievalParams(
                    search_filters=MetadataFilters(
                        filters=[],
                        condition=FilterCondition.AND,
                    ),
                    retrieval_mode=RetrievalMode.CHUNKS,
                ),
                llm_parameters=LlmParameters(
                    model_name=SupportedLlmModelNames.GPT_4_O,
                ),
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if messages is not OMIT:
            _request["messages"] = messages
        if data is not OMIT:
            _request["data"] = data
        if class_name is not OMIT:
            _request["class_name"] = class_name
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/chat"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_documents(
        self,
        pipeline_id: str,
        *,
        skip: typing.Optional[int] = None,
        limit: typing.Optional[int] = None,
        file_id: typing.Optional[str] = None,
        only_direct_upload: typing.Optional[bool] = None,
        only_api_data_source_documents: typing.Optional[bool] = None,
    ) -> typing.List[CloudDocument]:
        """
        Return a list of documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].

            - file_id: typing.Optional[str].

            - only_direct_upload: typing.Optional[bool].

            - only_api_data_source_documents: typing.Optional[bool].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_documents(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            params=remove_none_from_dict(
                {
                    "skip": skip,
                    "limit": limit,
                    "file_id": file_id,
                    "only_direct_upload": only_direct_upload,
                    "only_api_data_source_documents": only_api_data_source_documents,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_batch_pipeline_documents(
        self, pipeline_id: str, *, request: typing.List[CloudDocumentCreate]
    ) -> typing.List[CloudDocument]:
        """
        Batch create documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[CloudDocumentCreate].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.create_batch_pipeline_documents(
            pipeline_id="string",
            request=[],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def upsert_batch_pipeline_documents(
        self, pipeline_id: str, *, request: typing.List[CloudDocumentCreate]
    ) -> typing.List[CloudDocument]:
        """
        Batch create or update a document for a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[CloudDocumentCreate].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.upsert_batch_pipeline_documents(
            pipeline_id="string",
            request=[],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def paginated_list_pipeline_documents(
        self,
        pipeline_id: str,
        *,
        skip: typing.Optional[int] = None,
        limit: typing.Optional[int] = None,
        file_id: typing.Optional[str] = None,
        only_direct_upload: typing.Optional[bool] = None,
        only_api_data_source_documents: typing.Optional[bool] = None,
    ) -> PaginatedListCloudDocumentsResponse:
        """
        Return a list of documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].

            - file_id: typing.Optional[str].

            - only_direct_upload: typing.Optional[bool].

            - only_api_data_source_documents: typing.Optional[bool].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.paginated_list_pipeline_documents(
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/paginated"
            ),
            params=remove_none_from_dict(
                {
                    "skip": skip,
                    "limit": limit,
                    "file_id": file_id,
                    "only_direct_upload": only_direct_upload,
                    "only_api_data_source_documents": only_api_data_source_documents,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedListCloudDocumentsResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_document(self, document_id: str, pipeline_id: str) -> CloudDocument:
        """
        Return a single document for a pipeline.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_document(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/{document_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CloudDocument, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_pipeline_document(self, document_id: str, pipeline_id: str) -> None:
        """
        Delete a document from a pipeline.
        Initiates an async job that will:

        1. Delete vectors from the vector store
        2. Delete the document from MongoDB after vectors are successfully deleted

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.delete_pipeline_document(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/{document_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_pipeline_document_status(self, document_id: str, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Return a single document for a pipeline.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.get_pipeline_document_status(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/documents/{document_id}/status",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def list_pipeline_document_chunks(self, document_id: str, pipeline_id: str) -> typing.List[TextNode]:
        """
        Return a list of chunks for a pipeline document.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.pipelines.list_pipeline_document_chunks(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/documents/{document_id}/chunks",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[TextNode], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncPipelinesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def search_pipelines(
        self,
        *,
        project_id: typing.Optional[str] = None,
        project_name: typing.Optional[str] = None,
        pipeline_name: typing.Optional[str] = None,
        pipeline_type: typing.Optional[PipelineType] = None,
        organization_id: typing.Optional[str] = None,
    ) -> typing.List[Pipeline]:
        """
        Search for pipelines by various parameters.

        Parameters:
            - project_id: typing.Optional[str].

            - project_name: typing.Optional[str].

            - pipeline_name: typing.Optional[str].

            - pipeline_type: typing.Optional[PipelineType].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud import PipelineType
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.search_pipelines(
            pipeline_type=PipelineType.PLAYGROUND,
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict(
                {
                    "project_id": project_id,
                    "project_name": project_name,
                    "pipeline_name": pipeline_name,
                    "pipeline_type": pipeline_type,
                    "organization_id": organization_id,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[Pipeline], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_pipeline(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: PipelineCreate,
    ) -> Pipeline:
        """
        Create a new pipeline for a project.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: PipelineCreate.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def upsert_pipeline(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        request: PipelineCreate,
    ) -> Pipeline:
        """
        Upsert a pipeline for a project.
        Updates if a pipeline with the same name and project_id already exists. Otherwise, creates a new pipeline.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - request: PipelineCreate.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/pipelines"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Get a pipeline by ID for a given project.

        Parameters:
            - pipeline_id: str.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_existing_pipeline(
        self,
        pipeline_id: str,
        *,
        embedding_config: typing.Optional[PipelineUpdateEmbeddingConfig] = OMIT,
        transform_config: typing.Optional[PipelineUpdateTransformConfig] = OMIT,
        data_sink_id: typing.Optional[str] = OMIT,
        embedding_model_config_id: typing.Optional[str] = OMIT,
        data_sink: typing.Optional[DataSinkCreate] = OMIT,
        preset_retrieval_parameters: typing.Optional[PresetRetrievalParams] = OMIT,
        eval_parameters: typing.Optional[EvalExecutionParams] = OMIT,
        llama_parse_parameters: typing.Optional[LlamaParseParameters] = OMIT,
        status: typing.Optional[str] = OMIT,
        metadata_config: typing.Optional[PipelineMetadataConfig] = OMIT,
        name: typing.Optional[str] = OMIT,
        managed_pipeline_id: typing.Optional[str] = OMIT,
    ) -> Pipeline:
        """
        Update an existing pipeline for a project.

        Parameters:
            - pipeline_id: str.

            - embedding_config: typing.Optional[PipelineUpdateEmbeddingConfig].

            - transform_config: typing.Optional[PipelineUpdateTransformConfig]. Configuration for the transformation.

            - data_sink_id: typing.Optional[str].

            - embedding_model_config_id: typing.Optional[str].

            - data_sink: typing.Optional[DataSinkCreate].

            - preset_retrieval_parameters: typing.Optional[PresetRetrievalParams].

            - eval_parameters: typing.Optional[EvalExecutionParams].

            - llama_parse_parameters: typing.Optional[LlamaParseParameters].

            - status: typing.Optional[str].

            - metadata_config: typing.Optional[PipelineMetadataConfig].

            - name: typing.Optional[str].

            - managed_pipeline_id: typing.Optional[str].
        """
        _request: typing.Dict[str, typing.Any] = {}
        if embedding_config is not OMIT:
            _request["embedding_config"] = embedding_config
        if transform_config is not OMIT:
            _request["transform_config"] = transform_config
        if data_sink_id is not OMIT:
            _request["data_sink_id"] = data_sink_id
        if embedding_model_config_id is not OMIT:
            _request["embedding_model_config_id"] = embedding_model_config_id
        if data_sink is not OMIT:
            _request["data_sink"] = data_sink
        if preset_retrieval_parameters is not OMIT:
            _request["preset_retrieval_parameters"] = preset_retrieval_parameters
        if eval_parameters is not OMIT:
            _request["eval_parameters"] = eval_parameters
        if llama_parse_parameters is not OMIT:
            _request["llama_parse_parameters"] = llama_parse_parameters
        if status is not OMIT:
            _request["status"] = status
        if metadata_config is not OMIT:
            _request["metadata_config"] = metadata_config
        if name is not OMIT:
            _request["name"] = name
        if managed_pipeline_id is not OMIT:
            _request["managed_pipeline_id"] = managed_pipeline_id
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_pipeline(self, pipeline_id: str) -> None:
        """
        Delete a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.delete_pipeline(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_status(self, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Get the status of a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_status(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/status"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def sync_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Run ingestion for the pipeline by incrementally updating the data-sink with upstream changes from data-sources & files.

        Parameters:
            - pipeline_id: str.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/sync"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def cancel_pipeline_sync(self, pipeline_id: str) -> Pipeline:
        """
        Parameters:
            - pipeline_id: str.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/sync/cancel"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def force_delete_pipeline(self, pipeline_id: str) -> None:
        """
        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.force_delete_pipeline(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/force-delete"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def copy_pipeline(self, pipeline_id: str) -> Pipeline:
        """
        Copy a pipeline by ID.

        Parameters:
            - pipeline_id: str.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/copy"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_files(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
    ) -> typing.List[PipelineFile]:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_files(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files"),
            params=remove_none_from_dict(
                {"data_source_id": data_source_id, "only_manually_uploaded": only_manually_uploaded}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineFile], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_files_to_pipeline_api(
        self, pipeline_id: str, *, request: typing.List[PipelineFileCreate]
    ) -> typing.List[PipelineFile]:
        """
        Add files to a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[PipelineFileCreate].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.add_files_to_pipeline_api(
            pipeline_id="string",
            request=[],
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files"),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineFile], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_files_2(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
        limit: typing.Optional[int] = None,
        offset: typing.Optional[int] = None,
        order_by: typing.Optional[str] = None,
    ) -> PaginatedListPipelineFilesResponse:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].

            - limit: typing.Optional[int].

            - offset: typing.Optional[int].

            - order_by: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_files_2(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files2"),
            params=remove_none_from_dict(
                {
                    "data_source_id": data_source_id,
                    "only_manually_uploaded": only_manually_uploaded,
                    "limit": limit,
                    "offset": offset,
                    "order_by": order_by,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedListPipelineFilesResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_file_status_counts(
        self,
        pipeline_id: str,
        *,
        data_source_id: typing.Optional[str] = None,
        only_manually_uploaded: typing.Optional[bool] = None,
    ) -> FileCountByStatusResponse:
        """
        Get files for a pipeline.

        Parameters:
            - pipeline_id: str.

            - data_source_id: typing.Optional[str].

            - only_manually_uploaded: typing.Optional[bool].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_file_status_counts(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/status-counts"
            ),
            params=remove_none_from_dict(
                {"data_source_id": data_source_id, "only_manually_uploaded": only_manually_uploaded}
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(FileCountByStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_file_status(self, file_id: str, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Get status of a file for a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_file_status(
            file_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}/status"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_pipeline_file(
        self,
        file_id: str,
        pipeline_id: str,
        *,
        custom_metadata: typing.Optional[
            typing.Dict[str, typing.Optional[PipelineFileUpdateCustomMetadataValue]]
        ] = OMIT,
    ) -> PipelineFile:
        """
        Update a file for a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.

            - custom_metadata: typing.Optional[typing.Dict[str, typing.Optional[PipelineFileUpdateCustomMetadataValue]]].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.update_pipeline_file(
            file_id="string",
            pipeline_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if custom_metadata is not OMIT:
            _request["custom_metadata"] = custom_metadata
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}"
            ),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineFile, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_pipeline_file(self, file_id: str, pipeline_id: str) -> None:
        """
        Delete a file from a pipeline.

        Parameters:
            - file_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.delete_pipeline_file(
            file_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/files/{file_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def import_pipeline_metadata(self, pipeline_id: str, *, upload_file: typing.IO) -> typing.Dict[str, str]:
        """
        Import metadata for a pipeline.

        Parameters:
            - pipeline_id: str.

            - upload_file: typing.IO.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/metadata"),
            data=jsonable_encoder({}),
            files={"upload_file": upload_file},
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Dict[str, str], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_pipeline_files_metadata(self, pipeline_id: str) -> None:
        """
        Delete metadata for all files in a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.delete_pipeline_files_metadata(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/metadata"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_data_sources(self, pipeline_id: str) -> typing.List[PipelineDataSource]:
        """
        Get data sources for a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_data_sources(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/data-sources"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDataSource], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def add_data_sources_to_pipeline(
        self, pipeline_id: str, *, request: typing.List[PipelineDataSourceCreate]
    ) -> typing.List[PipelineDataSource]:
        """
        Add data sources to a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[PipelineDataSourceCreate].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.add_data_sources_to_pipeline(
            pipeline_id="string",
            request=[],
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/data-sources"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDataSource], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_pipeline_data_source(
        self, data_source_id: str, pipeline_id: str, *, sync_interval: typing.Optional[float] = OMIT
    ) -> PipelineDataSource:
        """
        Update the configuration of a data source in a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.

            - sync_interval: typing.Optional[float].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.update_pipeline_data_source(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if sync_interval is not OMIT:
            _request["sync_interval"] = sync_interval
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
            ),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineDataSource, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_pipeline_data_source(self, data_source_id: str, pipeline_id: str) -> None:
        """
        Delete a data source from a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.delete_pipeline_data_source(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def sync_pipeline_data_source(self, data_source_id: str, pipeline_id: str) -> Pipeline:
        """
        Run ingestion for the pipeline data source by incrementally updating the data-sink with upstream changes from data-source.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/sync",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(Pipeline, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_data_source_status(
        self, data_source_id: str, pipeline_id: str
    ) -> ManagedIngestionStatusResponse:
        """
        Get the status of a data source for a pipeline.

        Parameters:
            - data_source_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_data_source_status(
            data_source_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/data-sources/{data_source_id}/status",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def run_search(
        self,
        pipeline_id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        dense_similarity_top_k: typing.Optional[int] = OMIT,
        dense_similarity_cutoff: typing.Optional[float] = OMIT,
        sparse_similarity_top_k: typing.Optional[int] = OMIT,
        enable_reranking: typing.Optional[bool] = OMIT,
        rerank_top_n: typing.Optional[int] = OMIT,
        alpha: typing.Optional[float] = OMIT,
        search_filters: typing.Optional[MetadataFilters] = OMIT,
        search_filters_inference_schema: typing.Optional[
            typing.Dict[str, typing.Optional[RetrievalParamsSearchFiltersInferenceSchemaValue]]
        ] = OMIT,
        files_top_k: typing.Optional[int] = OMIT,
        retrieval_mode: typing.Optional[RetrievalMode] = OMIT,
        retrieve_image_nodes: typing.Optional[bool] = OMIT,
        retrieve_page_screenshot_nodes: typing.Optional[bool] = OMIT,
        retrieve_page_figure_nodes: typing.Optional[bool] = OMIT,
        query: str,
        class_name: typing.Optional[str] = OMIT,
    ) -> RetrieveResults:
        """
        Get retrieval results for a managed pipeline and a query

        Parameters:
            - pipeline_id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - dense_similarity_top_k: typing.Optional[int].

            - dense_similarity_cutoff: typing.Optional[float].

            - sparse_similarity_top_k: typing.Optional[int].

            - enable_reranking: typing.Optional[bool].

            - rerank_top_n: typing.Optional[int].

            - alpha: typing.Optional[float].

            - search_filters: typing.Optional[MetadataFilters].

            - search_filters_inference_schema: typing.Optional[typing.Dict[str, typing.Optional[RetrievalParamsSearchFiltersInferenceSchemaValue]]].

            - files_top_k: typing.Optional[int].

            - retrieval_mode: typing.Optional[RetrievalMode]. The retrieval mode for the query.

            - retrieve_image_nodes: typing.Optional[bool]. Whether to retrieve image nodes.

            - retrieve_page_screenshot_nodes: typing.Optional[bool]. Whether to retrieve page screenshot nodes.

            - retrieve_page_figure_nodes: typing.Optional[bool]. Whether to retrieve page figure nodes.

            - query: str. The query to retrieve against.

            - class_name: typing.Optional[str].
        ---
        from llama_cloud import FilterCondition, MetadataFilters, RetrievalMode
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.run_search(
            pipeline_id="string",
            search_filters=MetadataFilters(
                filters=[],
                condition=FilterCondition.AND,
            ),
            retrieval_mode=RetrievalMode.CHUNKS,
            query="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {"query": query}
        if dense_similarity_top_k is not OMIT:
            _request["dense_similarity_top_k"] = dense_similarity_top_k
        if dense_similarity_cutoff is not OMIT:
            _request["dense_similarity_cutoff"] = dense_similarity_cutoff
        if sparse_similarity_top_k is not OMIT:
            _request["sparse_similarity_top_k"] = sparse_similarity_top_k
        if enable_reranking is not OMIT:
            _request["enable_reranking"] = enable_reranking
        if rerank_top_n is not OMIT:
            _request["rerank_top_n"] = rerank_top_n
        if alpha is not OMIT:
            _request["alpha"] = alpha
        if search_filters is not OMIT:
            _request["search_filters"] = search_filters
        if search_filters_inference_schema is not OMIT:
            _request["search_filters_inference_schema"] = search_filters_inference_schema
        if files_top_k is not OMIT:
            _request["files_top_k"] = files_top_k
        if retrieval_mode is not OMIT:
            _request["retrieval_mode"] = retrieval_mode
        if retrieve_image_nodes is not OMIT:
            _request["retrieve_image_nodes"] = retrieve_image_nodes
        if retrieve_page_screenshot_nodes is not OMIT:
            _request["retrieve_page_screenshot_nodes"] = retrieve_page_screenshot_nodes
        if retrieve_page_figure_nodes is not OMIT:
            _request["retrieve_page_figure_nodes"] = retrieve_page_figure_nodes
        if class_name is not OMIT:
            _request["class_name"] = class_name
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/retrieve"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(RetrieveResults, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_jobs(self, pipeline_id: str) -> typing.List[PipelineDeployment]:
        """
        Get jobs for a pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_jobs(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/jobs"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[PipelineDeployment], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_job(self, job_id: str, pipeline_id: str) -> PipelineDeployment:
        """
        Get a job for a pipeline.

        Parameters:
            - job_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_job(
            job_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/jobs/{job_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PipelineDeployment, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_playground_session(self, pipeline_id: str) -> PlaygroundSession:
        """
        Get a playground session for a user and pipeline.

        Parameters:
            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_playground_session(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/playground-session"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PlaygroundSession, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def chat(
        self,
        pipeline_id: str,
        *,
        messages: typing.Optional[typing.List[InputMessage]] = OMIT,
        data: typing.Optional[ChatData] = OMIT,
        class_name: typing.Optional[str] = OMIT,
    ) -> typing.Any:
        """
        Make a retrieval query + chat completion for a managed pipeline.

        Parameters:
            - pipeline_id: str.

            - messages: typing.Optional[typing.List[InputMessage]].

            - data: typing.Optional[ChatData].

            - class_name: typing.Optional[str].
        ---
        from llama_cloud import (
            ChatData,
            FilterCondition,
            LlmParameters,
            MetadataFilters,
            PresetRetrievalParams,
            RetrievalMode,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.chat(
            pipeline_id="string",
            data=ChatData(
                retrieval_parameters=PresetRetrievalParams(
                    search_filters=MetadataFilters(
                        filters=[],
                        condition=FilterCondition.AND,
                    ),
                    retrieval_mode=RetrievalMode.CHUNKS,
                ),
                llm_parameters=LlmParameters(
                    model_name=SupportedLlmModelNames.GPT_4_O,
                ),
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if messages is not OMIT:
            _request["messages"] = messages
        if data is not OMIT:
            _request["data"] = data
        if class_name is not OMIT:
            _request["class_name"] = class_name
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/chat"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_documents(
        self,
        pipeline_id: str,
        *,
        skip: typing.Optional[int] = None,
        limit: typing.Optional[int] = None,
        file_id: typing.Optional[str] = None,
        only_direct_upload: typing.Optional[bool] = None,
        only_api_data_source_documents: typing.Optional[bool] = None,
    ) -> typing.List[CloudDocument]:
        """
        Return a list of documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].

            - file_id: typing.Optional[str].

            - only_direct_upload: typing.Optional[bool].

            - only_api_data_source_documents: typing.Optional[bool].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_documents(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            params=remove_none_from_dict(
                {
                    "skip": skip,
                    "limit": limit,
                    "file_id": file_id,
                    "only_direct_upload": only_direct_upload,
                    "only_api_data_source_documents": only_api_data_source_documents,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_batch_pipeline_documents(
        self, pipeline_id: str, *, request: typing.List[CloudDocumentCreate]
    ) -> typing.List[CloudDocument]:
        """
        Batch create documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[CloudDocumentCreate].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.create_batch_pipeline_documents(
            pipeline_id="string",
            request=[],
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def upsert_batch_pipeline_documents(
        self, pipeline_id: str, *, request: typing.List[CloudDocumentCreate]
    ) -> typing.List[CloudDocument]:
        """
        Batch create or update a document for a pipeline.

        Parameters:
            - pipeline_id: str.

            - request: typing.List[CloudDocumentCreate].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.upsert_batch_pipeline_documents(
            pipeline_id="string",
            request=[],
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents"
            ),
            json=jsonable_encoder(request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[CloudDocument], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def paginated_list_pipeline_documents(
        self,
        pipeline_id: str,
        *,
        skip: typing.Optional[int] = None,
        limit: typing.Optional[int] = None,
        file_id: typing.Optional[str] = None,
        only_direct_upload: typing.Optional[bool] = None,
        only_api_data_source_documents: typing.Optional[bool] = None,
    ) -> PaginatedListCloudDocumentsResponse:
        """
        Return a list of documents for a pipeline.

        Parameters:
            - pipeline_id: str.

            - skip: typing.Optional[int].

            - limit: typing.Optional[int].

            - file_id: typing.Optional[str].

            - only_direct_upload: typing.Optional[bool].

            - only_api_data_source_documents: typing.Optional[bool].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.paginated_list_pipeline_documents(
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/paginated"
            ),
            params=remove_none_from_dict(
                {
                    "skip": skip,
                    "limit": limit,
                    "file_id": file_id,
                    "only_direct_upload": only_direct_upload,
                    "only_api_data_source_documents": only_api_data_source_documents,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(PaginatedListCloudDocumentsResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_document(self, document_id: str, pipeline_id: str) -> CloudDocument:
        """
        Return a single document for a pipeline.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_document(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/{document_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(CloudDocument, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_pipeline_document(self, document_id: str, pipeline_id: str) -> None:
        """
        Delete a document from a pipeline.
        Initiates an async job that will:

        1. Delete vectors from the vector store
        2. Delete the document from MongoDB after vectors are successfully deleted

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.delete_pipeline_document(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/", f"api/v1/pipelines/{pipeline_id}/documents/{document_id}"
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_pipeline_document_status(self, document_id: str, pipeline_id: str) -> ManagedIngestionStatusResponse:
        """
        Return a single document for a pipeline.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.get_pipeline_document_status(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/documents/{document_id}/status",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ManagedIngestionStatusResponse, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def list_pipeline_document_chunks(self, document_id: str, pipeline_id: str) -> typing.List[TextNode]:
        """
        Return a list of chunks for a pipeline document.

        Parameters:
            - document_id: str.

            - pipeline_id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.pipelines.list_pipeline_document_chunks(
            document_id="string",
            pipeline_id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(
                f"{self._client_wrapper.get_base_url()}/",
                f"api/v1/pipelines/{pipeline_id}/documents/{document_id}/chunks",
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[TextNode], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
