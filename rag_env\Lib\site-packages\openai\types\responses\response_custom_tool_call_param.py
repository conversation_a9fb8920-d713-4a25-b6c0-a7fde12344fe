# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing_extensions import Literal, Required, TypedDict

__all__ = ["ResponseCustomToolCallParam"]


class ResponseCustomToolCallParam(TypedDict, total=False):
    call_id: Required[str]
    """An identifier used to map this custom tool call to a tool call output."""

    input: Required[str]
    """The input for the custom tool call generated by the model."""

    name: Required[str]
    """The name of the custom tool being called."""

    type: Required[Literal["custom_tool_call"]]
    """The type of the custom tool call. Always `custom_tool_call`."""

    id: str
    """The unique ID of the custom tool call in the OpenAI platform."""
