# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .base_plan_metronome_plan_type import BasePlanMetronomePlanType
from .base_plan_name import BasePlanName
from .base_plan_plan_frequency import BasePlanPlanFrequency
from .billing_period import BillingPeriod
from .plan_limits import PlanLimits
from .recurring_credit_grant import RecurringCreditGrant

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class BasePlan(pydantic.BaseModel):
    id: typing.Optional[str]
    name: BasePlanName
    metronome_plan_type: BasePlanMetronomePlanType
    metronome_rate_card_alias: typing.Optional[str]
    limits: PlanLimits
    recurring_credits: typing.Optional[typing.List[RecurringCreditGrant]]
    plan_frequency: BasePlanPlanFrequency
    metronome_customer_id: typing.Optional[str]
    starting_on: typing.Optional[dt.datetime]
    ending_before: typing.Optional[dt.datetime]
    current_billing_period: typing.Optional[BillingPeriod]
    is_payment_failed: typing.Optional[bool] = pydantic.Field(
        description="Whether the organization has a failed payment that requires support contact"
    )
    failure_count: typing.Optional[int] = pydantic.Field(
        description="The number of payment failures for this organization"
    )

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
