# This file was auto-generated by <PERSON>rn from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .report_state import ReportState

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ReportMetadata(pydantic.BaseModel):
    """
    Used to update the metadata of a report.
    """

    id: str = pydantic.Field(description="The id of the report")
    name: str = pydantic.Field(description="The name of the report")
    report_metadata: typing.Dict[str, typing.Any] = pydantic.Field(description="The metadata for the report")
    state: ReportState = pydantic.Field(description="The state of the report")
    input_files: typing.Optional[typing.List[str]]
    template_file: typing.Optional[str]
    template_text: typing.Optional[str]
    template_instructions: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
