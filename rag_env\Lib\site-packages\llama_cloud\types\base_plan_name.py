# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class BasePlanName(str, enum.Enum):
    FREE = "free"
    LLAMA_PARSE = "llama_parse"
    ENTERPRISE = "enterprise"
    UNKNOWN = "unknown"
    FREE_CONTRACT = "free_contract"
    PRO = "pro"
    ENTERPRISE_CONTRACT = "enterprise_contract"
    ENTERPRISE_POC = "enterprise_poc"
    FREE_V_1 = "free_v1"
    STARTER_V_1 = "starter_v1"
    PRO_V_1 = "pro_v1"

    def visit(
        self,
        free: typing.Callable[[], T_Result],
        llama_parse: typing.Callable[[], T_Result],
        enterprise: typing.Callable[[], T_Result],
        unknown: typing.Callable[[], T_Result],
        free_contract: typing.Callable[[], T_Result],
        pro: typing.Callable[[], T_Result],
        enterprise_contract: typing.Callable[[], T_Result],
        enterprise_poc: typing.Callable[[], T_Result],
        free_v_1: typing.Callable[[], T_Result],
        starter_v_1: typing.Callable[[], T_Result],
        pro_v_1: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is BasePlanName.FREE:
            return free()
        if self is BasePlanName.LLAMA_PARSE:
            return llama_parse()
        if self is BasePlanName.ENTERPRISE:
            return enterprise()
        if self is BasePlanName.UNKNOWN:
            return unknown()
        if self is BasePlanName.FREE_CONTRACT:
            return free_contract()
        if self is BasePlanName.PRO:
            return pro()
        if self is BasePlanName.ENTERPRISE_CONTRACT:
            return enterprise_contract()
        if self is BasePlanName.ENTERPRISE_POC:
            return enterprise_poc()
        if self is BasePlanName.FREE_V_1:
            return free_v_1()
        if self is BasePlanName.STARTER_V_1:
            return starter_v_1()
        if self is BasePlanName.PRO_V_1:
            return pro_v_1()
