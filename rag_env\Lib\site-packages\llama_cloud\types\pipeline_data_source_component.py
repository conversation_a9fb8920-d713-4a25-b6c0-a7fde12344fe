# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .cloud_az_storage_blob_data_source import CloudAzStorageBlobDataSource
from .cloud_box_data_source import CloudBoxDataSource
from .cloud_confluence_data_source import CloudConfluenceDataSource
from .cloud_jira_data_source import CloudJiraDataSource
from .cloud_notion_page_data_source import CloudNotionPageDataSource
from .cloud_one_drive_data_source import CloudOneDriveDataSource
from .cloud_s_3_data_source import CloudS3DataSource
from .cloud_sharepoint_data_source import CloudSharepointDataSource
from .cloud_slack_data_source import CloudSlackDataSource

PipelineDataSourceComponent = typing.Union[
    typing.Dict[str, typing.Any],
    CloudS3DataSource,
    CloudAzStorageBlobDataSource,
    CloudOneDriveDataSource,
    CloudSharepointDataSource,
    CloudSlackDataSource,
    CloudNotionPageDataSource,
    CloudConfluenceDataSource,
    CloudJiraDataSource,
    CloudBoxDataSource,
]
