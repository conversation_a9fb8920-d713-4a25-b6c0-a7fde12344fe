# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class MessageRole(str, enum.Enum):
    """
    Message role.
    """

    SYSTEM = "system"
    DEVELOPER = "developer"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    TOOL = "tool"
    CHATBOT = "chatbot"
    MODEL = "model"

    def visit(
        self,
        system: typing.Callable[[], T_Result],
        developer: typing.Callable[[], T_Result],
        user: typing.Callable[[], T_Result],
        assistant: typing.Callable[[], T_Result],
        function: typing.Callable[[], T_Result],
        tool: typing.Callable[[], T_Result],
        chatbot: typing.Callable[[], T_Result],
        model: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is MessageRole.SYSTEM:
            return system()
        if self is MessageRole.DEVELOPER:
            return developer()
        if self is MessageRole.USER:
            return user()
        if self is MessageRole.ASSISTANT:
            return assistant()
        if self is MessageRole.FUNCTION:
            return function()
        if self is MessageRole.TOOL:
            return tool()
        if self is MessageRole.CHATBOT:
            return chatbot()
        if self is MessageRole.MODEL:
            return model()
